﻿using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Docpark.ThirdMaster.Services.Models.MongoEntities
{
    public class Receipt_ReceiveEmail_History : BaseEntity
    {
        /// <summary>
        /// 发件箱
        /// </summary>
        public string From { get; set; }
        /// <summary>
        /// 发件人名称
        /// </summary>
        public string FromName { get; set; }
        /// <summary>
        /// 邮件标题
        /// </summary>
        public string SubTitle { get; set; }
        /// <summary>
        /// 文件数量
        /// </summary>
        public int FileCount { get; set; }
        /// <summary>
        /// 接收时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime ReceiveDate { get; set; }
        /// <summary>
        /// 是否成功接收
        /// </summary>
        public bool Status { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 收集类型 1供应商发票 2客户索赔发票
        /// </summary>
        public int CollectType { get; set; }
    }
}
