﻿using Docpark.ThirdMaster.Services.Utils;
using DocPark.Workflow.Share;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.Backgroundjob.PaperlessOffice
{
    [MasterServiceType("PaperlessOffice")]
    public class PaperlessOfficeTestJob : BackgroundService, IThirdMasterBackgroundJob
    {
        private readonly IConfiguration configuration;


        private string configXml = "paperlessOfficeTestJob.xml";

        public PaperlessOfficeTestJob()
        {

        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var timeInterval = EncryptionHelper.GetDataXMLByName(configXml, "TimeInterval"); ;
            Console.WriteLine("PaperlessOffice：" + timeInterval);
            await Task.Delay(TimeSpan.FromMinutes(int.Parse(timeInterval)), stoppingToken);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                   Console.WriteLine($"测试任务执行中...");
                }
                catch (TaskCanceledException)
                {
                    // When the stopping token is canceled, for example, a call made from services.msc,
                    // we shouldn't exit with a non-zero exit code. In other words, this is expected...
                }
                catch (Exception ex)
                {
                   
                    Console.WriteLine($"异常：" + ex.Message);
                }
                await Task.Delay(TimeSpan.FromMinutes(int.Parse(timeInterval)), stoppingToken);
            }
        }
    }
}
