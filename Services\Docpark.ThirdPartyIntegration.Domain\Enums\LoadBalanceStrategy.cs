namespace Docpark.ThirdPartyIntegration.Domain.Enums
{
    /// <summary>
    /// 负载均衡策略枚举
    /// </summary>
    public enum LoadBalanceStrategy
    {
        /// <summary>
        /// 轮询
        /// </summary>
        RoundRobin = 1,

        /// <summary>
        /// 加权轮询
        /// </summary>
        WeightedRoundRobin = 2,

        /// <summary>
        /// 最少连接
        /// </summary>
        LeastConnections = 3,

        /// <summary>
        /// 加权最少连接
        /// </summary>
        WeightedLeastConnections = 4,

        /// <summary>
        /// 随机
        /// </summary>
        Random = 5,

        /// <summary>
        /// 加权随机
        /// </summary>
        WeightedRandom = 6,

        /// <summary>
        /// IP哈希
        /// </summary>
        IpHash = 7,

        /// <summary>
        /// 最快响应
        /// </summary>
        FastestResponse = 8
    }
}
