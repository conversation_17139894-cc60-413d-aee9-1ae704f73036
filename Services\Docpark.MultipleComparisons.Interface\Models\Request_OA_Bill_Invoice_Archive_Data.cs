﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_OA_Bill_Invoice_Archive_Data
    {
        /// <summary>
        /// 发票类型<br/>
        /// VAT_PAPER: 增值税普通发票<br/>
        /// VAT_ELECTRONIC: 增值税电子普通发票<br/>
        /// SPECIAL_VAT_PAPER: 增值税专用发票<br/>
        /// SPECIAL_VAT_MOTOR_VEHICLE: 增值税机动车专用发票<br/>
        /// VAT_TOLL: 增值税过路费发票<br/>
        /// SPECIAL_VAT_SECOND_HAND_CAR: 增值税二手车专用发票<br/>
        /// VAT_PAPER_ROLL: 增值税卷票<br/>
        /// VAT_FLIGHT: 飞机票<br/>
        /// VAT_TRAIN: 火车票<br/>
        /// VAT_TAXI: 出租车票<br/>
        /// VAT_PASSAGE: 过路费发票<br/>
        /// VAT_BUS: 客运汽车票<br/>
        /// BLOCK: 区块链发票<br/>
        /// QUOTA_PAPER: 定额发票<br/>
        /// SHIP_PAPER: 船票<br/>
        /// MACHINE_PAPER: 机打发票<br/>
        /// MACHINE_PAPER_ELECTRONIC: 机打电子发票<br/>
        /// OTHER: 其他<br/>
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNumber { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string issueDate { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        public string totalPriceAmount { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string totalTaxAmount { get; set; }
        /// <summary>
        /// 发票详情
        /// </summary>
        public List<MC_Invoice_Item> lineItems { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        public string totalPriceAndTax { get; set; }
        /// <summary>
        /// 验证码
        /// </summary>
        public string checkCode { get; set; }
        /// <summary>
        /// 购方名称
        /// </summary>
        public string buyerName { get; set; }
        /// <summary>
        /// 购方税号
        /// </summary>
        public string buyerTaxNumber { get; set; }
        /// <summary>
        /// 销方名称
        /// </summary>
        public string supplierName { get; set; }
        /// <summary>
        /// 销方税号
        /// </summary>
        public string supplierTaxNumber { get; set; }
        /// <summary>
        /// 凭证号
        /// </summary>
        public List<string> voucherNos { get; set; }
        /// <summary>
        /// 发票图片,可以传以url@文件地址或者base64编码
        /// </summary>
        public List<string> base64 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public MC_Travel_Detail invoiceDetail { get; set; }
    }

}
