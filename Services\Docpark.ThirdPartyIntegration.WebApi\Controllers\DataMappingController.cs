using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 数据映射管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DataMappingController : ControllerBase
    {
        private readonly IDataMappingService _dataMappingService;
        private readonly IApiConfigurationService _apiConfigurationService;
        private readonly ILogger<DataMappingController> _logger;

        public DataMappingController(
            IDataMappingService dataMappingService,
            IApiConfigurationService apiConfigurationService,
            ILogger<DataMappingController> logger)
        {
            _dataMappingService = dataMappingService;
            _apiConfigurationService = apiConfigurationService;
            _logger = logger;
        }

        /// <summary>
        /// 验证映射配置
        /// </summary>
        /// <param name="mappingConfig">映射配置</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate")]
        public async Task<IActionResult> ValidateMappingConfig([FromBody] DataMappingConfig mappingConfig)
        {
            try
            {
                var result = await _dataMappingService.ValidateMappingConfigAsync(mappingConfig);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证映射配置失败");
                return StatusCode(500, new { message = "验证映射配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 生成映射预览
        /// </summary>
        /// <param name="request">预览请求</param>
        /// <returns>映射预览结果</returns>
        [HttpPost("preview")]
        public async Task<IActionResult> GenerateMappingPreview([FromBody] MappingPreviewRequest request)
        {
            try
            {
                if (request?.SampleData == null || request?.MappingConfig == null)
                {
                    return BadRequest(new { message = "示例数据和映射配置不能为空" });
                }

                var result = await _dataMappingService.GenerateMappingPreviewAsync(request.SampleData, request.MappingConfig);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成映射预览失败");
                return StatusCode(500, new { message = "生成映射预览失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 测试数据转换
        /// </summary>
        /// <param name="request">转换测试请求</param>
        /// <returns>转换结果</returns>
        [HttpPost("test-transform")]
        public async Task<IActionResult> TestDataTransform([FromBody] DataTransformTestRequest request)
        {
            try
            {
                if (request?.Value == null)
                {
                    return BadRequest(new { message = "测试值不能为空" });
                }

                var result = await _dataMappingService.TransformDataAsync(
                    request.Value, 
                    request.TransformType, 
                    request.Parameter);

                return Ok(new { 
                    originalValue = request.Value,
                    transformedValue = result,
                    transformType = request.TransformType.ToString(),
                    parameter = request.Parameter
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据转换失败");
                return StatusCode(500, new { message = "测试数据转换失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新API配置的数据映射设置
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="mappingConfig">映射配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("api-config/{apiConfigId}/mapping")]
        public async Task<IActionResult> UpdateApiMappingConfig(string apiConfigId, [FromBody] DataMappingConfig mappingConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                // 获取API配置
                var apiConfig = await _apiConfigurationService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return NotFound(new { message = "API配置不存在" });
                }

                // 验证映射配置
                var validationResult = await _dataMappingService.ValidateMappingConfigAsync(mappingConfig);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { 
                        message = "映射配置验证失败", 
                        errors = validationResult.Errors,
                        warnings = validationResult.Warnings
                    });
                }

                // 更新映射配置
                apiConfig.DataMapping = mappingConfig;
                apiConfig.UpdatedAt = DateTime.UtcNow;

                await _apiConfigurationService.UpdateAsync(apiConfig);

                _logger.LogInformation("API配置 {ApiConfigId} 的数据映射设置已更新", apiConfigId);

                return Ok(new { 
                    message = "数据映射配置更新成功",
                    apiConfigId = apiConfigId,
                    validationResult = validationResult
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新API配置的数据映射设置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "更新数据映射配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取API配置的数据映射设置
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>映射配置</returns>
        [HttpGet("api-config/{apiConfigId}/mapping")]
        public async Task<IActionResult> GetApiMappingConfig(string apiConfigId)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                var apiConfig = await _apiConfigurationService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return NotFound(new { message = "API配置不存在" });
                }

                return Ok(apiConfig.DataMapping ?? new DataMappingConfig());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取API配置的数据映射设置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取数据映射配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取支持的数据转换类型
        /// </summary>
        /// <returns>转换类型列表</returns>
        [HttpGet("transform-types")]
        public IActionResult GetTransformTypes()
        {
            try
            {
                var transformTypes = Enum.GetValues(typeof(DataTransformType))
                    .Cast<DataTransformType>()
                    .Select(t => new
                    {
                        value = (int)t,
                        name = t.ToString(),
                        description = GetTransformTypeDescription(t)
                    })
                    .ToList();

                return Ok(transformTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据转换类型失败");
                return StatusCode(500, new { message = "获取数据转换类型失败", error = ex.Message });
            }
        }

        #region 私有方法

        private string GetTransformTypeDescription(DataTransformType transformType)
        {
            return transformType switch
            {
                DataTransformType.None => "无转换",
                DataTransformType.ToString => "转换为字符串",
                DataTransformType.ToNumber => "转换为数字",
                DataTransformType.ToDateTime => "转换为日期时间",
                DataTransformType.ToBoolean => "转换为布尔值",
                DataTransformType.JsonToObject => "JSON字符串转对象",
                DataTransformType.ObjectToJson => "对象转JSON字符串",
                DataTransformType.RegexExtract => "正则表达式提取",
                DataTransformType.StringReplace => "字符串替换",
                DataTransformType.StringSplit => "字符串分割",
                DataTransformType.ArrayJoin => "数组连接",
                DataTransformType.Custom => "自定义转换",
                _ => "未知转换类型"
            };
        }

        #endregion
    }

    /// <summary>
    /// 映射预览请求
    /// </summary>
    public class MappingPreviewRequest
    {
        /// <summary>
        /// 示例数据
        /// </summary>
        public object SampleData { get; set; }

        /// <summary>
        /// 映射配置
        /// </summary>
        public DataMappingConfig MappingConfig { get; set; }
    }

    /// <summary>
    /// 数据转换测试请求
    /// </summary>
    public class DataTransformTestRequest
    {
        /// <summary>
        /// 测试值
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 转换类型
        /// </summary>
        public DataTransformType TransformType { get; set; }

        /// <summary>
        /// 转换参数
        /// </summary>
        public string Parameter { get; set; }
    }
}
