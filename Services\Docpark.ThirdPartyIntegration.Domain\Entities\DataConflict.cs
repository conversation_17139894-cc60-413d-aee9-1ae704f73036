using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 数据冲突实体
    /// </summary>
    public class DataConflict
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 同步会话ID
        /// </summary>
        [Required]
        public string SyncSessionId { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 数据唯一标识
        /// </summary>
        [Required]
        public string DataIdentifier { get; set; }

        /// <summary>
        /// 冲突类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ConflictType { get; set; }

        /// <summary>
        /// 冲突描述
        /// </summary>
        [StringLength(500)]
        public string ConflictDescription { get; set; }

        /// <summary>
        /// 现有数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument ExistingData { get; set; }

        /// <summary>
        /// 新数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument NewData { get; set; }

        /// <summary>
        /// 合并后的数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument MergedData { get; set; }

        /// <summary>
        /// 冲突解决策略
        /// </summary>
        [Required]
        public ConflictResolutionStrategy ResolutionStrategy { get; set; }

        /// <summary>
        /// 是否已解决
        /// </summary>
        public bool IsResolved { get; set; } = false;

        /// <summary>
        /// 解决时间
        /// </summary>
        public DateTime? ResolvedAt { get; set; }

        /// <summary>
        /// 解决者
        /// </summary>
        [StringLength(100)]
        public string ResolvedBy { get; set; }

        /// <summary>
        /// 解决备注
        /// </summary>
        [StringLength(500)]
        public string ResolutionNotes { get; set; }

        /// <summary>
        /// 优先级（1-5，5为最高）
        /// </summary>
        public int Priority { get; set; } = 3;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
