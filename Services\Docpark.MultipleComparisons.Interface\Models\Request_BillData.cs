﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_BillData
    {
        [Required(ErrorMessage = "票夹id必填")]
        /// <summary>
        /// 票夹id
        /// </summary>
        public string tic_id { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>
        public string totalamount { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string applicant { get; set; }
        /// <summary>
        /// 票夹名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 人员id
        /// </summary>
        public string id { get; set; }
    }

    public class Request_Bill_no
    {
        public string bill_no { get; set; }
    }

    public class Request_Bill_nos
    {
        public string[] bill_nos { get; set; }
    }
}
