﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.IO;

namespace DocPark.Commons
{
    public static class ConfigHelper
    {
        private static IConfiguration _configuration;

        public static IConfiguration Configuration
        {
            get
            {
                if (_configuration != null)
                    return _configuration;
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json")
                    .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
                    ;
                _configuration = builder.Build();
                return _configuration;
            }
            set
            {
                _configuration = value;
            }
        }

        public static IServiceCollection SetConfiguration(this IServiceCollection service, IConfiguration configuration)
        {
            _configuration = configuration;
            return service;
        }

        public static void AddConfigHelper(this IServiceCollection services, IConfiguration configuration)
        {
            _configuration = configuration;
        }
    }
}
