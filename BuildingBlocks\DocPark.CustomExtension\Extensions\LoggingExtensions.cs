﻿using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace DocPark.CustomExtension.Extensions
{
    public static class LoggingExtensions
    {
        public static IHostBuilder UseSerilog(this IHostBuilder builder, Action<LoggerConfiguration> configAction = default)
        {
            builder.UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                // 加载配置文件
                var config = loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration)
                    .Enrich.FromLogContext();

                if ( configAction != null ) configAction.Invoke(config);
                else
                {
                    config.WriteTo.Console(
                            outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                          .WriteTo.File(Path.Combine("logs", "log.txt"), rollingInterval: RollingInterval.Day, rollOnFileSizeLimit: true);
                }
            });
            return builder;
        }

        public static IHostBuilder UseSerilogWithElasticsearch(this IHostBuilder builder, Action<LoggerConfiguration> configAction = default)
        {
            builder.UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                var elkServerUrl = hostingContext.Configuration["Serilog:elkServerUrl"];
                var userName = hostingContext.Configuration["Serilog:userName"];
                var password = hostingContext.Configuration["Serilog:password"];
                var indexFormat = hostingContext.Configuration["Serilog:indexFormat"]==null?"docpark-logs": hostingContext.Configuration["Serilog:indexFormat"];

                // 加载配置文件
                var config = loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration)
                    .Enrich.FromLogContext();

                if ( configAction != null ) configAction.Invoke(config);
                else
                {
                    Func<Elasticsearch.Net.ConnectionConfiguration, Elasticsearch.Net.ConnectionConfiguration> p = (c) => c.BasicAuthentication(userName, password);

                    config.MinimumLevel.Verbose()
                    .Enrich.FromLogContext()
                    .WriteTo.Console()
                    .WriteTo.Elasticsearch(string.IsNullOrEmpty(elkServerUrl) ? null : new ElasticsearchSinkOptions(new Uri(elkServerUrl))
                    {
                        MinimumLogEventLevel = LogEventLevel.Verbose,
                        AutoRegisterTemplate = true,
                        IndexFormat = $"{indexFormat}-{DateTime.UtcNow:yyyy.MM.dd}",
                        ModifyConnectionSettings = string.IsNullOrEmpty(userName) ? null : p,
                    })
                    .ReadFrom.Configuration(hostingContext.Configuration);
                }
            });
            return builder;
        }
    }
}
