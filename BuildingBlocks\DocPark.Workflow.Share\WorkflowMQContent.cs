﻿using System;
using System.Collections.Generic;
using System.Text;

namespace DocPark.Workflow.Share
{
    /// <summary>
    /// 工作流消息队列的消息内容
    /// </summary>
    public class WorkflowMQContent
    {
        /// <summary>
        /// 请求ID,发布消息时创建
        /// </summary>
        public string? RequestId { get; set; }
        /// <summary>
        /// 执行代码(工作流状态Code)
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 执行名称(工作流状态Name)
        /// </summary>
        public string? Name { get; set; }
        /// <summary>
        /// 文档ID
        /// </summary>
        public string? DocumentID { get; set; }
        /// <summary>
        /// 文档类型
        /// </summary>
        public string? DocumentType { get; set; }

        public string WorkflowId { get; set; }
        /// <summary>
        /// 消息类型(作用: 提示或执行方式)<br/>
        /// 示例：如, 文档工作流订阅消息时, 值为: IMG(图片文件) / PFD / OFD
        /// </summary>
        public string? MessageType { get; set; }
        /// <summary>
        /// 元数据查询关键ID
        /// </summary>
        public string? MstId { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string? BusinessType { get; set; }
        /// <summary>
        /// 文档任务ID
        /// </summary>
        public string? DocumentTaskID { get; set; }
        /// <summary>
        /// 文档任务流转ActionID
        /// </summary>
        public string? DocumentTaskActionID { get; set; }
        /// <summary>
        /// 文档扫描用户(唯一标识)
        /// </summary>
        public string? ScanUser { get; set; }
        /// <summary>
        /// 文档拥有用户(唯一标识)
        /// </summary>
        public string? OwnUser { get; set; }

        /// <summary>
        /// 状态:默认为成功
        /// </summary>
        public bool? TriggerStatus { get; set; } = true;

        /// <summary>
        /// 消息
        /// </summary>
        public string? TriggerMessage { get; set; }

        /// <summary>
        /// 扩展数据
        /// </summary>
        public Dictionary<string, string>? Data { get; set; }
    }
}
