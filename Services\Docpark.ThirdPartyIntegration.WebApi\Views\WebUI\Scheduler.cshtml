@{
    Layout = "_Layout";
}

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 调度器状态 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">调度器状态</h6>
            </div>
            <div class="card-body">
                <div id="schedulerStatus">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">调度器控制</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="startScheduler()">
                        <i class="fas fa-play me-2"></i>启动调度器
                    </button>
                    <button class="btn btn-warning" onclick="pauseScheduler()">
                        <i class="fas fa-pause me-2"></i>暂停调度器
                    </button>
                    <button class="btn btn-danger" onclick="stopScheduler()">
                        <i class="fas fa-stop me-2"></i>停止调度器
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务管理 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">定时任务列表</h6>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createScheduleModal">
                    <i class="fas fa-plus me-2"></i>创建定时任务
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="scheduledJobsTable">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>API配置</th>
                                <th>调度类型</th>
                                <th>调度表达式</th>
                                <th>状态</th>
                                <th>下次执行</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="scheduledJobsTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建定时任务模态框 -->
<div class="modal fade" id="createScheduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建定时任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createScheduleForm">
                    <div class="mb-3">
                        <label for="scheduleApiConfig" class="form-label">选择API配置 *</label>
                        <select class="form-select" id="scheduleApiConfig" required>
                            <option value="">请选择API配置...</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="scheduleType" class="form-label">调度类型 *</label>
                        <select class="form-select" id="scheduleType" onchange="toggleScheduleOptions()" required>
                            <option value="">请选择调度类型...</option>
                            <option value="0">间隔调度</option>
                            <option value="1">每日调度</option>
                            <option value="2">Cron表达式</option>
                        </select>
                    </div>
                    
                    <!-- 间隔调度选项 -->
                    <div id="intervalOptions" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="intervalValue" class="form-label">间隔值</label>
                                    <input type="number" class="form-control" id="intervalValue" min="1" value="30">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="intervalUnit" class="form-label">时间单位</label>
                                    <select class="form-select" id="intervalUnit">
                                        <option value="0">秒</option>
                                        <option value="1" selected>分钟</option>
                                        <option value="2">小时</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 每日调度选项 -->
                    <div id="dailyOptions" style="display: none;">
                        <div class="mb-3">
                            <label for="dailyTime" class="form-label">执行时间</label>
                            <input type="time" class="form-control" id="dailyTime" value="09:00">
                        </div>
                    </div>
                    
                    <!-- Cron表达式选项 -->
                    <div id="cronOptions" style="display: none;">
                        <div class="mb-3">
                            <label for="cronExpression" class="form-label">Cron表达式</label>
                            <input type="text" class="form-control" id="cronExpression" placeholder="例如: 0 0 9 * * ?">
                            <div class="form-text">
                                格式: 秒 分 时 日 月 周<br>
                                示例: 0 0 9 * * ? (每天9点执行)
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="scheduleStartTime" class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control" id="scheduleStartTime">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="scheduleEndTime" class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="scheduleEndTime">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="misfireInstruction" class="form-label">错失触发策略</label>
                                <select class="form-select" id="misfireInstruction">
                                    <option value="0">忽略错失触发</option>
                                    <option value="1">立即执行一次</option>
                                    <option value="2">不执行</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maxRetryCount" class="form-label">最大重试次数</label>
                                <input type="number" class="form-control" id="maxRetryCount" min="0" max="10" value="3">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="scheduleDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="scheduleDescription" rows="3" placeholder="任务描述..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="scheduleEnabled" checked>
                            <label class="form-check-label" for="scheduleEnabled">
                                启用此定时任务
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createSchedule()">创建任务</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadSchedulerStatus();
        loadScheduledJobs();
        loadApiConfigs();
        
        // 每30秒刷新一次状态
        setInterval(loadSchedulerStatus, 30000);
        
        // 检查URL参数中是否有apiId
        const urlParams = new URLSearchParams(window.location.search);
        const apiId = urlParams.get('apiId');
        if (apiId) {
            setTimeout(() => {
                $('#scheduleApiConfig').val(apiId);
                $('#createScheduleModal').modal('show');
            }, 1000);
        }
    });

    function loadSchedulerStatus() {
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                displaySchedulerStatus(data);
            })
            .fail(function() {
                $('#schedulerStatus').html('<div class="text-danger">加载失败</div>');
            });
    }

    function displaySchedulerStatus(data) {
        const html = `
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary">${data.totalJobs || 0}</h4>
                        <small class="text-muted">总任务数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success">${data.runningJobs || 0}</h4>
                        <small class="text-muted">运行中</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">${data.pausedJobs || 0}</h4>
                        <small class="text-muted">已暂停</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${data.todayExecutions || 0}</h4>
                        <small class="text-muted">今日执行</small>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <strong>调度器状态:</strong><br>
                    <span class="badge bg-success">${data.schedulerState || '未知'}</span>
                </div>
                <div class="col-md-6">
                    <strong>启动时间:</strong><br>
                    <span class="text-muted">${formatDateTime(data.startTime)}</span>
                </div>
            </div>
        `;
        $('#schedulerStatus').html(html);
    }

    function loadScheduledJobs() {
        callAPI('/api/Scheduler/jobs')
            .done(function(data) {
                displayScheduledJobs(data);
            })
            .fail(function() {
                $('#scheduledJobsTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>');
            });
    }

    function displayScheduledJobs(jobs) {
        const tbody = $('#scheduledJobsTableBody');
        tbody.empty();
        
        if (jobs.length === 0) {
            tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无定时任务</td></tr>');
            return;
        }
        
        jobs.forEach(function(job) {
            const statusBadge = getJobStatusBadge(job.state);
            const scheduleType = getScheduleTypeName(job.scheduleType);
            
            const row = `
                <tr>
                    <td>${job.name || 'N/A'}</td>
                    <td>${job.apiConfigName || 'N/A'}</td>
                    <td><span class="badge bg-info">${scheduleType}</span></td>
                    <td><code>${job.scheduleExpression || 'N/A'}</code></td>
                    <td>${statusBadge}</td>
                    <td>${formatDateTime(job.nextFireTime)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="triggerJob('${job.id}')" title="立即执行">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="pauseJob('${job.id}')" title="暂停">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="resumeJob('${job.id}')" title="恢复">
                                <i class="fas fa-play-circle"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteJob('${job.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getJobStatusBadge(state) {
        const states = {
            'Normal': '<span class="badge bg-success">正常</span>',
            'Paused': '<span class="badge bg-warning">暂停</span>',
            'Complete': '<span class="badge bg-info">完成</span>',
            'Error': '<span class="badge bg-danger">错误</span>',
            'Blocked': '<span class="badge bg-secondary">阻塞</span>'
        };
        return states[state] || '<span class="badge bg-secondary">未知</span>';
    }

    function getScheduleTypeName(type) {
        const types = {
            0: '间隔',
            1: '每日',
            2: 'Cron'
        };
        return types[type] || '未知';
    }

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                const select = $('#scheduleApiConfig');
                select.empty().append('<option value="">请选择API配置...</option>');
                
                data.forEach(function(config) {
                    select.append(`<option value="${config.id}">${config.name} (${config.method} ${config.url})</option>`);
                });
            })
            .fail(function() {
                showError('加载API配置失败');
            });
    }

    function toggleScheduleOptions() {
        const scheduleType = $('#scheduleType').val();
        
        $('#intervalOptions, #dailyOptions, #cronOptions').hide();
        
        switch(scheduleType) {
            case '0': // 间隔调度
                $('#intervalOptions').show();
                break;
            case '1': // 每日调度
                $('#dailyOptions').show();
                break;
            case '2': // Cron表达式
                $('#cronOptions').show();
                break;
        }
    }

    function createSchedule() {
        const apiConfigId = $('#scheduleApiConfig').val();
        const scheduleType = parseInt($('#scheduleType').val());
        
        if (!apiConfigId || isNaN(scheduleType)) {
            showError('请选择API配置和调度类型');
            return;
        }
        
        let scheduleConfig = {
            scheduleType: scheduleType,
            startTime: $('#scheduleStartTime').val() ? new Date($('#scheduleStartTime').val()).toISOString() : null,
            endTime: $('#scheduleEndTime').val() ? new Date($('#scheduleEndTime').val()).toISOString() : null,
            misfireInstruction: parseInt($('#misfireInstruction').val()),
            maxRetryCount: parseInt($('#maxRetryCount').val()),
            description: $('#scheduleDescription').val(),
            isEnabled: $('#scheduleEnabled').is(':checked')
        };
        
        // 根据调度类型设置特定参数
        switch(scheduleType) {
            case 0: // 间隔调度
                scheduleConfig.intervalValue = parseInt($('#intervalValue').val());
                scheduleConfig.intervalUnit = parseInt($('#intervalUnit').val());
                break;
            case 1: // 每日调度
                scheduleConfig.dailyTime = $('#dailyTime').val();
                break;
            case 2: // Cron表达式
                scheduleConfig.cronExpression = $('#cronExpression').val();
                if (!scheduleConfig.cronExpression) {
                    showError('请输入Cron表达式');
                    return;
                }
                break;
        }
        
        callAPI('/api/Scheduler/schedule/' + apiConfigId, 'POST', scheduleConfig)
            .done(function() {
                showSuccess('定时任务创建成功');
                $('#createScheduleModal').modal('hide');
                $('#createScheduleForm')[0].reset();
                loadScheduledJobs();
                loadSchedulerStatus();
            })
            .fail(function(xhr) {
                const message = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showError('创建定时任务失败: ' + message);
            });
    }

    function startScheduler() {
        callAPI('/api/Scheduler/start', 'POST')
            .done(function() {
                showSuccess('调度器启动成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器启动失败');
            });
    }

    function pauseScheduler() {
        callAPI('/api/Scheduler/pause', 'POST')
            .done(function() {
                showSuccess('调度器暂停成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器暂停失败');
            });
    }

    function stopScheduler() {
        if (!confirm('确定要停止调度器吗？这将停止所有定时任务。')) {
            return;
        }
        
        callAPI('/api/Scheduler/stop', 'POST')
            .done(function() {
                showSuccess('调度器停止成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器停止失败');
            });
    }

    function triggerJob(jobId) {
        callAPI('/api/Scheduler/trigger/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务触发成功');
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务触发失败');
            });
    }

    function pauseJob(jobId) {
        callAPI('/api/Scheduler/pause/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务暂停成功');
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务暂停失败');
            });
    }

    function resumeJob(jobId) {
        callAPI('/api/Scheduler/resume/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务恢复成功');
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务恢复失败');
            });
    }

    function deleteJob(jobId) {
        if (!confirm('确定要删除这个定时任务吗？此操作不可恢复。')) {
            return;
        }
        
        callAPI('/api/Scheduler/delete/' + jobId, 'DELETE')
            .done(function() {
                showSuccess('任务删除成功');
                loadScheduledJobs();
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('任务删除失败');
            });
    }
</script>
}

<style>
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>
