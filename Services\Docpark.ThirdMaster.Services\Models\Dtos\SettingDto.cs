﻿using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class SettingDto
    {
        public string ProcessName { get; set; }
        public string Step { get; set; }
        public string BussinessType { get; set; }
        public string DocTypeId { get; set; }
        public List<MappingDto> Mappings { get; set; } = new List<MappingDto>();
    }
    public class MappingDto
    {
        public string SField { get; set; }
        public string JField { get; set; }
    }
}
