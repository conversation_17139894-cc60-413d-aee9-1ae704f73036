﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    /// <summary>
    /// 发票验真请求消息内容      参数：发票类型, 发票代码, 发票号码, 开票日期, 校验码, 车价合计, 企业名称
    /// </summary>
    public class Model_NuonuoInvoiceInspectionContent
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }
        /// <summary>
        /// 开票日期 （格式:yyyy-MM-dd）
        /// </summary>
        public string invoiceDate { get; set; }
        /// <summary>
        /// 1. 专用发票/机动车发票：不含税金额 增值税普通发票/普通发票（电子）/普通发票（卷票）/普通发票（通行费）：校验码（后6位）<br/>
        /// 2. 二手车发票：车价合计
        /// </summary>
        public string optionField { get; set; }

    }
}
