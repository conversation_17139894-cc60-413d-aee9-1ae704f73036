using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 熔断器配置实体
    /// </summary>
    public class CircuitBreakerConfig
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 当前状态
        /// </summary>
        public CircuitBreakerState CurrentState { get; set; } = CircuitBreakerState.Closed;

        /// <summary>
        /// 失败阈值
        /// </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary>
        /// 成功阈值（半开状态下）
        /// </summary>
        public int SuccessThreshold { get; set; } = 3;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 熔断持续时间（秒）
        /// </summary>
        public int OpenDurationSeconds { get; set; } = 60;

        /// <summary>
        /// 半开状态最大请求数
        /// </summary>
        public int HalfOpenMaxRequests { get; set; } = 10;

        /// <summary>
        /// 统计时间窗口（秒）
        /// </summary>
        public int StatisticsWindowSeconds { get; set; } = 60;

        /// <summary>
        /// 最小请求数（触发熔断的最小请求数）
        /// </summary>
        public int MinimumThroughput { get; set; } = 10;

        /// <summary>
        /// 错误率阈值（百分比）
        /// </summary>
        public double ErrorRateThreshold { get; set; } = 50.0;

        /// <summary>
        /// 慢调用阈值（毫秒）
        /// </summary>
        public int SlowCallThresholdMs { get; set; } = 10000;

        /// <summary>
        /// 慢调用率阈值（百分比）
        /// </summary>
        public double SlowCallRateThreshold { get; set; } = 50.0;

        /// <summary>
        /// 降级响应配置（JSON格式）
        /// </summary>
        public string FallbackConfig { get; set; }

        /// <summary>
        /// 最后状态变更时间
        /// </summary>
        public DateTime? LastStateChangeTime { get; set; }

        /// <summary>
        /// 失败计数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 成功计数
        /// </summary>
        public int SuccessCount { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }
    }
}
