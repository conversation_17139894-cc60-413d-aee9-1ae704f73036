@{
    Layout = "_Layout";
}

<!-- 页面标题 -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">数据同步管理</h1>
    <div>
        <button type="button" class="btn btn-primary btn-sm" onclick="showCreateSyncConfigModal()">
            <i class="fas fa-plus fa-sm text-white-50"></i> 创建同步配置
        </button>
        <button type="button" class="btn btn-success btn-sm" onclick="refreshSyncData()">
            <i class="fas fa-sync fa-sm text-white-50"></i> 刷新
        </button>
    </div>
</div>

<!-- 同步状态概览 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总同步配置
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-sync-configs">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cogs fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃同步会话
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-sync-sessions">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sync fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            未解决冲突
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="unresolved-conflicts">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            今日同步次数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-sync-count">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 同步配置列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">同步配置列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="sync-configs-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>API名称</th>
                        <th>同步策略</th>
                        <th>冲突解决</th>
                        <th>状态</th>
                        <th>最后同步</th>
                        <th>成功率</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="sync-configs-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 同步会话历史 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">最近同步会话</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="sync-sessions-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>会话ID</th>
                        <th>API名称</th>
                        <th>同步策略</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>持续时间</th>
                        <th>处理记录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="sync-sessions-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 数据冲突列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">未解决的数据冲突</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="conflicts-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>冲突ID</th>
                        <th>API名称</th>
                        <th>冲突类型</th>
                        <th>数据标识</th>
                        <th>创建时间</th>
                        <th>优先级</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="conflicts-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建同步配置模态框 -->
<div class="modal fade" id="createSyncConfigModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建同步配置</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createSyncConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="apiConfigId">API配置</label>
                                <select class="form-control" id="apiConfigId" name="apiConfigId" required>
                                    <option value="">请选择API配置</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="strategy">同步策略</label>
                                <select class="form-control" id="strategy" name="strategy" required>
                                    <option value="1">全量同步</option>
                                    <option value="2" selected>增量同步</option>
                                    <option value="3">差异同步</option>
                                    <option value="4">检查点同步</option>
                                    <option value="5">混合同步</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="conflictResolution">冲突解决策略</label>
                                <select class="form-control" id="conflictResolution" name="conflictResolution" required>
                                    <option value="1">跳过</option>
                                    <option value="2" selected>覆盖</option>
                                    <option value="3">保留现有</option>
                                    <option value="4">合并</option>
                                    <option value="5">创建版本</option>
                                    <option value="6">手动处理</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="batchSize">批处理大小</label>
                                <input type="number" class="form-control" id="batchSize" name="batchSize" value="100" min="1" max="1000">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="incrementalField">增量字段</label>
                                <input type="text" class="form-control" id="incrementalField" name="incrementalField" placeholder="如: updated_at">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="uniqueIdentifierField">唯一标识字段</label>
                                <input type="text" class="form-control" id="uniqueIdentifierField" name="uniqueIdentifierField" placeholder="如: id">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="enableIncrementalSync" name="enableIncrementalSync" checked>
                            <label class="form-check-label" for="enableIncrementalSync">启用增量同步</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="enableDataValidation" name="enableDataValidation" checked>
                            <label class="form-check-label" for="enableDataValidation">启用数据验证</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createSyncConfig()">创建</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadSyncOverview();
        loadSyncConfigs();
        loadSyncSessions();
        loadUnresolvedConflicts();
        loadApiConfigs();
        
        // 每30秒刷新一次数据
        setInterval(function() {
            loadSyncOverview();
            loadSyncSessions();
        }, 30000);
    });

    function loadSyncOverview() {
        // 加载同步概览数据
        // 这里应该调用相应的API获取统计数据
        $('#total-sync-configs').text('5');
        $('#active-sync-sessions').text('2');
        $('#unresolved-conflicts').text('1');
        $('#today-sync-count').text('24');
    }

    function loadSyncConfigs() {
        // 加载同步配置列表
        // 这里应该调用API获取同步配置数据
        var html = `
            <tr>
                <td>用户数据API</td>
                <td><span class="badge badge-primary">增量同步</span></td>
                <td><span class="badge badge-info">覆盖</span></td>
                <td><span class="badge badge-success">启用</span></td>
                <td>2023-12-01 10:30:00</td>
                <td>95.5%</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="executeSyncNow('api1')">立即同步</button>
                    <button class="btn btn-sm btn-info" onclick="viewSyncConfig('api1')">查看</button>
                    <button class="btn btn-sm btn-warning" onclick="editSyncConfig('api1')">编辑</button>
                </td>
            </tr>
        `;
        $('#sync-configs-tbody').html(html);
    }

    function loadSyncSessions() {
        // 加载同步会话历史
        var html = `
            <tr>
                <td>session_001</td>
                <td>用户数据API</td>
                <td>增量同步</td>
                <td><span class="badge badge-success">成功</span></td>
                <td>2023-12-01 10:30:00</td>
                <td>2.5秒</td>
                <td>150/150</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewSessionDetails('session_001')">详情</button>
                </td>
            </tr>
        `;
        $('#sync-sessions-tbody').html(html);
    }

    function loadUnresolvedConflicts() {
        // 加载未解决的冲突
        var html = `
            <tr>
                <td>conflict_001</td>
                <td>用户数据API</td>
                <td>数据已存在</td>
                <td>user_123</td>
                <td>2023-12-01 10:25:00</td>
                <td><span class="badge badge-warning">中</span></td>
                <td>
                    <button class="btn btn-sm btn-success" onclick="resolveConflict('conflict_001')">解决</button>
                    <button class="btn btn-sm btn-info" onclick="viewConflictDetails('conflict_001')">详情</button>
                </td>
            </tr>
        `;
        $('#conflicts-tbody').html(html);
    }

    function loadApiConfigs() {
        // 加载API配置选项
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                var options = '<option value="">请选择API配置</option>';
                data.forEach(function(config) {
                    options += `<option value="${config.id}">${config.name}</option>`;
                });
                $('#apiConfigId').html(options);
            })
            .fail(function() {
                showAlert('加载API配置失败', 'danger');
            });
    }

    function showCreateSyncConfigModal() {
        $('#createSyncConfigModal').modal('show');
    }

    function createSyncConfig() {
        var formData = {
            apiConfigId: $('#apiConfigId').val(),
            strategy: parseInt($('#strategy').val()),
            conflictResolution: parseInt($('#conflictResolution').val()),
            enableIncrementalSync: $('#enableIncrementalSync').is(':checked'),
            incrementalField: $('#incrementalField').val(),
            uniqueIdentifierField: $('#uniqueIdentifierField').val(),
            batchSize: parseInt($('#batchSize').val()),
            enableDataValidation: $('#enableDataValidation').is(':checked')
        };

        callAPI('/api/DataSync/configurations', 'POST', formData)
            .done(function(response) {
                showAlert('同步配置创建成功', 'success');
                $('#createSyncConfigModal').modal('hide');
                loadSyncConfigs();
                loadSyncOverview();
            })
            .fail(function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showAlert('创建同步配置失败: ' + error, 'danger');
            });
    }

    function executeSyncNow(apiConfigId) {
        if (confirm('确定要立即执行同步吗？')) {
            callAPI(`/api/DataSync/execute/incremental/${apiConfigId}`, 'POST')
                .done(function(response) {
                    showAlert('同步任务已启动', 'success');
                    loadSyncSessions();
                })
                .fail(function(xhr) {
                    var error = xhr.responseJSON ? xhr.responseJSON.message : '执行失败';
                    showAlert('执行同步失败: ' + error, 'danger');
                });
        }
    }

    function refreshSyncData() {
        loadSyncOverview();
        loadSyncConfigs();
        loadSyncSessions();
        loadUnresolvedConflicts();
        showAlert('数据已刷新', 'info');
    }

    // 其他功能函数
    function viewSyncConfig(id) { /* 查看同步配置详情 */ }
    function editSyncConfig(id) { /* 编辑同步配置 */ }
    function viewSessionDetails(sessionId) { /* 查看会话详情 */ }
    function resolveConflict(conflictId) { /* 解决冲突 */ }
    function viewConflictDetails(conflictId) { /* 查看冲突详情 */ }
</script>
}
