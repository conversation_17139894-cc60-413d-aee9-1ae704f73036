﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35004.147
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{B854AC7C-A8C6-4B80-9F86-F6F095C047CD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.MultipleComparisons.Interface", "Services\Docpark.MultipleComparisons.Interface\Docpark.MultipleComparisons.Interface.csproj", "{FE583F4A-2213-4086-A585-37414A4CE619}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BuildingBlocks", "BuildingBlocks", "{F7D11C52-DC5E-485E-9845-3B6B5DC093D4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DocPark.Commons", "BuildingBlocks\DocPark.Commons\DocPark.Commons.csproj", "{E6BDD669-534C-43E9-945E-164B0298D092}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.HttpClientExtension", "BuildingBlocks\Docpark.HttpClientExtension\Docpark.HttpClientExtension.csproj", "{D59CCF61-1268-4539-AF1B-4D1BCF9EA927}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DocPark.CustomExtension", "BuildingBlocks\DocPark.CustomExtension\DocPark.CustomExtension.csproj", "{2102901B-1794-43DD-B6E0-CD66C3EE83A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Devspaces.Support", "BuildingBlocks\Devspaces.Support\Devspaces.Support.csproj", "{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EventBus", "BuildingBlocks\EventBus\EventBus.csproj", "{E9F16AF8-7406-445F-86DC-186AD817AF7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EventBusRabbitMQ", "BuildingBlocks\EventBusRabbitMQ\EventBusRabbitMQ.csproj", "{B0C5373E-D85B-42CA-95FD-035E01A4972B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EventBusServiceBus", "BuildingBlocks\EventBusServiceBus\EventBusServiceBus.csproj", "{55026376-D65F-4A4B-A9F0-E635CAB7BC0E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebHost.Customization", "BuildingBlocks\WebHost.Customization\WebHost.Customization.csproj", "{DCF4662E-EDE8-4B0A-8558-A605FD25CB20}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DocPark.MongoDb", "BuildingBlocks\DocPark.MongoDb\DocPark.MongoDb.csproj", "{D5673EFE-D55E-4A0C-BD55-0EBE73B56567}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DocPark.Workflow.Share", "BuildingBlocks\DocPark.Workflow.Share\DocPark.Workflow.Share.csproj", "{4011CCB7-096D-487C-9FA1-9A4064D37516}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DocPark.EmailService", "BuildingBlocks\DocPark.EmailService\DocPark.EmailService.csproj", "{615E06E0-E9F7-4922-8BBC-C496575B09DB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Docpark.Continental.WebApi", "Services\Docpark.Continental.WebApi\Docpark.Continental.WebApi.csproj", "{6678EB43-C87C-4DD8-9DAE-D093BB434F0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.ThirdMaster.Services", "Services\Docpark.ThirdMaster.Services\Docpark.ThirdMaster.Services.csproj", "{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.ThirdPartyIntegration.Domain", "Services\Docpark.ThirdPartyIntegration.Domain\Docpark.ThirdPartyIntegration.Domain.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.ThirdPartyIntegration.Services", "Services\Docpark.ThirdPartyIntegration.Services\Docpark.ThirdPartyIntegration.Services.csproj", "{74CCBDD6-D62E-4A60-A9E9-146CF2B7D3BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Docpark.ThirdPartyIntegration.WebApi", "Services\Docpark.ThirdPartyIntegration.WebApi\Docpark.ThirdPartyIntegration.WebApi.csproj", "{B9F7AA43-1EEB-4109-82C6-349D56CD55FE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FE583F4A-2213-4086-A585-37414A4CE619}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE583F4A-2213-4086-A585-37414A4CE619}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE583F4A-2213-4086-A585-37414A4CE619}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE583F4A-2213-4086-A585-37414A4CE619}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6BDD669-534C-43E9-945E-164B0298D092}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6BDD669-534C-43E9-945E-164B0298D092}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6BDD669-534C-43E9-945E-164B0298D092}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6BDD669-534C-43E9-945E-164B0298D092}.Release|Any CPU.Build.0 = Release|Any CPU
		{D59CCF61-1268-4539-AF1B-4D1BCF9EA927}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D59CCF61-1268-4539-AF1B-4D1BCF9EA927}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D59CCF61-1268-4539-AF1B-4D1BCF9EA927}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D59CCF61-1268-4539-AF1B-4D1BCF9EA927}.Release|Any CPU.Build.0 = Release|Any CPU
		{2102901B-1794-43DD-B6E0-CD66C3EE83A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2102901B-1794-43DD-B6E0-CD66C3EE83A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2102901B-1794-43DD-B6E0-CD66C3EE83A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2102901B-1794-43DD-B6E0-CD66C3EE83A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9F16AF8-7406-445F-86DC-186AD817AF7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9F16AF8-7406-445F-86DC-186AD817AF7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9F16AF8-7406-445F-86DC-186AD817AF7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9F16AF8-7406-445F-86DC-186AD817AF7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0C5373E-D85B-42CA-95FD-035E01A4972B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0C5373E-D85B-42CA-95FD-035E01A4972B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0C5373E-D85B-42CA-95FD-035E01A4972B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0C5373E-D85B-42CA-95FD-035E01A4972B}.Release|Any CPU.Build.0 = Release|Any CPU
		{55026376-D65F-4A4B-A9F0-E635CAB7BC0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{55026376-D65F-4A4B-A9F0-E635CAB7BC0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{55026376-D65F-4A4B-A9F0-E635CAB7BC0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{55026376-D65F-4A4B-A9F0-E635CAB7BC0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCF4662E-EDE8-4B0A-8558-A605FD25CB20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCF4662E-EDE8-4B0A-8558-A605FD25CB20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCF4662E-EDE8-4B0A-8558-A605FD25CB20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCF4662E-EDE8-4B0A-8558-A605FD25CB20}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5673EFE-D55E-4A0C-BD55-0EBE73B56567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5673EFE-D55E-4A0C-BD55-0EBE73B56567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5673EFE-D55E-4A0C-BD55-0EBE73B56567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5673EFE-D55E-4A0C-BD55-0EBE73B56567}.Release|Any CPU.Build.0 = Release|Any CPU
		{4011CCB7-096D-487C-9FA1-9A4064D37516}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4011CCB7-096D-487C-9FA1-9A4064D37516}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4011CCB7-096D-487C-9FA1-9A4064D37516}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4011CCB7-096D-487C-9FA1-9A4064D37516}.Release|Any CPU.Build.0 = Release|Any CPU
		{615E06E0-E9F7-4922-8BBC-C496575B09DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{615E06E0-E9F7-4922-8BBC-C496575B09DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{615E06E0-E9F7-4922-8BBC-C496575B09DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{615E06E0-E9F7-4922-8BBC-C496575B09DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{6678EB43-C87C-4DD8-9DAE-D093BB434F0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6678EB43-C87C-4DD8-9DAE-D093BB434F0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6678EB43-C87C-4DD8-9DAE-D093BB434F0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6678EB43-C87C-4DD8-9DAE-D093BB434F0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{74CCBDD6-D62E-4A60-A9E9-146CF2B7D3BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74CCBDD6-D62E-4A60-A9E9-146CF2B7D3BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74CCBDD6-D62E-4A60-A9E9-146CF2B7D3BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74CCBDD6-D62E-4A60-A9E9-146CF2B7D3BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9F7AA43-1EEB-4109-82C6-349D56CD55FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9F7AA43-1EEB-4109-82C6-349D56CD55FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9F7AA43-1EEB-4109-82C6-349D56CD55FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9F7AA43-1EEB-4109-82C6-349D56CD55FE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{FE583F4A-2213-4086-A585-37414A4CE619} = {B854AC7C-A8C6-4B80-9F86-F6F095C047CD}
		{E6BDD669-534C-43E9-945E-164B0298D092} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{D59CCF61-1268-4539-AF1B-4D1BCF9EA927} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{2102901B-1794-43DD-B6E0-CD66C3EE83A5} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{E2951DF1-6D9B-4D2C-BBEE-8A80D0730431} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{E9F16AF8-7406-445F-86DC-186AD817AF7D} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{B0C5373E-D85B-42CA-95FD-035E01A4972B} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{55026376-D65F-4A4B-A9F0-E635CAB7BC0E} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{DCF4662E-EDE8-4B0A-8558-A605FD25CB20} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{D5673EFE-D55E-4A0C-BD55-0EBE73B56567} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{4011CCB7-096D-487C-9FA1-9A4064D37516} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{615E06E0-E9F7-4922-8BBC-C496575B09DB} = {F7D11C52-DC5E-485E-9845-3B6B5DC093D4}
		{6678EB43-C87C-4DD8-9DAE-D093BB434F0A} = {B854AC7C-A8C6-4B80-9F86-F6F095C047CD}
		{8ECC34CC-044B-4F28-B6B2-6125ADF0DF54} = {B854AC7C-A8C6-4B80-9F86-F6F095C047CD}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {B854AC7C-A8C6-4B80-9F86-F6F095C047CD}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {97B94DF3-029A-499B-9ED3-61800246E709}
	EndGlobalSection
EndGlobal
