﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace DocPark.Commons
{
    public static class MsgCrypto
    {
        /// <summary>
        /// 使用 SHA-256 对输入字符串进行加密，返回十六进制字符串。
        /// </summary>
        /// <param name="input">要加密的字符串</param>
        /// <returns>加密后的十六进制字符串</returns>
        public static string ComputeSha256Hash(string input)
        {
            // 创建 SHA256 算法实例
            using (SHA256 sha256 = SHA256.Create())
            {
                // 将输入字符串转换为字节数组
                byte[] bytes = Encoding.UTF8.GetBytes(input);

                // 计算哈希值
                byte[] hashBytes = sha256.ComputeHash(bytes);

                // 将字节数组转换为十六进制字符串
                StringBuilder builder = new StringBuilder();
                foreach (byte b in hashBytes)
                {
                    builder.Append(b.ToString("x2")); // x2 表示转换为两位十六进制
                }
                return builder.ToString();
            }
        }
    }
}
