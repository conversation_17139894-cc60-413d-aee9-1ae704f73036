﻿using Docpark.MultipleComparisons.Interface.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IProductService
    {
        Dictionary<string, object> MultCompSettings { get; }
        /// <summary>
        /// 获取业务类型下的单据号包含的影像件数量
        /// </summary>
        /// <param name="businessTypeId"></param>
        /// <param name="billNos"></param>
        /// <param name="itemTypeId"></param>
        /// <returns></returns>
        Task<List<ScanFolderItemDto>> GetScanFolderItems(string businessTypeId, List<string> billNos, string[] itemTypeId);

    }
}
