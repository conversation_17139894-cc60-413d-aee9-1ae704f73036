@{
    Layout = "_Layout";
}

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAuthModal">
            <i class="fas fa-plus me-2"></i>创建新授权配置
        </button>
        <button class="btn btn-success" onclick="refreshAuthList()">
            <i class="fas fa-sync-alt me-2"></i>刷新
        </button>
    </div>
    <div>
        <input type="text" class="form-control" id="searchInput" placeholder="搜索授权配置..." style="width: 300px;">
    </div>
</div>

<!-- 授权配置列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">授权配置列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="authConfigsTable">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>授权类型</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="authConfigsTableBody">
                    <tr>
                        <td colspan="5" class="text-center">
                            <i class="fas fa-spinner fa-spin"></i>
                            加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建授权配置模态框 -->
<div class="modal fade" id="createAuthModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建授权配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAuthForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="authName" class="form-label">配置名称 *</label>
                                <input type="text" class="form-control" id="authName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="authType" class="form-label">授权类型 *</label>
                                <select class="form-select" id="authType" required onchange="onAuthTypeChange()">
                                    <option value="">请选择授权类型</option>
                                    <option value="1">基础认证 (BasicAuth)</option>
                                    <option value="2">OAuth2</option>
                                    <option value="3">API密钥</option>
                                    <option value="4">Bearer令牌</option>
                                    <option value="99">自定义</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="authDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="authDescription" rows="2"></textarea>
                    </div>
                    
                    <!-- 动态参数配置区域 -->
                    <div id="authParametersContainer">
                        <h6 class="mb-3">授权参数配置</h6>
                        <div id="authParametersFields">
                            <p class="text-muted">请先选择授权类型</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="authIsEnabled" checked>
                            <label class="form-check-label" for="authIsEnabled">
                                启用此授权配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createAuthConfig()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑授权配置模态框 -->
<div class="modal fade" id="editAuthModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑授权配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAuthForm">
                    <input type="hidden" id="editAuthId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAuthName" class="form-label">配置名称 *</label>
                                <input type="text" class="form-control" id="editAuthName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAuthType" class="form-label">授权类型 *</label>
                                <select class="form-select" id="editAuthType" required onchange="onEditAuthTypeChange()">
                                    <option value="">请选择授权类型</option>
                                    <option value="1">基础认证 (BasicAuth)</option>
                                    <option value="2">OAuth2</option>
                                    <option value="3">API密钥</option>
                                    <option value="4">Bearer令牌</option>
                                    <option value="99">自定义</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editAuthDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editAuthDescription" rows="2"></textarea>
                    </div>

                    <!-- 动态参数配置区域 -->
                    <div id="editAuthParametersContainer">
                        <h6 class="mb-3">授权参数配置</h6>
                        <div id="editAuthParametersFields">
                            <p class="text-muted">请先选择授权类型</p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editAuthIsEnabled">
                            <label class="form-check-label" for="editAuthIsEnabled">
                                启用此授权配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateAuthConfig()">保存</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadAuthConfigs();
        
        // 搜索功能
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#authConfigsTableBody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });

    function loadAuthConfigs() {
        callAPI('/api/AuthenticationConfig')
            .done(function(data) {
                displayAuthConfigs(data);
            })
            .fail(function() {
                $('#authConfigsTableBody').html('<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>');
            });
    }

    function displayAuthConfigs(configs) {
        var tbody = $('#authConfigsTableBody');
        tbody.empty();
        
        if (configs.length === 0) {
            tbody.html('<tr><td colspan="5" class="text-center text-muted">暂无授权配置</td></tr>');
            return;
        }
        
        configs.forEach(function(config) {
            var statusBadge = config.isEnabled 
                ? '<span class="badge bg-success">启用</span>' 
                : '<span class="badge bg-secondary">禁用</span>';
                
            var authTypeName = getAuthTypeDisplayName(config.type);
            
            var row = `
                <tr>
                    <td>${config.name || 'N/A'}</td>
                    <td>${authTypeName}</td>
                    <td>${statusBadge}</td>
                    <td>${formatDateTime(config.createdAt)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editAuthConfig('${config.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="testAuthConfig('${config.id}')">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteAuthConfig('${config.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getAuthTypeDisplayName(type) {
        switch(type) {
            case 1: return '<span class="badge bg-info">基础认证</span>';
            case 2: return '<span class="badge bg-warning">OAuth2</span>';
            case 3: return '<span class="badge bg-success">API密钥</span>';
            case 4: return '<span class="badge bg-primary">Bearer令牌</span>';
            case 99: return '<span class="badge bg-secondary">自定义</span>';
            default: return '<span class="badge bg-light text-dark">未知</span>';
        }
    }

    function onAuthTypeChange() {
        var authType = $('#authType').val();
        var container = $('#authParametersFields');
        container.empty();
        
        switch(authType) {
            case '1': // BasicAuth
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">授权请求地址</label>
                        <input type="url" class="form-control" id="basicAuthUrl" placeholder="https://api.example.com/login (可选，用于登录API模式)">
                        <div class="form-text">如果使用登录API模式获取Token，请填入登录接口地址</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">请求参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="basicAuthParams" rows="4" required placeholder='请输入JSON格式的请求参数'></textarea>
                        <div class="form-text">
                            <strong>请求参数示例：</strong><br>
                            <code>{"userNameOrEmailAddress": "admin", "password": "123qwe", "isEncrypt": "false"}</code>
                        </div>
                    </div>
                    <div class="mb-3" id="basicAuthResponseConfig" style="display: none;">
                        <label class="form-label">响应配置 (JSON格式)</label>
                        <textarea class="form-control" id="basicAuthResponseParams" rows="4" placeholder='请输入JSON格式的响应配置'></textarea>
                        <div class="form-text">
                            <strong>响应配置示例：</strong><br>
                            <code>{"tokenPath": "result.accessToken", "refreshTokenPath": "result.refreshToken", "expiresInPath": "result.expireInSeconds"}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="fillBasicAuthExample()">填入请求参数示例</button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillBasicAuthResponseExample()">填入响应配置示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('basicAuthParams')">验证请求参数JSON</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('basicAuthResponseParams')">验证响应配置JSON</button>
                    </div>
                `);

                // 添加URL输入事件监听器
                setTimeout(function() {
                    $('#basicAuthUrl').off('input.basicAuth').on('input.basicAuth', function() {
                        var hasUrl = $(this).val().trim() !== '';
                        $('#basicAuthResponseConfig').toggle(hasUrl);
                    });
                }, 100);
                break;
            case '2': // OAuth2
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">OAuth2令牌请求地址 *</label>
                        <input type="url" class="form-control" id="oauth2TokenUrl" required placeholder="https://api.example.com/oauth/token">
                        <div class="form-text">OAuth2获取访问令牌的接口地址</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">OAuth2参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="oauth2Params" rows="6" required placeholder='请输入JSON格式的OAuth2配置参数'></textarea>
                        <div class="form-text">
                            <strong>OAuth2配置示例：</strong><br>
                            <code>{
  "clientId": "your_client_id",
  "clientSecret": "your_client_secret",
  "scope": "read write",
  "grantType": "client_credentials"
}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillOAuth2Example()">填入OAuth2示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('oauth2Params')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '3': // API Key
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">API Key参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="apiKeyParams" rows="5" required placeholder='请输入JSON格式的API Key配置参数'></textarea>
                        <div class="form-text">
                            <strong>API Key配置示例：</strong><br>
                            <code>{
  "keyName": "X-API-Key",
  "keyValue": "your_api_key_value",
  "location": "header"
}</code>
                            <br><small>location可选值: "header" 或 "query"</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillApiKeyExample()">填入API Key示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('apiKeyParams')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '4': // Bearer Token
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">Bearer Token参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="bearerTokenParams" rows="4" required placeholder='请输入JSON格式的Bearer Token配置参数'></textarea>
                        <div class="form-text">
                            <strong>Bearer Token配置示例：</strong><br>
                            <code>{"token": "your_bearer_token_value"}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillBearerTokenExample()">填入Bearer Token示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('bearerTokenParams')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '99': // Custom
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">自定义参数 (JSON格式)</label>
                        <textarea class="form-control" id="customParams" rows="4" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
                        <div class="form-text">请输入有效的JSON格式参数</div>
                    </div>
                `);
                break;
            default:
                container.html('<p class="text-muted">请先选择授权类型</p>');
        }
    }

    function createAuthConfig() {
        var authType = $('#authType').val();
        if (!authType) {
            showError('请选择授权类型');
            return;
        }

        var parameters = {};
        var responseConfig = {};

        try {
            switch(authType) {
                case '1': // BasicAuth
                    parameters = JSON.parse($('#basicAuthParams').val() || '{}');
                    // 如果有登录URL，添加到参数中
                    var loginUrl = $('#basicAuthUrl').val();
                    if (loginUrl) {
                        parameters.loginUrl = loginUrl;
                        // 如果有响应配置，解析并添加到responseConfig中
                        var responseConfigText = $('#basicAuthResponseParams').val();
                        if (responseConfigText && responseConfigText.trim()) {
                            responseConfig = JSON.parse(responseConfigText);
                        }
                    }
                    break;
                case '2': // OAuth2
                    parameters = JSON.parse($('#oauth2Params').val() || '{}');
                    // OAuth2必须有tokenUrl
                    var tokenUrl = $('#oauth2TokenUrl').val();
                    if (!tokenUrl) {
                        showError('OAuth2令牌请求地址不能为空');
                        return;
                    }
                    parameters.tokenUrl = tokenUrl;
                    break;
                case '3': // API Key
                    parameters = JSON.parse($('#apiKeyParams').val() || '{}');
                    break;
                case '4': // Bearer Token
                    parameters = JSON.parse($('#bearerTokenParams').val() || '{}');
                    break;
                case '99': // Custom
                    parameters = JSON.parse($('#customParams').val() || '{}');
                    break;
                default:
                    showError('未知的授权类型');
                    return;
            }
        } catch (e) {
            showError('参数格式错误，请输入有效的JSON: ' + e.message);
            return;
        }

        var formData = {
            name: $('#authName').val(),
            description: $('#authDescription').val(),
            type: parseInt(authType),
            parameters: parameters,
            responseConfig: responseConfig,
            isEnabled: $('#authIsEnabled').is(':checked')
        };

        if (!formData.name) {
            showError('请填写配置名称');
            return;
        }
        
        callAPI('/api/AuthenticationConfig', 'POST', formData)
            .done(function(data) {
                showSuccess('授权配置创建成功');
                $('#createAuthModal').modal('hide');
                $('#createAuthForm')[0].reset();
                $('#authParametersFields').html('<p class="text-muted">请先选择授权类型</p>');
                loadAuthConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showError('创建失败: ' + message);
            });
    }

    function testAuthConfig(id) {
        // 显示测试进行中的提示
        showInfo('正在测试授权配置...');

        callAPI('/api/AuthenticationConfig/' + id + '/test', 'POST')
            .done(function(result) {
                if (result.success) {
                    var successMessage = '✅ 授权配置测试成功！';

                    if (result.hasToken) {
                        successMessage += ' Token获取正常';
                        if (result.tokenType) {
                            successMessage += ' (类型: ' + result.tokenType + ')';
                        }
                        if (result.expiresAt) {
                            var expiresAt = new Date(result.expiresAt);
                            successMessage += '，过期时间: ' + expiresAt.toLocaleString();
                        }
                        successMessage += '，可以为其他API提供授权。';
                    } else {
                        successMessage += ' 授权验证通过。';
                    }

                    showSuccess(successMessage);
                } else {
                    var failMessage = '⚠️ 授权配置测试失败';
                    if (result.message) {
                        failMessage += ': ' + result.message;
                    }
                    failMessage += '。请检查配置参数和请求地址是否正确。';
                    showWarning(failMessage);
                }
            })
            .fail(function(xhr) {
                var errorMessage = '授权配置测试请求失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage += ': ' + xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        var errorData = JSON.parse(xhr.responseText);
                        if (errorData.message) {
                            errorMessage += ': ' + errorData.message;
                        }
                    } catch (e) {
                        errorMessage += ': ' + xhr.statusText;
                    }
                }
                showError('❌ ' + errorMessage);
            });
    }

    function editAuthConfig(id) {
        callAPI('/api/AuthenticationConfig/' + id)
            .done(function(config) {
                // 填充编辑表单
                $('#editAuthId').val(config.id);
                $('#editAuthName').val(config.name);
                $('#editAuthDescription').val(config.description);
                $('#editAuthType').val(config.type);
                $('#editAuthIsEnabled').prop('checked', config.isEnabled);

                // 触发类型变化事件来显示相应的参数字段
                onEditAuthTypeChange();

                // 填充参数值
                setTimeout(function() {
                    if (config.parameters) {
                        var params = {...config.parameters}; // 复制参数对象

                        switch(config.type) {
                            case 1: // BasicAuth
                                // 如果有loginUrl，填充到URL字段并从JSON中移除
                                if (params.loginUrl) {
                                    $('#editBasicAuthUrl').val(params.loginUrl);
                                    delete params.loginUrl;
                                    // 显示响应配置区域
                                    $('#editBasicAuthResponseConfig').show();
                                }
                                $('#editBasicAuthParams').val(JSON.stringify(params, null, 2));

                                // 填充响应配置
                                if (config.responseConfig && Object.keys(config.responseConfig).length > 0) {
                                    $('#editBasicAuthResponseParams').val(JSON.stringify(config.responseConfig, null, 2));
                                }
                                break;
                            case 2: // OAuth2
                                // 如果有tokenUrl，填充到URL字段并从JSON中移除
                                if (params.tokenUrl) {
                                    $('#editOauth2TokenUrl').val(params.tokenUrl);
                                    delete params.tokenUrl;
                                }
                                $('#editOauth2Params').val(JSON.stringify(params, null, 2));
                                break;
                            case 3: // API Key
                                $('#editApiKeyParams').val(JSON.stringify(params, null, 2));
                                break;
                            case 4: // Bearer Token
                                $('#editBearerTokenParams').val(JSON.stringify(params, null, 2));
                                break;
                            case 99: // Custom
                                $('#editCustomParams').val(JSON.stringify(params, null, 2));
                                break;
                        }
                    }
                }, 100);

                $('#editAuthModal').modal('show');
            })
            .fail(function() {
                showError('获取授权配置详情失败');
            });
    }

    function deleteAuthConfig(id) {
        if (!confirm('确定要删除这个授权配置吗？此操作不可恢复。')) {
            return;
        }

        callAPI('/api/AuthenticationConfig/' + id, 'DELETE')
            .done(function() {
                showSuccess('授权配置删除成功');
                loadAuthConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '删除失败';
                showError('删除失败: ' + message);
            });
    }

    function toggleLoginApiFields() {
        var useLoginApi = $('#useLoginApi').is(':checked');
        $('#loginApiFields').toggle(useLoginApi);
    }

    function onEditAuthTypeChange() {
        var authType = $('#editAuthType').val();
        var container = $('#editAuthParametersFields');
        container.empty();

        switch(authType) {
            case '1': // BasicAuth
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">授权请求地址</label>
                        <input type="url" class="form-control" id="editBasicAuthUrl" placeholder="https://api.example.com/login (可选，用于登录API模式)">
                        <div class="form-text">如果使用登录API模式获取Token，请填入登录接口地址</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">请求参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="editBasicAuthParams" rows="4" required placeholder='请输入JSON格式的请求参数'></textarea>
                        <div class="form-text">
                            <strong>请求参数示例：</strong><br>
                            <code>{"userNameOrEmailAddress": "admin", "password": "123qwe", "isEncrypt": "false"}</code>
                        </div>
                    </div>
                    <div class="mb-3" id="editBasicAuthResponseConfig" style="display: none;">
                        <label class="form-label">响应配置 (JSON格式)</label>
                        <textarea class="form-control" id="editBasicAuthResponseParams" rows="4" placeholder='请输入JSON格式的响应配置'></textarea>
                        <div class="form-text">
                            <strong>响应配置示例：</strong><br>
                            <code>{"tokenPath": "result.accessToken", "refreshTokenPath": "result.refreshToken", "expiresInPath": "result.expireInSeconds"}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="fillEditBasicAuthExample()">填入请求参数示例</button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillEditBasicAuthResponseExample()">填入响应配置示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('editBasicAuthParams')">验证请求参数JSON</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('editBasicAuthResponseParams')">验证响应配置JSON</button>
                    </div>
                `);

                // 添加编辑模式的URL输入事件监听器
                setTimeout(function() {
                    $('#editBasicAuthUrl').off('input.editBasicAuth').on('input.editBasicAuth', function() {
                        var hasUrl = $(this).val().trim() !== '';
                        $('#editBasicAuthResponseConfig').toggle(hasUrl);
                    });
                }, 100);
                break;
            case '2': // OAuth2
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">OAuth2令牌请求地址 *</label>
                        <input type="url" class="form-control" id="editOauth2TokenUrl" required placeholder="https://api.example.com/oauth/token">
                        <div class="form-text">OAuth2获取访问令牌的接口地址</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">OAuth2参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="editOauth2Params" rows="6" required placeholder='请输入JSON格式的OAuth2配置参数'></textarea>
                        <div class="form-text">
                            <strong>OAuth2配置示例：</strong><br>
                            <code>{
  "clientId": "your_client_id",
  "clientSecret": "your_client_secret",
  "scope": "read write",
  "grantType": "client_credentials"
}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillEditOAuth2Example()">填入OAuth2示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('editOauth2Params')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '3': // API Key
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">API Key参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="editApiKeyParams" rows="5" required placeholder='请输入JSON格式的API Key配置参数'></textarea>
                        <div class="form-text">
                            <strong>API Key配置示例：</strong><br>
                            <code>{
  "keyName": "X-API-Key",
  "keyValue": "your_api_key_value",
  "location": "header"
}</code>
                            <br><small>location可选值: "header" 或 "query"</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillEditApiKeyExample()">填入API Key示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('editApiKeyParams')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '4': // Bearer Token
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">Bearer Token参数配置 (JSON格式) *</label>
                        <textarea class="form-control" id="editBearerTokenParams" rows="4" required placeholder='请输入JSON格式的Bearer Token配置参数'></textarea>
                        <div class="form-text">
                            <strong>Bearer Token配置示例：</strong><br>
                            <code>{"token": "your_bearer_token_value"}</code>
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="fillEditBearerTokenExample()">填入Bearer Token示例</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="validateJson('editBearerTokenParams')">验证JSON格式</button>
                    </div>
                `);
                break;
            case '99': // Custom
                container.html(`
                    <div class="mb-3">
                        <label class="form-label">自定义参数 (JSON格式)</label>
                        <textarea class="form-control" id="editCustomParams" rows="4" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
                    </div>
                `);
                break;
            default:
                container.html('<p class="text-muted">请先选择授权类型</p>');
        }
    }

    function toggleEditLoginApiFields() {
        var useLoginApi = $('#editUseLoginApi').is(':checked');
        $('#editLoginApiFields').toggle(useLoginApi);
    }

    function updateAuthConfig() {
        var id = $('#editAuthId').val();
        var authType = $('#editAuthType').val();
        if (!authType) {
            showError('请选择授权类型');
            return;
        }

        var parameters = {};
        var responseConfig = {};

        try {
            switch(authType) {
                case '1': // BasicAuth
                    parameters = JSON.parse($('#editBasicAuthParams').val() || '{}');
                    // 如果有登录URL，添加到参数中
                    var editLoginUrl = $('#editBasicAuthUrl').val();
                    if (editLoginUrl) {
                        parameters.loginUrl = editLoginUrl;
                        // 如果有响应配置，解析并添加到responseConfig中
                        var responseConfigText = $('#editBasicAuthResponseParams').val();
                        if (responseConfigText && responseConfigText.trim()) {
                            responseConfig = JSON.parse(responseConfigText);
                        }
                    }
                    break;
                case '2': // OAuth2
                    parameters = JSON.parse($('#editOauth2Params').val() || '{}');
                    // OAuth2必须有tokenUrl
                    var editTokenUrl = $('#editOauth2TokenUrl').val();
                    if (!editTokenUrl) {
                        showError('OAuth2令牌请求地址不能为空');
                        return;
                    }
                    parameters.tokenUrl = editTokenUrl;
                    break;
                case '3': // API Key
                    parameters = JSON.parse($('#editApiKeyParams').val() || '{}');
                    break;
                case '4': // Bearer Token
                    parameters = JSON.parse($('#editBearerTokenParams').val() || '{}');
                    break;
                case '99': // Custom
                    parameters = JSON.parse($('#editCustomParams').val() || '{}');
                    break;
                default:
                    showError('未知的授权类型');
                    return;
            }
        } catch (e) {
            showError('参数格式错误，请输入有效的JSON: ' + e.message);
            return;
        }

        var formData = {
            id: id,
            name: $('#editAuthName').val(),
            description: $('#editAuthDescription').val(),
            type: parseInt(authType),
            parameters: parameters,
            responseConfig: responseConfig,
            isEnabled: $('#editAuthIsEnabled').is(':checked')
        };

        if (!formData.name) {
            showError('请填写配置名称');
            return;
        }

        callAPI('/api/AuthenticationConfig/' + id, 'PUT', formData)
            .done(function(data) {
                showSuccess('授权配置更新成功');
                $('#editAuthModal').modal('hide');
                loadAuthConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '更新失败';
                showError('更新失败: ' + message);
            });
    }

    function fillBasicAuthExample() {
        $('#basicAuthParams').val(JSON.stringify({
            "userNameOrEmailAddress": "admin",
            "password": "123qwe",
            "isEncrypt": "false"
        }, null, 2));
    }

    function fillBasicAuthResponseExample() {
        $('#basicAuthResponseParams').val(JSON.stringify({
            "tokenPath": "result.accessToken",
            "refreshTokenPath": "result.refreshToken",
            "expiresInPath": "result.expireInSeconds"
        }, null, 2));
        $('#basicAuthResponseConfig').show();
    }

    function fillOAuth2Example() {
        $('#oauth2TokenUrl').val("https://api.example.com/oauth/token");
        $('#oauth2Params').val(JSON.stringify({
            "clientId": "your_client_id",
            "clientSecret": "your_client_secret",
            "scope": "read write",
            "grantType": "client_credentials",
            "tokenPath": "access_token",
            "refreshTokenPath": "refresh_token",
            "expiresInPath": "expires_in"
        }, null, 2));
    }

    function fillApiKeyExample() {
        $('#apiKeyParams').val(JSON.stringify({
            "keyName": "X-API-Key",
            "keyValue": "your_api_key_value",
            "location": "header"
        }, null, 2));
    }

    function fillBearerTokenExample() {
        $('#bearerTokenParams').val(JSON.stringify({
            "token": "your_bearer_token_value"
        }, null, 2));
    }

    function validateJson(textareaId) {
        try {
            var jsonText = $('#' + textareaId).val();
            if (!jsonText.trim()) {
                showWarning('请先输入JSON内容');
                return;
            }
            JSON.parse(jsonText);
            showSuccess('JSON格式验证通过');
        } catch (e) {
            showError('JSON格式错误: ' + e.message);
        }
    }

    // 编辑表单的示例填充函数
    function fillEditBasicAuthExample() {
        $('#editBasicAuthParams').val(JSON.stringify({
            "userNameOrEmailAddress": "admin",
            "password": "123qwe",
            "isEncrypt": "false"
        }, null, 2));
    }

    function fillEditBasicAuthResponseExample() {
        $('#editBasicAuthResponseParams').val(JSON.stringify({
            "tokenPath": "result.accessToken",
            "refreshTokenPath": "result.refreshToken",
            "expiresInPath": "result.expireInSeconds"
        }, null, 2));
        $('#editBasicAuthResponseConfig').show();
    }

    function fillEditOAuth2Example() {
        $('#editOauth2TokenUrl').val("https://api.example.com/oauth/token");
        $('#editOauth2Params').val(JSON.stringify({
            "clientId": "your_client_id",
            "clientSecret": "your_client_secret",
            "scope": "read write",
            "grantType": "client_credentials",
            "tokenPath": "access_token",
            "refreshTokenPath": "refresh_token",
            "expiresInPath": "expires_in"
        }, null, 2));
    }

    function fillEditApiKeyExample() {
        $('#editApiKeyParams').val(JSON.stringify({
            "keyName": "X-API-Key",
            "keyValue": "your_api_key_value",
            "location": "header"
        }, null, 2));
    }

    function fillEditBearerTokenExample() {
        $('#editBearerTokenParams').val(JSON.stringify({
            "token": "your_bearer_token_value"
        }, null, 2));
    }

    function refreshAuthList() {
        loadAuthConfigs();
        showSuccess('列表已刷新');
    }
</script>
}
