﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Commons;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class DocparkHostService : IDocparkHostService
    {
        public DocparkHostService(IDocparkHttpClient httpClient, IDGrpc grpc)
        {
            _httpClient = httpClient;
            _grpc = grpc;
            BaseAddress = _grpc.GetStringAsync(ConstSettings.DefaultHost).Result;
        }

        private IDocparkHttpClient _httpClient { get; }
        private IDGrpc _grpc { get; }
        private string BaseAddress { get; set; }

        public async Task<ResultInfo<List<UserListDto>>> GetUsersByUserIds(long[] userIds)
        {
            var userRequest = new
            {
                userIds = userIds,
            };
            var api = ConstApi.User_GetUsersByUserIds_Api;
            var strResult = await _httpClient.PostAsync(api.ToUrl(BaseAddress), userRequest);

            var result = JsonConvert.DeserializeObject<ListResultDto<UserListDto>>(strResult);
            return ResultInfo<List<UserListDto>>.Success(result.Result.Items.ToList());
        }

        public async Task<ResultInfo<string>> GetOrganizationUnitByUserName(string userName)
        {
            var api = ConstApi.OrganizationUnit_GetParentOrganizationUnitByUserName;
            var strResult = await _httpClient.GetAsync(string.Format(api, userName).ToUrl(BaseAddress));
            var result = JsonConvert.DeserializeObject<ListResultDto<ExtendedOrganizationUnitDto>>(strResult);
            var organizationUnit = result.Result.Items.FirstOrDefault(p => p.ParentId == null);
            return organizationUnit == null ? ResultInfo<string>.Success("未找到组织") : ResultInfo<string>.Success(organizationUnit.DisplayName);
        }
        public async Task UpdateFormDataStatus(Guid mstId, int status)
        {
            var url = ConstApi.AppObject_UpdateStatus.ToUrl(BaseAddress);
            Dictionary<string, object> keyValues = new Dictionary<string, object>() {
                { "mstId",mstId },
                { "status",status}
            };
            await _httpClient.PostAsync(url, keyValues);
        }
        public async Task<ResultInfo<string>> GetOrganizationUnitByUserId(string userId)
        {
            var api = ConstApi.OrganizationUnit_GetOrganizationUnitByUserId;
            var strResult = await _httpClient.GetAsync(string.Format(api, userId).ToUrl(BaseAddress));
            var result = JsonConvert.DeserializeObject<ListResultDto<ExtendedOrganizationUnitDto>>(strResult);
            var organizationUnit = result.Result.Items.FirstOrDefault(p => p.ParentId == null);
            if (organizationUnit == null)
            {
                organizationUnit = result.Result.Items.FirstOrDefault();
            }
            return organizationUnit == null ? ResultInfo<string>.Success("未找到组织") : ResultInfo<string>.Success(organizationUnit.DisplayName);
        }

        public async Task<ResultInfo<string>> GetShortSummary(string mstId)
        {
            var api = ConstApi.DocumentForm_GetShortSummary;
            var result = await _httpClient.GetAsync<ResultDto>(string.Format(api, mstId).ToUrl(BaseAddress));
            if (result.Success)
            {
                return ResultInfo<string>.Success(result.Result.ToString());
            }
            else
            {
                return ResultInfo<string>.Fail(result.Error.Message);
            }
        }
        public async Task<Dictionary<string, object>> GetShortSummarys(List<Guid> mstId)
        {
            var api = ConstApi.DocumentForm_GetShortSummarys;
            var formRequst = new
            {
                mstIds = mstId,
            };
            var result = await _httpClient.PostAsync<ResultDto>(string.Format(api, mstId).ToUrl(BaseAddress), formRequst);
            if (result.Success)
            {
                return JsonConvert.DeserializeObject<Dictionary<string, object>>(result.Result.ToString());
            }
            else
            {
                return null;
            }
        }
        public async Task<ResultInfo> BatchRemove(List<string> mstId)
        {
            var api = ConstApi.AppForm_BatchRemove;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), mstId);

            if (result.Success)
            {
                return ResultInfo.Success();
            }
            else
            {
                return ResultInfo.Fail(result.Error.Message);
            }
        }

        public async Task<ResultInfo> SaveFormData(List<SaveFormDocumentDto> inputs)
        {
            var api = ConstApi.DocumentForm_SaveFiles;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), inputs);
            if (result.Success)
            {
                return ResultInfo.Success();
            }
            else
            {
                return ResultInfo.Fail(result.Error.Message);
            }
        }

        public async Task<T> GetFormData<T>(string documentTypeId, string mstId)
        {
            var resultFormData = await this.GetFormDataList(documentTypeId, new List<Filter>(),
                new List<Sorter>(), new List<string>() { mstId }, 0, 1);
            var jObjFormData = JObject.Parse(resultFormData.Result.Data);
            if (jObjFormData["items"].HasValues)
            {
                var data = jObjFormData["items"].Children().FirstOrDefault();
                if (data != null)
                {
                    return JsonConvert.DeserializeObject<T>(data.ToString());
                }
            }
            return default(T);
        }
        public async Task<T> GetFormData<T>(string mstId)
        {
            var api = string.Format(ConstApi.DocumentForm_GetFormData, mstId);

            var result = await _httpClient.GetAsync<ResultDto>(api.ToUrl(BaseAddress));
            if (result.Success)
            {
                var formData = JsonConvert.DeserializeObject<T>(result.Result.ToString());
                return formData;
            }
            return default(T);
        }

        public async Task<List<T>> GetFormDataList<T>(string documentTypeId, List<string> mstId, bool enablePremission = false)
        {
            var resultFormData = await this.GetFormDataList(documentTypeId, new List<Filter>(),
                   new List<Sorter>(), mstId, 1, mstId.Count, enablePremission);
            var jObjFormData = JObject.Parse(resultFormData.Result.Data);
            List<T> list = new List<T>();
            if (jObjFormData["items"].HasValues)
            {
                foreach (var data in jObjFormData["items"].Children())
                {
                    if (data != null)
                    {
                        list.Add(JsonConvert.DeserializeObject<T>(data.ToString()));
                    }
                }

            }
            return list;
        }
        public async Task<ResultInfo<string>> GetFormDataList(string documentTypeId, List<Filter> filters, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10, bool enablePremission = false)
        {
            var formRequest = new
            {
                formId = CommonUtil.ConvertObjectIdToGuid(documentTypeId),
                filters = filters,
                sorters = sorters,
                mstIds = mstIds,
                maxResultCount = pageSize <= 0 ? 10 : pageSize,
                skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                enablePermission = enablePremission
            };

            var api = ConstApi.DocumentForm_GetFormDataList;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequest);
            if (result.Success)
            {
                return ResultInfo<string>.Success(result.Result.ToString());
            }
            else
            {
                return ResultInfo<string>.Fail(result.Error.Message);
            }
        }
        public async Task<ResultInfo<string>> GetFormDataLists(string documentTypeId, List<List<Filter>> filterss, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10)
        {
            var formRequests = new List<object>();
            foreach (var filters in filterss)
            {
                var formRequest = new
                {
                    formId = CommonUtil.ConvertObjectIdToGuid(documentTypeId),
                    filters = filters,
                    sorters = sorters,
                    mstIds = mstIds,
                    maxResultCount = pageSize <= 0 ? 10 : pageSize,
                    skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                };
                formRequests.Add(formRequest);
            }

            var api = ConstApi.DocumentForm_GetFormDataLists;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequests);
            if (result.Success)
            {
                return ResultInfo<string>.Success(result.Result.ToString());
            }
            else
            {
                return ResultInfo<string>.Fail(result.Error.Message);
            }
        }
        public async Task<ResultInfo<string>> GetMstIds(string formId, List<Filter> filters, int pageIndex = 1, int pageSize = 10)
        {
            ResultDto result = new ResultDto();
            var formRequest = new
            {
                formId = formId,
                filters = filters,
                sorters = Array.Empty<Sorter>(),
                mstIds = Array.Empty<string>(),
                maxResultCount = pageSize <= 0 ? 10 : pageSize,
                skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                enablePermission = false,
                isSyncPager = false
            };
            var api = ConstApi.FormData_GetMstIds;

            result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequest);
            if (result.Success)
            {
                return ResultInfo<string>.Success(result.Result.ToString());
            }
            else
            {
                return ResultInfo<string>.Fail(result.Error.Message);
            }
        }
        public async Task<ResultInfo<string>> GetDocumentMstIds(string formId, List<Filter> filters, int pageIndex = 1, int pageSize = 10)
        {
            ResultDto result = new ResultDto();
            var formRequest = new
            {
                formId = formId,
                filters = filters,
                sorters = Array.Empty<Sorter>(),
                mstIds = Array.Empty<string>(),
                maxResultCount = pageSize <= 0 ? 10 : pageSize,
                skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                enablePermission = false,
                isSyncPager = false
            };
            var api = ConstApi.DocumentForm_GetMstIds;

            result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequest);
            if (result.Success)
            {
                return ResultInfo<string>.Success(result.Result.ToString());
            }
            else
            {
                return ResultInfo<string>.Fail(result.Error.Message);
            }
        }
        public async Task<ResultInfo<List<Guid>>> GetPermissionBusinessType(string userId)
        {
            var api = string.Format(ConstApi.AppPermissionService_GetPermissionBusinessType, userId).ToUrl(BaseAddress);

            var result = await _httpClient.GetAsync<ResultDto>(api);

            var jObjFormData = JObject.Parse(result.Result.ToString());

            var guids = JsonConvert.DeserializeObject<List<Guid>>(jObjFormData["items"].ToString());
            return ResultInfo<List<Guid>>.Success(guids);
        }


        public async Task<List<DocFormField>> GetFields(string documentTypeId)
        {
            var url = string.Format(ConstApi.DocumentForm_GetFields, documentTypeId).ToUrl(BaseAddress);
            var strResult = await _httpClient.GetAsync(url);
            var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(strResult);
            if (result.ContainsKey("success") && result.ContainsKey("result") && result["success"].ToString().ToLower() == "true")
            {
                return JsonConvert.DeserializeObject<List<DocFormField>>(result["result"].ToString());
            }
            return new List<DocFormField>();
        }

        public async Task<bool> SaveFormData(Dictionary<string, object> data)
        {
            var url = ConstApi.DocumentForm_SaveFormData.ToUrl(BaseAddress);
            string strResult = await _httpClient.PostAsync(url, data);
            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(strResult);
            return jsonResult.ContainsKey("result") && jsonResult["success"].ToString().ToLower() == "true";
        }

        public async Task<bool> SaveFormDataList(List<Dictionary<string, object>> dataList)
        {
            var url = ConstApi.DocumentForm_SaveFormDataList.ToUrl(BaseAddress);
            string strResult = await _httpClient.PostAsync(url, dataList);
            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(strResult);
            return jsonResult.ContainsKey("result") && jsonResult["success"].ToString().ToLower() == "true";
        }
        public async Task<List<RelationshipsDto>> GetRelationships(string relationId, List<Guid> TargetObjInstanceIds)
        {

            var url = (ConstApi.Relationships_GetObjsByTargetObjInstanceIds).ToUrl(BaseAddress);

            Dictionary<string, object> keyValuePairs = new Dictionary<string, object>() {
                { "relationId",relationId },
                {"targetObjInstanceIds",TargetObjInstanceIds }
            };

            var Result = await _httpClient.PostAsync<ResultDto>(url, keyValuePairs);

            if (Result.Success)
            {
                return JsonConvert.DeserializeObject<List<RelationshipsDto>>(Result.Result.ToString());
            }
            return null;
        }

        public async Task<List<Guid>> GetTargetObjInstanceIds(List<Guid> objUniqueIds)
        {
            var url = (ConstApi.Relationships_GetTargetObjInstanceIdList).ToUrl(BaseAddress);

            var Result = await _httpClient.PostAsync<ResultDto>(url, objUniqueIds);

            if (Result.Success)
            {
                return JsonConvert.DeserializeObject<List<Guid>>(Result.Result.ToString());
            }
            return null;
        }

        public async Task RemoveRelationshipsList(List<Guid> objUniqueIds)
        {
            var url = (ConstApi.Relationships_RemoveList).ToUrl(BaseAddress);
            string strParams = "";
            foreach (var id in objUniqueIds)
            {
                strParams += "&objUniqueIdList=" + id;
            }
            var result = await _httpClient.DeleteAsync<ResultDto>(url + "?" + strParams.TrimStart('&'));
        }

        public async Task<Guid> GetTargetObjInstanceId(Guid ObjUniqueId)
        {
            var url = (string.Format(ConstApi.Relationships_GetTargetObjInstanceId, ObjUniqueId)).ToUrl(BaseAddress);
            var Result = await _httpClient.GetAsync<ResultDto>(url);

            if (Result.Success)
            {
                return new Guid(Result.Result.ToString());
            }
            return Guid.Empty;
        }

        public async Task<bool> SaveRelationships(List<Dictionary<string, object>> data)
        {
            var url = ConstApi.Relationship_BatchSave.ToUrl(BaseAddress);
            var result = await _httpClient.PostAsync<ResultDto>(url, data);
            return result.Success;
        }
        public async Task<bool> RemoveRelationships(Guid objUnionId)
        {
            var url = string.Format(ConstApi.Relationships_Remove, objUnionId).ToUrl(BaseAddress);
            var result = await _httpClient.DeleteAsync<ResultDto>(url);
            return result.Success;
        }


        public async Task<bool> RemoveAllRelationships(Guid ObjId, string relationId, Guid TargetObjInstanceId)
        {
            var url = string.Format(ConstApi.Relationships_RemoveAll, ObjId, relationId, TargetObjInstanceId).ToUrl(BaseAddress);
            var result = await _httpClient.DeleteAsync<ResultDto>(url);
            return result.Success;
        }

        public async Task<int> Count(string documentTypeId, List<Filter> filters, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10, bool enablePremission = false)
        {
            var formRequest = new
            {
                formId = CommonUtil.ConvertObjectIdToGuid(documentTypeId),
                filters = filters,
                sorters = sorters,
                mstIds = mstIds,
                maxResultCount = pageSize <= 0 ? 10 : pageSize,
                skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                enablePermission = enablePremission
            };

            var api = ConstApi.DocumentForm_GetFormDataCount;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequest);
            if (result.Success)
            {
                return int.Parse(result.Result.ToString());
            }
            else
            {
                return 0;
            }
        }
        public async Task<int> Count(string documentTypeId, List<List<Filter>> filterss, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10)
        {
            var formRequests = new List<object>();
            foreach (var filters in filterss)
            {
                var formRequest = new
                {
                    formId = CommonUtil.ConvertObjectIdToGuid(documentTypeId),
                    filters = filters,
                    sorters = sorters,
                    mstIds = mstIds,
                    maxResultCount = pageSize <= 0 ? 10 : pageSize,
                    skipCount = ((pageIndex <= 0 ? 1 : pageIndex) - 1) * pageSize,
                };
                formRequests.Add(formRequest);
            }

            var api = ConstApi.DocumentForm_GetFormDataCounts;

            var result = await _httpClient.PostAsync<ResultDto>(api.ToUrl(BaseAddress), formRequests);
            if (result.Success)
            {
                return int.Parse(result.Result.ToString());
            }
            else
            {
                return 0;
            }
        }
    }
}
