@{
    Layout = "_Layout";
}

<!-- 页面标题 -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">系统监控</h1>
    <div>
        <button type="button" class="btn btn-primary btn-sm" onclick="performBatchHealthCheck()">
            <i class="fas fa-heartbeat fa-sm text-white-50"></i> 批量健康检查
        </button>
        <button type="button" class="btn btn-success btn-sm" onclick="refreshApiHealth()">
            <i class="fas fa-sync fa-sm text-white-50"></i> 刷新监控
        </button>
        <button type="button" class="btn btn-info btn-sm" onclick="exportMonitoringData()">
            <i class="fas fa-download fa-sm text-white-50"></i> 导出数据
        </button>
    </div>
</div>

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 系统状态概览 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            系统状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="system-status">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-heartbeat fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            调度器状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="scheduler-status">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            活跃任务数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-jobs">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            今日成功率
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="success-rate">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时监控图表 -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">API执行趋势</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="refreshChart()">刷新数据</a>
                        <a class="dropdown-item" href="#" onclick="exportChartData()">导出数据</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="executionTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统资源使用</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="small text-gray-500">CPU使用率</div>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" id="cpu-usage" style="width: 0%"></div>
                    </div>
                    <div class="text-right small text-gray-500" id="cpu-percentage">0%</div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">内存使用率</div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" id="memory-usage" style="width: 0%"></div>
                    </div>
                    <div class="text-right small text-gray-500" id="memory-percentage">0%</div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">数据库连接</div>
                    <div class="progress">
                        <div class="progress-bar bg-info" role="progressbar" id="db-connections" style="width: 0%"></div>
                    </div>
                    <div class="text-right small text-gray-500" id="db-percentage">0%</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API健康状态 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">API健康状态</h6>
                <button class="btn btn-sm btn-primary" onclick="refreshApiHealth()">
                    <i class="fas fa-sync-alt me-2"></i>刷新
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="apiHealthTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>API名称</th>
                                <th>状态</th>
                                <th>响应时间</th>
                                <th>最后检查</th>
                                <th>成功率(24h)</th>
                                <th>错误信息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="apiHealthTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span class="ms-2">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近错误和警告 -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">最近错误</h6>
            </div>
            <div class="card-body">
                <div id="recent-errors">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统事件</h6>
            </div>
            <div class="card-body">
                <div id="system-events">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 性能指标 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">性能指标</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 mb-0 font-weight-bold text-primary" id="avg-response-time">-</div>
                            <div class="small text-gray-500">平均响应时间(ms)</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 mb-0 font-weight-bold text-success" id="total-requests">-</div>
                            <div class="small text-gray-500">今日总请求数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 mb-0 font-weight-bold text-info" id="data-processed">-</div>
                            <div class="small text-gray-500">数据处理量(MB)</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 mb-0 font-weight-bold text-warning" id="error-rate">-</div>
                            <div class="small text-gray-500">错误率(%)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let executionTrendChart;
    let refreshInterval;

    $(document).ready(function() {
        initMonitoring();
        
        // 每30秒刷新一次数据
        refreshInterval = setInterval(loadMonitoringData, 30000);
    });

    function initMonitoring() {
        initExecutionTrendChart();
        loadMonitoringData();
    }

    function loadMonitoringData() {
        loadSystemStatus();
        loadApiHealth();
        loadRecentErrors();
        loadSystemEvents();
        loadPerformanceMetrics();
        updateExecutionTrendChart();
    }

    function loadSystemStatus() {
        // 加载调度器统计
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                $('#scheduler-status').html('<span class="badge bg-success">运行中</span>');
                $('#active-jobs').text(data.runningJobs || 0);
                
                var successRate = data.todayExecutions > 0 
                    ? ((data.todaySuccesses / data.todayExecutions) * 100).toFixed(1) + '%'
                    : 'N/A';
                $('#success-rate').text(successRate);
            })
            .fail(function() {
                $('#scheduler-status').html('<span class="badge bg-danger">异常</span>');
                $('#active-jobs').text('N/A');
                $('#success-rate').text('N/A');
            });

        // 模拟系统状态（实际应该调用健康检查API）
        $('#system-status').html('<span class="badge bg-success">正常</span>');
        
        // 模拟系统资源使用情况
        updateResourceUsage();
    }

    function updateResourceUsage() {
        // 这里应该调用实际的系统监控API
        // 目前使用模拟数据
        var cpuUsage = Math.floor(Math.random() * 30) + 20; // 20-50%
        var memoryUsage = Math.floor(Math.random() * 40) + 30; // 30-70%
        var dbConnections = Math.floor(Math.random() * 20) + 10; // 10-30%

        $('#cpu-usage').css('width', cpuUsage + '%');
        $('#cpu-percentage').text(cpuUsage + '%');

        $('#memory-usage').css('width', memoryUsage + '%');
        $('#memory-percentage').text(memoryUsage + '%');

        $('#db-connections').css('width', dbConnections + '%');
        $('#db-percentage').text(dbConnections + '%');
    }

    function loadApiHealth() {
        callAPI('/api/ApiConfiguration')
            .done(function(apis) {
                var tbody = $('#apiHealthTableBody');
                tbody.empty();

                if (!apis || apis.length === 0) {
                    tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无API配置</td></tr>');
                    return;
                }

                apis.forEach(function(api) {
                    // 模拟健康检查数据
                    var isHealthy = Math.random() > 0.2; // 80%概率健康
                    var responseTime = Math.floor(Math.random() * 500) + 100;
                    var successRate = Math.floor(Math.random() * 20) + 80;

                    var statusBadge = isHealthy
                        ? '<span class="badge bg-success">正常</span>'
                        : '<span class="badge bg-danger">异常</span>';

                    var row = `
                        <tr>
                            <td>${api.name}</td>
                            <td>${statusBadge}</td>
                            <td>${responseTime}ms</td>
                            <td>${new Date().toLocaleString('zh-CN')}</td>
                            <td>${successRate}%</td>
                            <td>${isHealthy ? '-' : '连接超时'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="testApiConnection('${api.id}')">
                                    <i class="fas fa-play"></i> 测试
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            })
            .fail(function() {
                $('#apiHealthTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>');
            });
    }

    function loadRecentErrors() {
        // 模拟最近错误数据
        var errors = [
            { time: '2分钟前', message: 'API连接超时', api: 'UserAPI' },
            { time: '15分钟前', message: '数据解析失败', api: 'OrderAPI' },
            { time: '1小时前', message: '认证失败', api: 'PaymentAPI' }
        ];

        var html = '';
        errors.forEach(function(error) {
            html += `
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="small text-gray-500">${error.time}</div>
                        <div>${error.message}</div>
                        <div class="small text-muted">API: ${error.api}</div>
                    </div>
                </div>
            `;
        });

        if (html === '') {
            html = '<div class="text-center text-muted">暂无错误记录</div>';
        }

        $('#recent-errors').html(html);
    }

    function loadSystemEvents() {
        // 模拟系统事件数据
        var events = [
            { time: '5分钟前', message: '调度器启动', type: 'info' },
            { time: '30分钟前', message: '新增API配置', type: 'success' },
            { time: '2小时前', message: '数据清理完成', type: 'info' }
        ];

        var html = '';
        events.forEach(function(event) {
            var iconClass = event.type === 'success' ? 'fa-check-circle text-success' : 'fa-info-circle text-info';
            html += `
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas ${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="small text-gray-500">${event.time}</div>
                        <div>${event.message}</div>
                    </div>
                </div>
            `;
        });

        if (html === '') {
            html = '<div class="text-center text-muted">暂无系统事件</div>';
        }

        $('#system-events').html(html);
    }

    function loadPerformanceMetrics() {
        // 获取API数据统计
        callAPI('/api/ApiResponseData/statistics')
            .done(function(data) {
                $('#avg-response-time').text(Math.floor(Math.random() * 200) + 100);
                $('#total-requests').text(data.totalRecords || 0);
                $('#data-processed').text(((data.totalDataSize || 0) / (1024 * 1024)).toFixed(2));
                $('#error-rate').text((Math.random() * 5).toFixed(1));
            })
            .fail(function() {
                $('#avg-response-time').text('N/A');
                $('#total-requests').text('N/A');
                $('#data-processed').text('N/A');
                $('#error-rate').text('N/A');
            });
    }

    function initExecutionTrendChart() {
        var ctx = document.getElementById('executionTrendChart').getContext('2d');
        executionTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '成功',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: '失败',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function updateExecutionTrendChart() {
        // 生成模拟数据
        var now = new Date();
        var labels = [];
        var successData = [];
        var failureData = [];

        for (var i = 23; i >= 0; i--) {
            var time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(time.getHours() + ':00');
            successData.push(Math.floor(Math.random() * 50) + 10);
            failureData.push(Math.floor(Math.random() * 10));
        }

        executionTrendChart.data.labels = labels;
        executionTrendChart.data.datasets[0].data = successData;
        executionTrendChart.data.datasets[1].data = failureData;
        executionTrendChart.update();
    }

    function refreshApiHealth() {
        loadApiHealth();
        loadSystemHealthOverview();
        showSuccess('API健康状态已刷新');
    }

    function testApiConnection(apiId) {
        // 使用新的健康检查API
        callAPI('/api/Monitoring/health-check/' + apiId, 'POST')
            .done(function(result) {
                if (result.status === 1) { // HealthStatus.Healthy
                    showSuccess(`API健康检查成功 - 响应时间: ${result.responseTimeMs}ms`);
                } else {
                    showWarning(`API健康检查异常 - 状态: ${getHealthStatusText(result.status)}`);
                }
                loadApiHealth(); // 刷新健康状态
            })
            .fail(function() {
                showError('API健康检查请求失败');
            });
    }

    function loadSystemHealthOverview() {
        callAPI('/api/Monitoring/health-overview')
            .done(function(data) {
                // 更新系统健康概览
                $('#system-status').html(`
                    <span class="text-${data.overallHealthPercentage > 90 ? 'success' : data.overallHealthPercentage > 70 ? 'warning' : 'danger'}">
                        ${data.overallHealthPercentage.toFixed(1)}% 健康
                    </span>
                `);

                // 更新其他统计信息
                updateHealthStats(data);
            })
            .fail(function() {
                $('#system-status').html('<span class="text-danger">获取失败</span>');
            });
    }

    function updateHealthStats(data) {
        // 更新健康统计信息
        var healthyPercentage = data.totalApis > 0 ? (data.healthyApis / data.totalApis * 100).toFixed(1) : 0;
        var avgResponseTime = data.averageResponseTime ? data.averageResponseTime.toFixed(0) : 0;

        // 可以在页面上显示更多统计信息
        console.log('健康API数量:', data.healthyApis);
        console.log('总API数量:', data.totalApis);
        console.log('平均响应时间:', avgResponseTime + 'ms');
    }

    function getHealthStatusText(status) {
        var statusMap = {
            1: '健康',
            2: '警告',
            3: '不健康',
            4: '未知',
            5: '降级'
        };
        return statusMap[status] || '未知';
    }

    function performBatchHealthCheck() {
        // 获取所有API配置ID
        callAPI('/api/ApiConfiguration')
            .done(function(configs) {
                var apiConfigIds = configs.map(c => c.id);

                // 执行批量健康检查
                callAPI('/api/Monitoring/health-check/batch', 'POST', { apiConfigIds: apiConfigIds })
                    .done(function(results) {
                        showSuccess(`批量健康检查完成，检查了 ${results.length} 个API`);
                        loadApiHealth();
                        loadSystemHealthOverview();
                    })
                    .fail(function() {
                        showError('批量健康检查失败');
                    });
            })
            .fail(function() {
                showError('获取API配置失败');
            });
    }

    function refreshChart() {
        updateExecutionTrendChart();
        showSuccess('图表数据已刷新');
    }

    function exportChartData() {
        // 实现图表数据导出功能
        showInfo('图表数据导出功能开发中...');
    }

    function exportMonitoringData() {
        // 导出监控数据
        var startDate = new Date();
        startDate.setDate(startDate.getDate() - 7); // 最近7天
        var endDate = new Date();

        var exportData = {
            exportTime: new Date().toISOString(),
            period: {
                start: startDate.toISOString(),
                end: endDate.toISOString()
            },
            systemHealth: {},
            apiHealth: [],
            performanceMetrics: []
        };

        // 获取系统健康概览
        callAPI('/api/Monitoring/health-overview')
            .done(function(healthData) {
                exportData.systemHealth = healthData;

                // 获取API配置列表
                callAPI('/api/ApiConfiguration')
                    .done(function(configs) {
                        var promises = [];

                        configs.forEach(function(config) {
                            // 获取每个API的健康检查历史
                            var healthPromise = callAPI(`/api/Monitoring/health-check/history/${config.id}?limit=100`)
                                .done(function(healthHistory) {
                                    exportData.apiHealth.push({
                                        apiId: config.id,
                                        apiName: config.name,
                                        healthHistory: healthHistory
                                    });
                                });
                            promises.push(healthPromise);

                            // 获取性能统计
                            var statsPromise = callAPI(`/api/Monitoring/statistics/${config.id}`)
                                .done(function(stats) {
                                    exportData.performanceMetrics.push({
                                        apiId: config.id,
                                        apiName: config.name,
                                        statistics: stats
                                    });
                                });
                            promises.push(statsPromise);
                        });

                        // 等待所有数据获取完成后导出
                        Promise.all(promises).then(function() {
                            downloadJsonFile(exportData, `monitoring_data_${new Date().toISOString().split('T')[0]}.json`);
                            showSuccess('监控数据导出成功');
                        }).catch(function() {
                            showError('部分数据获取失败，导出可能不完整');
                            downloadJsonFile(exportData, `monitoring_data_${new Date().toISOString().split('T')[0]}.json`);
                        });
                    })
                    .fail(function() {
                        showError('获取API配置失败');
                    });
            })
            .fail(function() {
                showError('获取系统健康数据失败');
            });
    }

    function downloadJsonFile(data, filename) {
        var dataStr = JSON.stringify(data, null, 2);
        var dataBlob = new Blob([dataStr], {type: 'application/json'});
        var url = URL.createObjectURL(dataBlob);
        var link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // 页面卸载时清理定时器
    $(window).on('beforeunload', function() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
</script>
}
