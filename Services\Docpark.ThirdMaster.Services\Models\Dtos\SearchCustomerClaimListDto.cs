﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class SearchCustomerClaimListDto
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 单据号
        /// </summary>
        public string BillNo { get; set; }
        /// <summary>
        /// 所属公司
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 创建时间起
        /// </summary>
        public DateTime? StartDateTime { get; set; }
        /// <summary>
        /// 创建时间止
        /// </summary>
        public DateTime? EndDateTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 分页数
        /// </summary>
        [Range(0, int.MaxValue)]
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 分页大小
        /// </summary>
        [Range(0, 1000)]
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 是否导出
        /// </summary>
        public bool IsExport { get; set; } = false;
        /// <summary>
        /// MstId
        /// </summary>
        public List<string> MstIds { get; set; } = new List<string>();

        public List<ShortFieldDto> ShortFields { get; set; } = new List<ShortFieldDto>();
    }
}
