﻿using Docpark.HttpClientExtension.Const;
using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class PageResult<T> : ResultInfo
    {
        public PageResultModel Result { get; set; }

        public class PageResultModel
        {
            /// <summary>
            /// 总记录数
            /// </summary>
            public int TotalCount { get; set; }

            /// <summary>
            /// 分页数据
            /// </summary>
            public List<T> Data { get; set; }
        }

        private PageResult() { }

        /// <summary>
        /// 失败
        /// </summary>
        /// <param name="msg">定义失败信息</param>
        /// <returns></returns>
        public new static PageResult<T> Fail(string msg)
        {
            return new PageResult<T>
            {
                Code = ResultCode.Failed,
                Msg = msg
            };
        }

        /// <summary>
        /// 成功 
        /// </summary>
        /// <param name="data">要返回的数据泛型</param>
        /// <param name="msg">可定义成功信息</param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        public static PageResult<T> Success(List<T> data, string msg, int totalCount)
        {
            if ( totalCount <= 0 )
            {
                return new PageResult<T>
                {
                    Code = ResultCode.Success,
                    Msg = msg,
                    Result = new PageResultModel { TotalCount = 0, Data = data }
                };
            }

            return new PageResult<T>
            {
                Code = ResultCode.Success,
                Msg = msg,
                Result = new PageResultModel { TotalCount = totalCount, Data = data }
            };
        }

        /// <summary>
        /// 成功 
        /// </summary>
        /// <param name="data">要返回的数据泛型</param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        public static PageResult<T> Success(List<T> data, int totalCount)
        {
            return Success(data, string.Empty, totalCount);
        }
    }
}
