﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.Const
{
    public class ConstApi
    {
        public const string User_GetUsersByUserIds_Api = "api/services/app/User/GetUsersByUserIds";

        public const string OrganizationUnit_GetParentOrganizationUnitByUserName = "api/services/app/OrganizationUnit/GetParentOrganizationUnitByUserName?userName={0}";

        public const string OrganizationUnit_GetOrganizationUnitByUserId = "api/services/app/OrganizationUnit/GetOrganizationUnitByUserId?userId={0}";

        public const string DocumentForm_GetShortSummary = "api/services/app/DocumentForm/GetShortSummary?mstId={0}";
        public const string DocumentForm_GetShortSummarys = "api/services/app/DocumentForm/GetShortSummarys";

        public const string AppForm_BatchRemove = "api/services/app/AppForm/BatchRemove";

        public const string DocumentForm_SaveFiles = "api/services/app/DocumentForm/SaveFiles";

        public const string DocumentForm_GetFormDataList = "api/services/app/DocumentForm/GetFormDataList";
        public const string DocumentForm_GetFormDataLists = "api/services/app/DocumentForm/GetFormDataLists";
        public const string DocumentForm_GetFormData = "api/services/app/DocumentForm/GetData?mstId={0}";
        public const string DocumentForm_GetFormDataCount = "api/services/app/DocumentForm/GetFormDataCount";
        public const string DocumentForm_GetFormDataCounts = "api/services/app/DocumentForm/GetFormDataCounts";

        public const string AppPermissionService_GetPermissionBusinessType = "api/services/app/AppPermissionService/GetPermissionBusinessType?userId={0}";

        public const string DocumentForm_GetFields = "api/services/app/DocumentForm/GetFields?documentTypeId={0}";

        public const string DocumentForm_SaveFormData = "api/services/app/DocumentForm/SaveFormData";
        public const string DocumentForm_SaveFormDataList = "api/services/app/DocumentForm/SaveFormDataBatch";

        public const string Relationships_GetObjsByTargetObjInstanceIds = "api/services/app/Relationships/GetObjsByTargetObjInstanceIds";
        public const string Relationships_GetTargetObjInstanceId = "api/services/app/Relationships/GetTargetObjInstanceId?ObjUniqueId={0}";

        public const string Relationships_GetTargetObjInstanceIdList = "api/services/app/Relationships/GetTargetObjInstanceIdList";

        public const string Relationships_RemoveList = "api/services/app/Relationships/RemoveList";

        public const string Relationship_BatchSave = "api/services/app/Relationships/BatchSave";


        public const string DocumentType_GetAll = "documentType/all";

        public const string DocumentType_Get_get_by_identity = "documentType/get_by_identity";

        public const string Relationships_Remove = "api/services/app/Relationships/Remove?ObjUniqueId={0}";

        public const string Relationships_RemoveAll = "api/services/app/Relationships/RemoveAll?ObjId={0}&RelationId={1}&TargetObjInstanceId={2}";
        public const string AppObject_UpdateStatus = "api/services/app/AppObject/UpdateStatus";

        public const string WechatUpdateInvoiceStatus = "api/MobileFolder/UpdateEInvoiceStatus";


        public const string Document_Upload_File = "api/ftp/upload/add/file";

        public const string FormData_GetMstIds = "api/services/app/FormData/GetMstId";
        public const string DocumentForm_GetMstIds = "api/services/app/DocumentForm/GetMstId";
    }
}
