﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Receipts
    {
        /// <summary>
        /// 回单文件base64
        /// </summary>
        public string base64 { get; set; }
    }

    public class Request_EleReceipt_Archive_Data
    {
        /// <summary>
        /// 本方银行账号
        /// </summary>
        public string accountNo { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        public string transDate { get; set; }
        /// <summary>
        /// 支付报销款
        /// </summary>
        public string remarks { get; set; }
        /// <summary>
        /// 借贷标识, DEBIT 借; CREDIT 贷
        /// </summary>
        public string drOrCr { get; set; }
        /// <summary>
        /// 流水号
        /// </summary>
        public string transNo { get; set; }
        /// <summary>
        /// 对方银行账户名
        /// </summary>
        public string opAccountName { get; set; }
        /// <summary>
        /// 对方银行账户号
        /// </summary>
        public string opAccountNo { get; set; }
        /// <summary>
        /// 对方银行开户行
        /// </summary>
        public string opAccountBankName { get; set; }
        /// <summary>
        /// 用途
        /// </summary>
        public string extraRemarks { get; set; }
        /// <summary>
        /// 交易币种
        /// </summary>
        public string transCcy { get; set; }
        /// <summary>
        /// 交易金额
        /// </summary>
        public string transAmtD { get; set; }
        /// <summary>
        /// 回单图片集合
        /// </summary>
        public List<Receipts> receipts { get; set; }
    }
}
