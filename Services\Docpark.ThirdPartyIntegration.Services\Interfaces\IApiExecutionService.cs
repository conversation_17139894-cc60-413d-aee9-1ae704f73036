using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// API执行服务接口
    /// </summary>
    public interface IApiExecutionService
    {
        /// <summary>
        /// 执行API调用
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <returns></returns>
        Task<ApiExecutionResult> ExecuteApiAsync(string apiConfigId);

        /// <summary>
        /// 批量执行API调用
        /// </summary>
        /// <param name="apiConfigIds"></param>
        /// <returns></returns>
        Task<List<ApiExecutionResult>> ExecuteBatchAsync(List<string> apiConfigIds);

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <returns></returns>
        Task<bool> TestApiConnectionAsync(string apiConfigId);

        /// <summary>
        /// 手动执行API调用
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="executedBy"></param>
        /// <returns></returns>
        Task<ApiExecutionResult> ExecuteManuallyAsync(string apiConfigId, string executedBy);
    }
}
