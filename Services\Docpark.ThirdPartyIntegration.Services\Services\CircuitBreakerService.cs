using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 熔断器服务实现
    /// </summary>
    public class CircuitBreakerService : ICircuitBreakerService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly ILogger<CircuitBreakerService> _logger;

        private const string CircuitBreakerConfigCollectionName = "circuit_breaker_configs";

        public CircuitBreakerService(
            IMongoRepository mongoRepository,
            ILogger<CircuitBreakerService> logger)
        {
            _mongoRepository = mongoRepository;
            _logger = logger;
        }

        /// <summary>
        /// 创建熔断器配置
        /// </summary>
        public async Task<string> CreateCircuitBreakerConfigAsync(CircuitBreakerConfig config)
        {
            try
            {
                config.Id = null; // 确保创建新记录
                config.CreatedAt = DateTime.UtcNow;
                config.UpdatedAt = DateTime.UtcNow;

                await _mongoRepository.InsertOneAsync(CircuitBreakerConfigCollectionName, config);
                _logger.LogInformation("创建熔断器配置成功，配置名称: {ConfigName}", config.Name);
                return config.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建熔断器配置失败，配置名称: {ConfigName}", config.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新熔断器配置
        /// </summary>
        public async Task<bool> UpdateCircuitBreakerConfigAsync(string id, CircuitBreakerConfig config)
        {
            try
            {
                config.Id = id;
                config.UpdatedAt = DateTime.UtcNow;

                var filter = Builders<CircuitBreakerConfig>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.ReplaceOneAsync(CircuitBreakerConfigCollectionName, filter, config);
                
                _logger.LogInformation("更新熔断器配置成功，ID: {Id}", id);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新熔断器配置失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取熔断器配置
        /// </summary>
        public async Task<CircuitBreakerConfig> GetCircuitBreakerConfigAsync(string id)
        {
            try
            {
                var filter = Builders<CircuitBreakerConfig>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<CircuitBreakerConfig>(CircuitBreakerConfigCollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器配置失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 根据API配置ID获取熔断器配置
        /// </summary>
        public async Task<CircuitBreakerConfig> GetCircuitBreakerConfigByApiIdAsync(string apiConfigId)
        {
            try
            {
                var filter = Builders<CircuitBreakerConfig>.Filter.Eq(x => x.ApiConfigId, apiConfigId);
                return await _mongoRepository.FindOneAsync<CircuitBreakerConfig>(CircuitBreakerConfigCollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据API配置ID获取熔断器配置失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 获取所有熔断器配置
        /// </summary>
        public async Task<List<CircuitBreakerConfig>> GetAllCircuitBreakerConfigsAsync(bool enabledOnly = false)
        {
            try
            {
                var filterBuilder = Builders<CircuitBreakerConfig>.Filter;
                var filter = enabledOnly ? filterBuilder.Eq(x => x.IsEnabled, true) : filterBuilder.Empty;

                var configs = await _mongoRepository.GetCollection<CircuitBreakerConfig>(CircuitBreakerConfigCollectionName)
                    .Find(filter)
                    .ToListAsync();

                return configs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器配置列表失败");
                throw;
            }
        }

        /// <summary>
        /// 删除熔断器配置
        /// </summary>
        public async Task<bool> DeleteCircuitBreakerConfigAsync(string id)
        {
            try
            {
                var filter = Builders<CircuitBreakerConfig>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.DeleteOneAsync<CircuitBreakerConfig>(CircuitBreakerConfigCollectionName, filter);
                
                _logger.LogInformation("删除熔断器配置成功，ID: {Id}", id);
                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除熔断器配置失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 检查是否允许请求
        /// </summary>
        public async Task<CircuitBreakerResult> IsRequestAllowedAsync(string apiConfigId)
        {
            try
            {
                var config = await GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config == null || !config.IsEnabled)
                {
                    return new CircuitBreakerResult
                    {
                        IsAllowed = true,
                        State = CircuitBreakerState.Closed,
                        Reason = "熔断器未配置或已禁用"
                    };
                }

                // 检查当前状态
                switch (config.CurrentState)
                {
                    case CircuitBreakerState.Open:
                        return await HandleOpenStateAsync(config);
                    case CircuitBreakerState.HalfOpen:
                        return await HandleHalfOpenStateAsync(config);
                    case CircuitBreakerState.Closed:
                    default:
                        return await HandleClosedStateAsync(config);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查熔断器状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                // 发生错误时默认允许请求
                return new CircuitBreakerResult
                {
                    IsAllowed = true,
                    State = CircuitBreakerState.Closed,
                    Reason = "熔断器检查异常，默认允许"
                };
            }
        }

        /// <summary>
        /// 记录请求结果
        /// </summary>
        public async Task RecordRequestResultAsync(string apiConfigId, bool isSuccess, long responseTimeMs)
        {
            try
            {
                var config = await GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config == null || !config.IsEnabled)
                {
                    return;
                }

                if (isSuccess)
                {
                    config.SuccessCount++;
                    
                    // 如果是半开状态且成功次数达到阈值，则关闭熔断器
                    if (config.CurrentState == CircuitBreakerState.HalfOpen && 
                        config.SuccessCount >= config.SuccessThreshold)
                    {
                        await CloseCircuitBreakerAsync(apiConfigId, "成功次数达到阈值");
                    }
                }
                else
                {
                    config.FailureCount++;
                    
                    // 检查是否需要打开熔断器
                    if (config.CurrentState == CircuitBreakerState.Closed &&
                        config.FailureCount >= config.FailureThreshold)
                    {
                        await OpenCircuitBreakerAsync(apiConfigId, "失败次数达到阈值");
                    }
                    else if (config.CurrentState == CircuitBreakerState.HalfOpen)
                    {
                        await OpenCircuitBreakerAsync(apiConfigId, "半开状态下请求失败");
                    }
                }

                // 更新配置
                await UpdateCircuitBreakerConfigAsync(config.Id, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录请求结果失败，API配置ID: {ApiConfigId}", apiConfigId);
            }
        }

        /// <summary>
        /// 手动打开熔断器
        /// </summary>
        public async Task<bool> OpenCircuitBreakerAsync(string apiConfigId, string reason = null)
        {
            try
            {
                var config = await GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config == null)
                {
                    return false;
                }

                config.CurrentState = CircuitBreakerState.Open;
                config.LastStateChangeTime = DateTime.UtcNow;
                config.FailureCount = 0; // 重置计数器
                config.SuccessCount = 0;

                await UpdateCircuitBreakerConfigAsync(config.Id, config);
                _logger.LogWarning("熔断器已打开，API配置ID: {ApiConfigId}, 原因: {Reason}", apiConfigId, reason);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开熔断器失败，API配置ID: {ApiConfigId}", apiConfigId);
                return false;
            }
        }

        /// <summary>
        /// 手动关闭熔断器
        /// </summary>
        public async Task<bool> CloseCircuitBreakerAsync(string apiConfigId, string reason = null)
        {
            try
            {
                var config = await GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config == null)
                {
                    return false;
                }

                config.CurrentState = CircuitBreakerState.Closed;
                config.LastStateChangeTime = DateTime.UtcNow;
                config.FailureCount = 0; // 重置计数器
                config.SuccessCount = 0;

                await UpdateCircuitBreakerConfigAsync(config.Id, config);
                _logger.LogInformation("熔断器已关闭，API配置ID: {ApiConfigId}, 原因: {Reason}", apiConfigId, reason);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭熔断器失败，API配置ID: {ApiConfigId}", apiConfigId);
                return false;
            }
        }

        /// <summary>
        /// 重置熔断器状态
        /// </summary>
        public async Task<bool> ResetCircuitBreakerAsync(string apiConfigId)
        {
            try
            {
                return await CloseCircuitBreakerAsync(apiConfigId, "手动重置");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置熔断器失败，API配置ID: {ApiConfigId}", apiConfigId);
                return false;
            }
        }

        /// <summary>
        /// 获取熔断器状态
        /// </summary>
        public async Task<object> GetCircuitBreakerStatusAsync(string apiConfigId)
        {
            try
            {
                var config = await GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config == null)
                {
                    return new { Status = "未配置" };
                }

                return new
                {
                    ApiConfigId = apiConfigId,
                    State = config.CurrentState.ToString(),
                    IsEnabled = config.IsEnabled,
                    FailureCount = config.FailureCount,
                    SuccessCount = config.SuccessCount,
                    FailureThreshold = config.FailureThreshold,
                    SuccessThreshold = config.SuccessThreshold,
                    LastStateChangeTime = config.LastStateChangeTime,
                    CheckTime = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 获取熔断器统计信息
        /// </summary>
        public async Task<object> GetCircuitBreakerStatisticsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                // 这里实现统计逻辑
                var statistics = new
                {
                    TotalConfigs = await GetTotalConfigsCountAsync(apiConfigId),
                    OpenConfigs = await GetOpenConfigsCountAsync(apiConfigId),
                    HalfOpenConfigs = await GetHalfOpenConfigsCountAsync(apiConfigId),
                    ClosedConfigs = await GetClosedConfigsCountAsync(apiConfigId)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器统计信息失败");
                throw;
            }
        }

        /// <summary>
        /// 执行带熔断保护的请求
        /// </summary>
        public async Task<T> ExecuteWithCircuitBreakerAsync<T>(string apiConfigId, Func<Task<T>> operation, Func<Task<T>> fallback = null)
        {
            var result = await IsRequestAllowedAsync(apiConfigId);
            
            if (!result.IsAllowed)
            {
                if (fallback != null)
                {
                    return await fallback();
                }
                throw new InvalidOperationException($"熔断器阻止请求: {result.Reason}");
            }

            try
            {
                var startTime = DateTime.UtcNow;
                var operationResult = await operation();
                var responseTime = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
                
                await RecordRequestResultAsync(apiConfigId, true, responseTime);
                return operationResult;
            }
            catch (Exception ex)
            {
                await RecordRequestResultAsync(apiConfigId, false, 0);
                throw;
            }
        }

        #region 私有辅助方法

        private async Task<CircuitBreakerResult> HandleOpenStateAsync(CircuitBreakerConfig config)
        {
            // 检查是否可以转为半开状态
            if (config.LastStateChangeTime.HasValue &&
                DateTime.UtcNow.Subtract(config.LastStateChangeTime.Value).TotalSeconds >= config.OpenDurationSeconds)
            {
                // 转为半开状态
                config.CurrentState = CircuitBreakerState.HalfOpen;
                config.LastStateChangeTime = DateTime.UtcNow;
                await UpdateCircuitBreakerConfigAsync(config.Id, config);

                return new CircuitBreakerResult
                {
                    IsAllowed = true,
                    State = CircuitBreakerState.HalfOpen,
                    Reason = "转为半开状态，允许试探请求"
                };
            }

            return new CircuitBreakerResult
            {
                IsAllowed = false,
                State = CircuitBreakerState.Open,
                Reason = "熔断器处于开启状态",
                NextRetryTime = config.LastStateChangeTime?.AddSeconds(config.OpenDurationSeconds)
            };
        }

        private async Task<CircuitBreakerResult> HandleHalfOpenStateAsync(CircuitBreakerConfig config)
        {
            // 半开状态下限制并发请求数
            return new CircuitBreakerResult
            {
                IsAllowed = true,
                State = CircuitBreakerState.HalfOpen,
                Reason = "半开状态，允许试探请求"
            };
        }

        private async Task<CircuitBreakerResult> HandleClosedStateAsync(CircuitBreakerConfig config)
        {
            return new CircuitBreakerResult
            {
                IsAllowed = true,
                State = CircuitBreakerState.Closed,
                Reason = "熔断器关闭，正常处理请求"
            };
        }

        // 统计相关的辅助方法
        private async Task<int> GetTotalConfigsCountAsync(string apiConfigId) => 0; // 实现统计逻辑
        private async Task<int> GetOpenConfigsCountAsync(string apiConfigId) => 0;
        private async Task<int> GetHalfOpenConfigsCountAsync(string apiConfigId) => 0;
        private async Task<int> GetClosedConfigsCountAsync(string apiConfigId) => 0;

        #endregion
    }
}
