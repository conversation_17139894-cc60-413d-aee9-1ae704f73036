﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class DocumentFileDto
    {
        /// <summary>
        /// 非图片类型的预览IFrame url
        /// </summary>
        public string Url { get; set; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 总共多少页
        /// </summary>
        public int? PageCount { get; set; }

        public string DocumentId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string LinkType { get; set; }

        public List<string> Base64s { get; set; }
    }
}
