﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class MetadataDto
    {
        public string Token { get; set; }

        public string AppKey { get; set; }

        public string AppSecret { get; set; }

        public string Timestamp { get; set; }
    }

    public class UploadDto : MetadataDto
    {
        public string FileName { get; set; }
        public string FileStream { get; set; }
        public string FileType { get; set; }
        public string DocTypeID { get; set; }
        public string DocumentNo { get; set; }
        public string ClientNo { get; set; }
        public string FileLabel { get; set; }
    }

    public class VoucherFlowInputDto : MetadataDto
    { 
        public List<VoucherFlowDto> Data { get; set; }
    }

    public class VoucherFlowDto 
    {
        public string ParentDocTypeID { get; set; }
        public string ParentArchivesKey { get; set; }
        public string DocTypeID { get; set; }
        public string ArchivesKey { get; set; }
    }
}