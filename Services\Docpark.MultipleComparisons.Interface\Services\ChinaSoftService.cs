﻿using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Models.ChinaSoftware;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class ChinaSoftService : IChinaSoftService
    {
        private readonly IComHttpClient _httpClient;

        private readonly ILogger<ChinaSoftService> _logger;
        private readonly IConfiguration configuration;

        public ChinaSoftService(IComHttpClient httpClient,
            ILogger<ChinaSoftService> logger, IConfiguration configuration)
        {
            this._httpClient = httpClient;

            this._logger = logger;
            this.configuration = configuration;
        }
        public async Task<SyncOutputDto> Synchronous(SyncInputDto dto)
        {
            try
            {
                var resStatus = ValidToken(dto.extra.timestamp, dto.extra.Token);
                if (resStatus == 0)
                    return new SyncOutputDto
                    {
                        code = "9999",
                        msg = "token已过期",
                        uuid = dto.uuid
                    };
                if (resStatus == 1)
                    return new SyncOutputDto
                    {
                        code = "8888",
                        msg = "token不正确",
                        uuid = dto.uuid
                    };
                var userList = Get_Person(dto.data);
                var strResult = await this._httpClient.SyncThirdpartyUserAsync(userList);
                if (!string.IsNullOrEmpty(strResult))
                {
                    var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                    if (payload != null && Convert.ToBoolean(payload["success"]))
                    {
                        var errMsgResult = JsonConvert.DeserializeObject<Dictionary<string, string>>(payload["result"].ToString());
                        if (errMsgResult.Count > 0)
                            return new SyncOutputDto
                            {
                                code = "-1",
                                msg = errMsgResult.FirstOrDefault().Value,
                                uuid = dto.uuid
                            };
                        else
                            return new SyncOutputDto
                            {
                                code = "0",
                                uuid = dto.uuid,
                                msg = "成功"
                            };
                    }
                    else
                    {
                        var Message = !string.IsNullOrEmpty(payload["error"].ToString()) ? ((JObject)((JObject)payload)["error"])["message"].ToString() : "同步第三方组织机构用户信息失败！";
                        this._logger.LogInformation($"{Message}");
                        return new SyncOutputDto
                        {
                            code = "-1",
                            msg = Message,
                            uuid = dto.uuid
                        };
                    }
                }
                else
                {
                    this._logger.LogInformation($"同步第三方组织机构用户信息失败！");

                    return new SyncOutputDto
                    {
                        code = "-1",
                        msg = "同步第三方组织机构用户信息失败",
                        uuid = dto.uuid
                    };
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError($"message:{ex.Message}.Stack:{ex.StackTrace}");
                return new SyncOutputDto
                {
                    code = "-1",
                    msg = "同步失败,请重试",
                    uuid = dto.uuid
                };
            }

        }


        List<Model_ThirdpartyCreateUser> Get_Person(SyncCommonData data)
        {
            //var user_info = JObject.Parse(data.userjson);
            //var password = user_info.SelectToken("password").ToString();
            //var name = user_info.SelectToken("USER_NAME").ToString();

            var email = string.IsNullOrEmpty(data.sjh) ? DateTime.Now.Ticks.ToString() : data.sjh;
            var list_SaveUser = new List<Model_ThirdpartyCreateUser>{new Model_ThirdpartyCreateUser()
            {
                AssignedRoleNames = new string[] { "大象慧云" },
                OrganizationUnits = new List<long>() {  2},
                SendActivationEmail = false,
                SetRandomPassword = false,
                AuthProvider = "SignatureLogin", // 第三方平台提供者
                ProviderKey = data.yhuuid, // 第三方用户ID
                User = new Model_CreateUserDto()
                {
                    //UserName =data.yhm,
                    UserName=data.yhid,
                    Password = "",
                    Name = data.yhm,
                    EmailAddress = $"{email}@test.com",
                    PhoneNumber = data.sjh,
                    ShouldChangePasswordOnNextLogin = true,
                    IsActive = true,
                    IsLockoutEnabled = true,
                    IsTwoFactorEnabled = true,
                    Surname = "",
                    Id = 0
                }
            }
            };
            return list_SaveUser;
        }

        #region 验真token工具

        private int ValidToken(string timestamp, string token)
        {
            var time = GetTimeStampToSeconds(timestamp);
            if (time > 30)
                return 0;
            var appKeyStr = configuration.GetValue<string>("Authentication:AppKey");
            var appSecret = configuration.GetValue<string>("Authentication:AppSecret");
            string tokenStr = Md5Str($"{appKeyStr}+{timestamp}+{appSecret}");
            _logger.LogInformation($"签名验证：token={tokenStr} = signature={token}");
            if (token.ToUpper() != tokenStr.ToUpper())
                return 1;
            return 2;
        }

        private string Md5Str(string param)
        {
            byte[] bt = Encoding.UTF8.GetBytes(param);
            //创建默认实现的实例
            var md5 = MD5.Create();
            //计算指定字节数组的哈希值。
            var md5bt = md5.ComputeHash(bt);
            //将byte数组转换为字符串
            StringBuilder builder = new StringBuilder();
            foreach (var item in md5bt)
            {
                builder.Append(item.ToString("X2"));
            }
            string md5Str = builder.ToString();
            return builder.ToString();
        }

        private double GetTimeStampToSeconds(string timestamp, DateTime? dateTimeNow = null)
        {
            var param = StampToDateTime(timestamp);
            if (dateTimeNow != null)
            {
                var result = dateTimeNow.Value.Subtract(param).TotalMinutes;
                return result;
            }
            else
            {
                var dateTimeNow1 = DateTime.Now;
                var result = dateTimeNow1.Subtract(param).TotalMinutes;
                return result;
            }
        }

        private DateTime StampToDateTime(string timestamp)
        {
            DateTime time = DateTime.MinValue;
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            if (timestamp.Length == 10)        //精确到秒
            {
                time = startTime.AddSeconds(double.Parse(timestamp));
            }
            else if (timestamp.Length == 13)   //精确到毫秒
            {
                time = startTime.AddMilliseconds(double.Parse(timestamp));
            }
            return time;
        }
        #endregion
    }
}