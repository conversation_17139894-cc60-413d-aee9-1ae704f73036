using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 响应处理服务接口
    /// </summary>
    public interface IResponseProcessingService
    {
        /// <summary>
        /// 处理响应数据
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <param name="processingConfig">处理配置</param>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>处理结果</returns>
        Task<ResponseProcessingResult> ProcessResponseAsync(object responseData, ResponseProcessingConfig processingConfig, string apiConfigId);

        /// <summary>
        /// 数据去重
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="deduplicationConfig">去重配置</param>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>去重后的数据</returns>
        Task<List<object>> DeduplicateDataAsync(List<object> data, DeduplicationConfig deduplicationConfig, string apiConfigId);

        /// <summary>
        /// 数据过滤
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="filterConfig">过滤配置</param>
        /// <returns>过滤后的数据</returns>
        Task<List<object>> FilterDataAsync(List<object> data, DataFilterConfig filterConfig);

        /// <summary>
        /// 数据聚合
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="aggregationConfig">聚合配置</param>
        /// <returns>聚合后的数据</returns>
        Task<List<object>> AggregateDataAsync(List<object> data, DataAggregationConfig aggregationConfig);

        /// <summary>
        /// 数据验证
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="validationConfig">验证配置</param>
        /// <returns>验证结果</returns>
        Task<DataValidationResult> ValidateDataAsync(List<object> data, DataValidationConfig validationConfig);

        /// <summary>
        /// 批处理数据
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="batchConfig">批处理配置</param>
        /// <param name="processor">处理器函数</param>
        /// <returns>处理结果</returns>
        Task<BatchProcessingResult> ProcessInBatchesAsync<T>(List<T> data, BatchProcessingConfig batchConfig, Func<List<T>, Task<bool>> processor);

        /// <summary>
        /// 计算数据哈希值
        /// </summary>
        /// <param name="data">数据对象</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>哈希值</returns>
        Task<string> ComputeDataHashAsync(object data, string algorithm = "SHA256");

        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        /// <param name="dataHash">数据哈希值</param>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="scopeDays">检查范围（天数）</param>
        /// <returns>是否重复</returns>
        Task<bool> IsDataDuplicateAsync(string dataHash, string apiConfigId, int scopeDays);
    }

    /// <summary>
    /// 响应处理结果
    /// </summary>
    public class ResponseProcessingResult
    {
        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 处理后的数据
        /// </summary>
        public object ProcessedData { get; set; }

        /// <summary>
        /// 原始数据
        /// </summary>
        public object OriginalData { get; set; }

        /// <summary>
        /// 处理统计信息
        /// </summary>
        public ProcessingStatistics Statistics { get; set; }

        /// <summary>
        /// 处理过程中的错误
        /// </summary>
        public List<ProcessingError> Errors { get; set; } = new List<ProcessingError>();

        /// <summary>
        /// 处理过程中的警告
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 处理统计信息
    /// </summary>
    public class ProcessingStatistics
    {
        /// <summary>
        /// 原始记录数
        /// </summary>
        public int OriginalRecordCount { get; set; }

        /// <summary>
        /// 处理后记录数
        /// </summary>
        public int ProcessedRecordCount { get; set; }

        /// <summary>
        /// 去重移除的记录数
        /// </summary>
        public int DuplicateRecordsRemoved { get; set; }

        /// <summary>
        /// 过滤移除的记录数
        /// </summary>
        public int FilteredRecordsRemoved { get; set; }

        /// <summary>
        /// 验证失败的记录数
        /// </summary>
        public int ValidationFailedRecords { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 各阶段处理时间
        /// </summary>
        public Dictionary<string, long> StageTimings { get; set; } = new Dictionary<string, long>();
    }

    /// <summary>
    /// 处理错误
    /// </summary>
    public class ProcessingError
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public ProcessingErrorType ErrorType { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 相关数据
        /// </summary>
        public object RelatedData { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public string ExceptionDetails { get; set; }

        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 数据验证结果
    /// </summary>
    public class DataValidationResult
    {
        /// <summary>
        /// 是否全部通过验证
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 通过验证的记录数
        /// </summary>
        public int ValidRecordCount { get; set; }

        /// <summary>
        /// 验证失败的记录数
        /// </summary>
        public int InvalidRecordCount { get; set; }

        /// <summary>
        /// 验证错误详情
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new List<ValidationError>();

        /// <summary>
        /// 通过验证的数据
        /// </summary>
        public List<object> ValidData { get; set; } = new List<object>();

        /// <summary>
        /// 验证失败的数据
        /// </summary>
        public List<object> InvalidData { get; set; } = new List<object>();
    }

    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// 记录索引
        /// </summary>
        public int RecordIndex { get; set; }

        /// <summary>
        /// 字段路径
        /// </summary>
        public string FieldPath { get; set; }

        /// <summary>
        /// 验证规则
        /// </summary>
        public string ValidationRule { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 实际值
        /// </summary>
        public object ActualValue { get; set; }

        /// <summary>
        /// 期望值或格式
        /// </summary>
        public string ExpectedValue { get; set; }
    }

    /// <summary>
    /// 批处理结果
    /// </summary>
    public class BatchProcessingResult
    {
        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 总批次数
        /// </summary>
        public int TotalBatches { get; set; }

        /// <summary>
        /// 成功处理的批次数
        /// </summary>
        public int SuccessfulBatches { get; set; }

        /// <summary>
        /// 失败的批次数
        /// </summary>
        public int FailedBatches { get; set; }

        /// <summary>
        /// 总处理记录数
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 成功处理的记录数
        /// </summary>
        public int SuccessfulRecords { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 批处理错误
        /// </summary>
        public List<BatchError> Errors { get; set; } = new List<BatchError>();
    }

    /// <summary>
    /// 批处理错误
    /// </summary>
    public class BatchError
    {
        /// <summary>
        /// 批次索引
        /// </summary>
        public int BatchIndex { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public string ExceptionDetails { get; set; }

        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 处理错误类型
    /// </summary>
    public enum ProcessingErrorType
    {
        /// <summary>
        /// 去重错误
        /// </summary>
        DeduplicationError = 0,

        /// <summary>
        /// 过滤错误
        /// </summary>
        FilterError = 1,

        /// <summary>
        /// 聚合错误
        /// </summary>
        AggregationError = 2,

        /// <summary>
        /// 验证错误
        /// </summary>
        ValidationError = 3,

        /// <summary>
        /// 批处理错误
        /// </summary>
        BatchProcessingError = 4,

        /// <summary>
        /// 数据转换错误
        /// </summary>
        DataTransformationError = 5,

        /// <summary>
        /// 配置错误
        /// </summary>
        ConfigurationError = 6,

        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown = 99
    }
}
