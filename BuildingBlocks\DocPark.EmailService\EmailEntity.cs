﻿using System;
using System.Collections.Generic;

namespace DocPark.EmailService
{
    public class EmailEntity
    {
        /// <summary>
        /// 邮件UniqueId
        /// </summary>
        public string UniqueId { get; set; }
        /// <summary>
        /// 发件人姓名
        /// </summary>
        public string SendlName { get; set; }
        /// <summary>
        /// 发件人地址
        /// </summary>
        public string SendAddress { get; set; }
        /// <summary>
        /// Subject/主题
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime Date { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<Attachments> Attachments { get; set; }
    }
    public class Attachments
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
    }
}
