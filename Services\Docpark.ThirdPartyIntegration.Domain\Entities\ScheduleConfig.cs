using System;
using System.ComponentModel.DataAnnotations;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 调度配置实体
    /// </summary>
    public class ScheduleConfig
    {
        /// <summary>
        /// 调度类型
        /// </summary>
        [Required]
        public ScheduleType Type { get; set; }

        /// <summary>
        /// 间隔时间（分钟）
        /// </summary>
        public int IntervalMinutes { get; set; }

        /// <summary>
        /// 每日执行时间
        /// </summary>
        public TimeSpan? DailyExecutionTime { get; set; }

        /// <summary>
        /// Cron表达式
        /// </summary>
        [StringLength(100)]
        public string CronExpression { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextExecutionTime { get; set; }

        /// <summary>
        /// 上次执行时间
        /// </summary>
        public DateTime? LastExecutionTime { get; set; }

        /// <summary>
        /// 任务优先级（1-10，数字越大优先级越高）
        /// </summary>
        public int Priority { get; set; } = 5;

        /// <summary>
        /// 任务超时时间（分钟）
        /// </summary>
        public int TimeoutMinutes { get; set; } = 30;

        /// <summary>
        /// 失败重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（分钟）
        /// </summary>
        public int RetryIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// 是否允许并发执行
        /// </summary>
        public bool AllowConcurrentExecution { get; set; } = false;

        /// <summary>
        /// 任务分组
        /// </summary>
        [StringLength(50)]
        public string JobGroup { get; set; } = "DEFAULT";

        /// <summary>
        /// 任务描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 时区信息
        /// </summary>
        [StringLength(50)]
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// 错失触发策略
        /// </summary>
        public MisfireInstruction MisfireInstruction { get; set; } = MisfireInstruction.SmartPolicy;

        /// <summary>
        /// 任务数据（JSON格式）
        /// </summary>
        public string JobData { get; set; }

        /// <summary>
        /// 最大执行次数（0表示无限制）
        /// </summary>
        public int MaxExecutionCount { get; set; } = 0;

        /// <summary>
        /// 已执行次数
        /// </summary>
        public int ExecutionCount { get; set; } = 0;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 错失触发策略
    /// </summary>
    public enum MisfireInstruction
    {
        /// <summary>
        /// 智能策略（默认）
        /// </summary>
        SmartPolicy = 0,

        /// <summary>
        /// 忽略错失的触发
        /// </summary>
        IgnoreMisfires = 1,

        /// <summary>
        /// 立即执行一次
        /// </summary>
        FireOnceNow = 2,

        /// <summary>
        /// 不执行
        /// </summary>
        DoNothing = 3
    }
}
