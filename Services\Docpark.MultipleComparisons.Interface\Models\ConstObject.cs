﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class ConstObject
    {

        /// <summary>
        /// 文件与元素据的关系
        /// </summary>
        public const string DocumentAndMetaDataRelationships = "METADATA";
        /// <summary>
        /// 文件与业务类型的关系
        /// </summary>
        public const string BusinessAndDocumentRelationships = "DOCUMENT";

        /// <summary>
        /// 影像文件与元素据的关系
        /// </summary>
        public const string DocumentAndScanMetaDataRelationships = "SCANMETADATA";
        /// <summary>
        /// 影像文件与业务类型的关系
        /// </summary>
        public const string BusinessAndScanDocumentRelationships = "SCANDOCUMENT";

        /// <summary>
        /// 文件与附件的关系
        /// </summary>
        public const string Document_Attachment = "Attachment";
    }
}
