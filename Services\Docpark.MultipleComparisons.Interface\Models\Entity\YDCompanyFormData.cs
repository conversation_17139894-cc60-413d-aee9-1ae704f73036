﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    /// <summary>
    /// 远东企业信息表单       
    /// </summary>
    [JsonTable("YDCompanyFormData")]
    public class YDCompanyFormData
    {
        /// <summary>
        /// 企业名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 企业税号
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 企业key
        /// </summary>
        public string appKey { get; set; }
        /// <summary>
        /// 企业密钥
        /// </summary>
        public string appSecret { get; set; }
    }

}
