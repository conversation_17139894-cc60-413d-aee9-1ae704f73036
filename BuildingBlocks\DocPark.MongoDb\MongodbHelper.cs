﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace DocPark.MongoDb
{
    public partial class MongodbManager
    {
        /// <summary>
        /// 获取MongoCollection
        /// </summary>
        /// <returns></returns>
        public IMongoCollection<TEntity> GetCollection<TEntity>()
        {
            string collectionName = typeof(TEntity).Name;
            var collection = GetCollection<TEntity>(collectionName);
            return collection;
        }

        /// <summary>
        /// 获取延迟加载结果集Queryable
        /// </summary>
        /// <returns></returns>
        public IQueryable<TEntity> GetQueryable<TEntity>()
        {
            return GetCollection<TEntity>().AsQueryable().AsQueryable();
        }

        /// <summary>
        /// 根据条件查询获取第一条数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public TEntity GetEntity<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null)
        {
            var mongoQueryable = GetCollection<TEntity>().AsQueryable();
            if (filter != null)
            {
                mongoQueryable = mongoQueryable.Where(filter);
            }

            if (orderBy != null)
            {
                return orderBy(mongoQueryable).FirstOrDefault();
            }

            return IAsyncCursorSourceExtensions.FirstOrDefault(mongoQueryable);
        }

        /// <summary>
        /// 根据条件查询数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public List<TEntity> GetList<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null)
        {
            var mongoQueryable = GetCollection<TEntity>().AsQueryable();
            if (filter != null)
            {
                mongoQueryable = mongoQueryable.Where(filter);
            }

            if (orderBy != null)
            {
                return orderBy(mongoQueryable).ToList();
            }

            return IAsyncCursorSourceExtensions.ToList(mongoQueryable);
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public PageInfo<TEntity> GetPageList<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, int pageIndex = 1, int pageSize = 10)
        {
            var mongoQueryable = GetCollection<TEntity>().AsQueryable(new AggregateOptions { AllowDiskUse = true });
            if (filter != null)
            {
                mongoQueryable = mongoQueryable.Where(filter);
            }

            int totalCount = mongoQueryable.Count();
            pageIndex = pageIndex <= 0 ? 1 : pageIndex;
            pageSize = pageSize <= 0 ? 10 : pageSize;

            List<TEntity> data;
            if (orderBy != null)
            {
                data = orderBy(mongoQueryable).Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
                return new PageInfo<TEntity> { TotalCount = totalCount, Data = data };
            }

            data = mongoQueryable.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
            return new PageInfo<TEntity> { TotalCount = totalCount, Data = data };
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="queryable"></param>
        /// <param name="orderBy"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public PageInfo<TEntity> GetPageListByQueryable<TEntity>(IQueryable<TEntity> queryable, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, int pageIndex = 1, int pageSize = 10)
        {
            int totalCount = queryable.Count();
            pageIndex = pageIndex <= 0 ? 1 : pageIndex;
            pageSize = pageSize <= 0 ? 10 : pageSize;

            List<TEntity> data;
            if (orderBy != null)
            {
                data = orderBy(queryable).Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
                return new PageInfo<TEntity> { TotalCount = totalCount, Data = data };
            }

            data = queryable.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
            return new PageInfo<TEntity> { TotalCount = totalCount, Data = data };
        }

        /// <summary>
        /// 根据条件获取记录数
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<int> Count<TEntity>(Expression<Func<TEntity, bool>> filter = null)
        {
            var mongoQueryable = GetCollection<TEntity>().AsQueryable();
            if (filter != null)
            {
                return await mongoQueryable.CountAsync(filter);
            }

            return await mongoQueryable.CountAsync();
        }

        /// <summary>
        /// 添加数据
        /// </summary>
        /// <param name="entity"></param>
        public async Task<bool> Insert<TEntity>(TEntity entity)
        {
            if (entity == null) return false;
            try
            {
                await GetCollection<TEntity>().InsertOneAsync(entity);
            }
            catch (Exception ex)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 批量添加数据
        /// </summary>
        /// <param name="entitys"></param>
        public async Task<bool> BulkCopy<TEntity>(IEnumerable<TEntity> entitys)
        {
            if (entitys == null) return false;
            try
            {
                await GetCollection<TEntity>().InsertManyAsync(entitys);
            }
            catch (Exception ex)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="entity"></param>
        public async Task<bool> Update<TEntity>(Expression<Func<TEntity, bool>> filter, TEntity entity)
        {
            if (entity == null) return false;
            try
            {
                var updateList = BuildUpdateDefinition(entity);
                await GetCollection<TEntity>().UpdateOneAsync(filter, updateList);
            }
            catch (Exception ex)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 删除文档   
        /// </summary>
        /// <param name="filter"></param>
        public async Task<bool> Detele<TEntity>(Expression<Func<TEntity, bool>> filter = null)
        {
            try
            {
                if (filter == null)
                {
                    await GetCollection<TEntity>().DeleteManyAsync(p => true);
                }
                else
                {
                    await GetCollection<TEntity>().DeleteManyAsync(filter);
                }
            }
            catch (Exception ex)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 利用反射创建更新字段(这里没有处理空)
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        private UpdateDefinition<TEntity> BuildUpdateDefinition<TEntity>(TEntity entity)
        {
            var update = Builders<TEntity>.Update;
            var updateList = new List<UpdateDefinition<TEntity>>();

            foreach (PropertyInfo info in entity.GetType().GetProperties())
            {
                var value = info.GetValue(entity);
                if (value != null)
                {
                    updateList.Add(update.Set(info.Name, info.GetValue(entity)));
                }
            }
            return update.Combine(updateList);
        }
    }

    /// <summary>
    /// 分页
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PageInfo<T>
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 分页数据
        /// </summary>
        public List<T> Data { get; set; }
    }
}
