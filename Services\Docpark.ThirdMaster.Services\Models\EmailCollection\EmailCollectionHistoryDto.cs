﻿using System.ComponentModel.DataAnnotations;
using System;

namespace Docpark.ThirdMaster.Services.Models.EmailCollection
{
    public class EmailCollectionHistoryDto
    {
        public string From { get; set; }
        public string SubTitle { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        [Required]
        public int PageIndex { get; set; } = 1;
        [Required]
        public int PageSize { get; set; } = 10;
    }
}
