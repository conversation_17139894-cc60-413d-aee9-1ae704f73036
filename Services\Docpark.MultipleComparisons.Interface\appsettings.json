{"timeCycle": 2, "thirdparty_org": {"auth_provider": "UFIDA_Program", "org_api": "http://113.204.129.98:9081/api/sys-organization/sysSynchroGetOrg/getUpdatedElements", "rec_person_count": 999}, "formal_ele_archives_settings": {"formal_token": {"formal_api": "http://***********:8080/open/auth/login", "formal_appCode": "yddl", "formal_appSecret": "0ljHPATnuNLyJZpCzIYjS2Ms"}, "formal_oa_bill_archive_url": "http://**********:18652/open/customData/61039c58ded26808a5b63ad9", "formal_oa_invoice_archive_url": "http://**********:18652/open/ecm-invoices/upsert", "formal_voucher_archive_url": "http://**********:18652/open/voucher/v2/", "formal_ele_receipt_archive_url": "http://**********:18652/open/trans/", "formal_voucher2bill_archive_url": "http://**********:18652/open/relation/v2/voucher/custom/61039c58ded26808a5b63ad9", "formal_voucher2invoice_archive_url": "http://**********:18652/open/relation/v2/voucher/maycur", "formal_voucher2batch_archive_url": "http://**********:18652/open/relation/v2/voucher/trans"}, "thirdparty_bill_callback_apis": "http://**********:12580/api/imaging/fsscOurImageService/updateBills", "thirdticketholderUrl": "http://bate-ea-app.gooneymc.com:9592/", "thirdticketpriviewUrl": "http://docpark.gooneymc.com:9502/", "thirdparty_img_preview_host": "http://docpark.gooneymc.com:8002/", "getToken_url": "http://lanfk.fegroup.cn:12580/api/imaging/fsscGetTokenService/getToken", "bill_init_status": "01", "Identification_url": "https://sdk.nuonuo.com/open/v1/services", "method_url": "nuonuo.electronInvoice.invoiceInspection", "fk_verify_url": "http://lanfk.fegroup.cn:12580/api/imaging/fsscInvoiceCheckService/checkInvoices", "MongoDBConnectionString": "mongodb://127.0.0.1:27017", "MongoDBDataBaseName": "docpark_datafactorydb", "IdentityUrl": "http://bate-v1-host.gooneymc.com/", "Grpc_Host": "http://bate-v1-host.gooneymc.com:5001", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Serilog": {"elkServerUrl": "http://localhost:9200", "indexFormat": "docpark_MultipleCom_service", "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information"}}}, "ArchivesBaseUrl": "http://bate-ea-app.gooneymc.com:9594/", "SendArchivesInfoUrl": "api/FeatureAccountingArchives/SendArchivesInfo", "SendVoucherFlowUrl": "api/FeatureAccountingArchives/SendVoucherFlow", "VoucherAppKey": "f7b39bc4bc3c11eb826c0242c0a80003", "VoucherAppSecret": "d13d38c5bc3c11eb826c0242c0a80003", "UploadFileURL": "api/FeatureAccountingArchives/UploadArchivesFile", "PdfApiBaseUrl": "http://**************:8050/", "Authentication": {"AppKey": "CJxerZAH", "AppSecret": "y23hGs6GwiQ0t0eBTZmbPC4pNinXnpjj"}}