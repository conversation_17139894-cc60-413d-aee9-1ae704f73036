using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 数据同步配置实体
    /// </summary>
    public class SyncConfiguration
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 同步策略
        /// </summary>
        [Required]
        public SyncStrategy Strategy { get; set; }

        /// <summary>
        /// 冲突解决策略
        /// </summary>
        [Required]
        public ConflictResolutionStrategy ConflictResolution { get; set; }

        /// <summary>
        /// 是否启用增量同步
        /// </summary>
        public bool EnableIncrementalSync { get; set; } = true;

        /// <summary>
        /// 增量同步字段名（如：updated_at, modified_time等）
        /// </summary>
        [StringLength(100)]
        public string IncrementalField { get; set; }

        /// <summary>
        /// 数据唯一标识字段（用于去重和冲突检测）
        /// </summary>
        [StringLength(100)]
        public string UniqueIdentifierField { get; set; }

        /// <summary>
        /// 版本字段名（用于版本控制）
        /// </summary>
        [StringLength(100)]
        public string VersionField { get; set; }

        /// <summary>
        /// 检查点字段名（用于断点续传）
        /// </summary>
        [StringLength(100)]
        public string CheckpointField { get; set; }

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（秒）
        /// </summary>
        public int RetryIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// 数据保留天数
        /// </summary>
        public int DataRetentionDays { get; set; } = 30;

        /// <summary>
        /// 是否启用数据验证
        /// </summary>
        public bool EnableDataValidation { get; set; } = true;

        /// <summary>
        /// 是否启用数据压缩
        /// </summary>
        public bool EnableDataCompression { get; set; } = false;

        /// <summary>
        /// 自定义同步规则（JSON格式）
        /// </summary>
        public string CustomSyncRules { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }
    }
}
