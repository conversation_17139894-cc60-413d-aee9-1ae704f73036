﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Models.Entity;
using Docpark.MultipleComparisons.Interface.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class IdentiFicationController : ControllerBase
    {
        private readonly ILogger<IdentiFicationController> _logger;
        private IDocparkMetaDataService _docparkMetaDataService { get; }
        private readonly IObjectQuery objectQuery;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IConfiguration configuration;
        private readonly IComHttpClient _httpClient;
        private readonly IDGrpc dGrpc;
        private readonly IIdentiFicationService _identiFicationService;
        private readonly IComHttpClient _comHttpClient;
        private readonly Model_NuonuoInvoiceInspectionSettings inspectionSettings;

        public IdentiFicationController(IDocparkMetaDataService _docparkMetaDataService, ILogger<IdentiFicationController> logger, IObjectQuery objectQuery, IThirdPartyMongoDbService callLogService, IConfiguration configuration, IDGrpc dGrpc, IIdentiFicationService identiFicationService, IComHttpClient httpClient, IComHttpClient comHttpClient)
        {
            _logger = logger;
            this._docparkMetaDataService = _docparkMetaDataService;
            this.objectQuery = objectQuery;
            this.callLogService = callLogService;
            this.configuration = configuration;
            this.dGrpc = dGrpc;
            this._identiFicationService = identiFicationService;
            _httpClient = httpClient;
            _comHttpClient = comHttpClient;
        }

        [HttpPost]
        [Route("VerifiyData")]
        public async Task<ResponseResult> VerifiyData(Request_FileData request_FileData) 
        {
            var list = await this._identiFicationService.VerifiyData(request_FileData);

            ResponseResult responseResult = new ResponseResult();
            try
            {
                if (list.model.verify_result)
                {
                    responseResult.isSuccess = true;
                    responseResult.data = list.model;
                    responseResult.msg = list.message;
                    responseResult.count = 0;
                }
                else
                {
                    responseResult.isSuccess = false;
                    responseResult.data = null;
                    responseResult.msg = list.message;
                    responseResult.count = 0;
                }
            }
            catch (Exception ex)
            {
                responseResult.isSuccess = false;
                responseResult.msg = ex.Message;
            }
            return responseResult;
        }

        [HttpPost]
        [Route("GetNNSendRequest")]
        public string GetNNSendRequest(string appKey, string appSecret, string taxnum, Model_NuonuoInvoiceInspectionContent content)
        {
            var result = this._identiFicationService.sendRequest(appKey, appSecret, taxnum, content, out string errmsg);
            return result;
        }


    }
}
