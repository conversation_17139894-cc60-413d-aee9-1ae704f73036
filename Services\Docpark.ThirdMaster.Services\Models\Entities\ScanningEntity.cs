﻿using Docpark.HttpClientExtension.Json;
using System;

namespace Docpark.ThirdMaster.Services.Models.Entities
{
    [JsonTable("Scanning")]
    public class ScanningEntity
    {
        public Guid MstId { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// BatchNo
        /// </summary>
        public string BatchNo { get; set; }
        /// <summary>
        /// BillNo
        /// </summary>
        public string BillNo { get; set; }
        /// <summary>
        /// VoucherNo
        /// </summary>
        public string VoucherNo { get; set; }

        public ScanningEntity(Guid mstId, string businessType, string batchNo, string billNo)
        {
            MstId = mstId;
            BusinessType = businessType;
            BatchNo = batchNo;
            BillNo = billNo;
        }
    }
}
