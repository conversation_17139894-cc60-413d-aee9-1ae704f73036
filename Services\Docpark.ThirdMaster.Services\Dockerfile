#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM registry.cn-shanghai.aliyuncs.com/gnsass/netcoresdk-nodejs-gdip:3.1 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM registry.cn-shanghai.aliyuncs.com/gnsass/netcoresdk-nodejs:3.1 AS build
WORKDIR /src
COPY ["Services/Docpark.ThirdMaster.Services/Docpark.ThirdMaster.Services.csproj", "Services/Docpark.ThirdMaster.Services/"]
COPY ["BuildingBlocks/DocPark.Commons/DocPark.Commons.csproj", "BuildingBlocks/DocPark.Commons/"]
COPY ["BuildingBlocks/DocPark.CustomExtension/DocPark.CustomExtension.csproj", "BuildingBlocks/DocPark.CustomExtension/"]
COPY ["BuildingBlocks/Devspaces.Support/Devspaces.Support.csproj", "BuildingBlocks/Devspaces.Support/"]
COPY ["BuildingBlocks/EventBusRabbitMQ/EventBusRabbitMQ.csproj", "BuildingBlocks/EventBusRabbitMQ/"]
COPY ["BuildingBlocks/EventBus/EventBus.csproj", "BuildingBlocks/EventBus/"]
COPY ["BuildingBlocks/EventBusServiceBus/EventBusServiceBus.csproj", "BuildingBlocks/EventBusServiceBus/"]
COPY ["BuildingBlocks/DocPark.MongoDb/DocPark.MongoDb.csproj", "BuildingBlocks/DocPark.MongoDb/"]
COPY ["BuildingBlocks/DocPark.EmailService/DocPark.EmailService.csproj", "BuildingBlocks/DocPark.EmailService/"]
COPY ["BuildingBlocks/Docpark.HttpClientExtension/Docpark.HttpClientExtension.csproj", "BuildingBlocks/Docpark.HttpClientExtension/"]
COPY ["BuildingBlocks/DocPark.Workflow.Share/DocPark.Workflow.Share.csproj", "BuildingBlocks/DocPark.Workflow.Share/"]


RUN dotnet restore "Services/Docpark.ThirdMaster.Services/Docpark.ThirdMaster.Services.csproj"
COPY . .
WORKDIR "/src/Services/Docpark.ThirdMaster.Services"
RUN dotnet build "Docpark.ThirdMaster.Services.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Docpark.ThirdMaster.Services.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Docpark.ThirdMaster.Services.dll"]