using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 告警控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AlertController : ControllerBase
    {
        private readonly IAlertService _alertService;
        private readonly ILogger<AlertController> _logger;

        public AlertController(
            IAlertService alertService,
            ILogger<AlertController> logger)
        {
            _alertService = alertService;
            _logger = logger;
        }

        /// <summary>
        /// 创建告警规则
        /// </summary>
        /// <param name="rule">告警规则</param>
        /// <returns>规则ID</returns>
        [HttpPost("rules")]
        public async Task<IActionResult> CreateAlertRule([FromBody] AlertRule rule)
        {
            try
            {
                var ruleId = await _alertService.CreateAlertRuleAsync(rule);
                return Ok(new { id = ruleId, message = "告警规则创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建告警规则失败");
                return StatusCode(500, new { message = "创建告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新告警规则
        /// </summary>
        /// <param name="id">规则ID</param>
        /// <param name="rule">告警规则</param>
        /// <returns>更新结果</returns>
        [HttpPut("rules/{id}")]
        public async Task<IActionResult> UpdateAlertRule(string id, [FromBody] AlertRule rule)
        {
            try
            {
                var success = await _alertService.UpdateAlertRuleAsync(id, rule);
                if (success)
                {
                    return Ok(new { message = "告警规则更新成功" });
                }
                return NotFound(new { message = "告警规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新告警规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "更新告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取告警规则
        /// </summary>
        /// <param name="id">规则ID</param>
        /// <returns>告警规则</returns>
        [HttpGet("rules/{id}")]
        public async Task<IActionResult> GetAlertRule(string id)
        {
            try
            {
                var rule = await _alertService.GetAlertRuleAsync(id);
                if (rule != null)
                {
                    return Ok(rule);
                }
                return NotFound(new { message = "告警规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有告警规则
        /// </summary>
        /// <param name="enabledOnly">仅获取启用的规则</param>
        /// <returns>告警规则列表</returns>
        [HttpGet("rules")]
        public async Task<IActionResult> GetAllAlertRules([FromQuery] bool enabledOnly = false)
        {
            try
            {
                var rules = await _alertService.GetAllAlertRulesAsync(enabledOnly);
                return Ok(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警规则列表失败");
                return StatusCode(500, new { message = "获取告警规则列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除告警规则
        /// </summary>
        /// <param name="id">规则ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("rules/{id}")]
        public async Task<IActionResult> DeleteAlertRule(string id)
        {
            try
            {
                var success = await _alertService.DeleteAlertRuleAsync(id);
                if (success)
                {
                    return Ok(new { message = "告警规则删除成功" });
                }
                return NotFound(new { message = "告警规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除告警规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 触发告警
        /// </summary>
        /// <param name="alert">告警信息</param>
        /// <returns>告警ID</returns>
        [HttpPost("trigger")]
        public async Task<IActionResult> TriggerAlert([FromBody] Alert alert)
        {
            try
            {
                var alertId = await _alertService.TriggerAlertAsync(alert);
                return Ok(new { id = alertId, message = "告警触发成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发告警失败");
                return StatusCode(500, new { message = "触发告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 确认告警
        /// </summary>
        /// <param name="alertId">告警ID</param>
        /// <param name="request">确认请求</param>
        /// <returns>确认结果</returns>
        [HttpPost("acknowledge/{alertId}")]
        public async Task<IActionResult> AcknowledgeAlert(string alertId, [FromBody] AcknowledgeAlertRequest request)
        {
            try
            {
                var success = await _alertService.AcknowledgeAlertAsync(alertId, request.AcknowledgedBy, request.Notes);
                if (success)
                {
                    return Ok(new { message = "告警确认成功" });
                }
                return NotFound(new { message = "告警不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认告警失败，告警ID: {AlertId}", alertId);
                return StatusCode(500, new { message = "确认告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 解决告警
        /// </summary>
        /// <param name="alertId">告警ID</param>
        /// <param name="request">解决请求</param>
        /// <returns>解决结果</returns>
        [HttpPost("resolve/{alertId}")]
        public async Task<IActionResult> ResolveAlert(string alertId, [FromBody] ResolveAlertRequest request)
        {
            try
            {
                var success = await _alertService.ResolveAlertAsync(alertId, request.ResolvedBy, request.ResolutionNotes);
                if (success)
                {
                    return Ok(new { message = "告警解决成功" });
                }
                return NotFound(new { message = "告警不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解决告警失败，告警ID: {AlertId}", alertId);
                return StatusCode(500, new { message = "解决告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取活跃告警
        /// </summary>
        /// <param name="severity">严重程度</param>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>活跃告警列表</returns>
        [HttpGet("active")]
        public async Task<IActionResult> GetActiveAlerts([FromQuery] AlertSeverity? severity = null, [FromQuery] string apiConfigId = null)
        {
            try
            {
                var alerts = await _alertService.GetActiveAlertsAsync(severity, apiConfigId);
                return Ok(alerts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃告警失败");
                return StatusCode(500, new { message = "获取活跃告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取告警历史
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="limit">限制数量</param>
        /// <returns>告警历史</returns>
        [HttpGet("history")]
        public async Task<IActionResult> GetAlertHistory(
            [FromQuery] string apiConfigId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int limit = 100)
        {
            try
            {
                var alerts = await _alertService.GetAlertHistoryAsync(apiConfigId, startTime, endTime, limit);
                return Ok(alerts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警历史失败");
                return StatusCode(500, new { message = "获取告警历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 评估告警规则
        /// </summary>
        /// <returns>评估结果</returns>
        [HttpPost("evaluate")]
        public async Task<IActionResult> EvaluateAlertRules()
        {
            try
            {
                await _alertService.EvaluateAlertRulesAsync();
                return Ok(new { message = "告警规则评估完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估告警规则失败");
                return StatusCode(500, new { message = "评估告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 评估特定API的告警规则
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>评估结果</returns>
        [HttpPost("evaluate/{apiConfigId}")]
        public async Task<IActionResult> EvaluateApiAlertRules(string apiConfigId)
        {
            try
            {
                await _alertService.EvaluateApiAlertRulesAsync(apiConfigId);
                return Ok(new { message = "API告警规则评估完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估API告警规则失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "评估API告警规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取告警统计信息
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>告警统计信息</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetAlertStatistics([FromQuery] DateTime? startTime = null, [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _alertService.GetAlertStatisticsAsync(startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警统计信息失败");
                return StatusCode(500, new { message = "获取告警统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 静默告警
        /// </summary>
        /// <param name="alertId">告警ID</param>
        /// <param name="request">静默请求</param>
        /// <returns>静默结果</returns>
        [HttpPost("silence/{alertId}")]
        public async Task<IActionResult> SilenceAlert(string alertId, [FromBody] SilenceAlertRequest request)
        {
            try
            {
                var success = await _alertService.SilenceAlertAsync(alertId, request.SilenceDurationMinutes, request.SilencedBy, request.Reason);
                if (success)
                {
                    return Ok(new { message = "告警静默成功" });
                }
                return BadRequest(new { message = "告警静默失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "静默告警失败，告警ID: {AlertId}", alertId);
                return StatusCode(500, new { message = "静默告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 取消静默
        /// </summary>
        /// <param name="alertId">告警ID</param>
        /// <param name="request">取消静默请求</param>
        /// <returns>取消静默结果</returns>
        [HttpPost("unsilence/{alertId}")]
        public async Task<IActionResult> UnsilenceAlert(string alertId, [FromBody] UnsilenceAlertRequest request)
        {
            try
            {
                var success = await _alertService.UnsilenceAlertAsync(alertId, request.UnsilencedBy);
                if (success)
                {
                    return Ok(new { message = "取消告警静默成功" });
                }
                return BadRequest(new { message = "取消告警静默失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消静默告警失败，告警ID: {AlertId}", alertId);
                return StatusCode(500, new { message = "取消静默告警失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的告警数据
        /// </summary>
        /// <param name="olderThanDays">保留天数</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup")]
        public async Task<IActionResult> CleanupExpiredAlerts([FromQuery] int olderThanDays = 90)
        {
            try
            {
                var deletedCount = await _alertService.CleanupExpiredAlertsAsync(olderThanDays);
                return Ok(new { 
                    message = "清理过期告警数据完成",
                    deletedCount = deletedCount,
                    olderThanDays = olderThanDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期告警数据失败");
                return StatusCode(500, new { message = "清理过期告警数据失败", error = ex.Message });
            }
        }
    }

    #region 请求模型

    /// <summary>
    /// 确认告警请求
    /// </summary>
    public class AcknowledgeAlertRequest
    {
        public string AcknowledgedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// 解决告警请求
    /// </summary>
    public class ResolveAlertRequest
    {
        public string ResolvedBy { get; set; }
        public string ResolutionNotes { get; set; }
    }

    /// <summary>
    /// 静默告警请求
    /// </summary>
    public class SilenceAlertRequest
    {
        public int SilenceDurationMinutes { get; set; }
        public string SilencedBy { get; set; }
        public string Reason { get; set; }
    }

    /// <summary>
    /// 取消静默请求
    /// </summary>
    public class UnsilenceAlertRequest
    {
        public string UnsilencedBy { get; set; }
    }

    #endregion
}
