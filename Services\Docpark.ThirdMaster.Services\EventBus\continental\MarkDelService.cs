﻿using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("MarkDel", "标记删除")]
    public class MarkDelService : IEventService
    {


        private readonly IEventBus eventBus;

        public MarkDelService(IEventBus eventBus)
        {
            this.eventBus = eventBus;
        }

        public Task<string> Handle(WorkflowMQContent mqContent)
        {
            mqContent.TriggerStatus = true;

            var msg = JsonConvert.SerializeObject(mqContent);
            var publishEventData = new DocumentServiceMQEventData(msg);
            eventBus.Publish(publishEventData);

            return Task.FromResult(string.Empty);
        }
    }
}
