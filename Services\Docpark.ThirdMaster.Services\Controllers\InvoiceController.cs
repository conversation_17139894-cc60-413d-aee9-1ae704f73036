﻿
using Docpark.ThirdMaster.Services.EmailCollection;
using Docpark.ThirdMaster.Services.Models;
using Docpark.ThirdMaster.Services.Models.Dtos;
using Docpark.ThirdMaster.Services.Models.Entities;
using Docpark.ThirdMaster.Services.Utils;
using Docpark.HttpClientExtension;
using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Commons;
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.Controllers
{
    public class InvoiceController : BaseController
    {
        private readonly ILogger<InvoiceController> logger;
        private readonly IDGrpc grpc;
        private readonly IDocparkHttpClient docparkHttpClient;
        private readonly IObjectQuery objectQuery;
        private readonly IDocparkHostService docparkHostService;
        private readonly IDocparkFileService docparkFileService;
        private readonly IServiceScopeFactory serviceScopeFactory;
        private readonly IConfiguration configuration;
        public InvoiceController(ILogger<InvoiceController> logger, IDGrpc grpc, IObjectQuery objectQuery, IDocparkHostService docparkHostService, IDocparkHttpClient docparkHttpClient, IDocparkFileService docparkFileService, IServiceScopeFactory serviceScopeFactory, IConfiguration configuration)
        {
            this.logger = logger;
            this.grpc = grpc;
            this.objectQuery = objectQuery;
            this.docparkHostService = docparkHostService;
            this.docparkHttpClient = docparkHttpClient;
            this.docparkFileService = docparkFileService;
            this.serviceScopeFactory = serviceScopeFactory;
            this.configuration = configuration;
        }
        /// <summary>
        /// 获取客户索赔发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultDataDto> GetCustomerClaimList(SearchCustomerClaimListDto input)
        {
            try
            {
                var businessType = await grpc.GetStringAsync("App.CustomerClaim.BusinessType");
                var docTypeId = await grpc.GetStringAsync("App.CustomerClaim.DocTypeId");

                input.BusinessType = businessType;

                var filter = new List<Filter>()
                {
                    new Filter() { Field = "StepName", Mode = "eq", Values = new string[] { "凭证反填" } }
                };
                if (!string.IsNullOrWhiteSpace(input.BillNo))
                {
                    //var billId = await GetBillId(input);
                    //if (string.IsNullOrWhiteSpace(billId))
                    //{
                    //    return OnError("暂无数据...");
                    //}
                    //filter.Add(new Filter() { Field = "sys_bill_id", Mode = "eq", Values = new string[] { billId } });
                    filter.Add(new Filter() { Field = "CaseNo", Mode = "eq", Values = new string[] { input.BillNo } });
                }
                if (!string.IsNullOrWhiteSpace(input.BusinessType))
                {
                    var id = CommonUtil.ConvertObjectIdToGuid(input.BusinessType).ToString();
                    filter.Add(new Filter() { Field = "businesstypeid", Mode = "eq", Values = new string[] { id } });
                }
                if (input.StartDateTime.HasValue && input.EndDateTime.HasValue)
                {
                    filter.Add(new Filter() { Field = "TaskCreatedTime", Mode = "range", Values = new string[] { input.StartDateTime.Value.ToString("yyyy-MM-dd"), input.EndDateTime.Value.ToString("yyyy-MM-dd 23:59:59") } });
                }
                var sorts = new List<Sorter> { new Sorter() { Field = "TaskCreatedTime", Order = "descend" } };
                var result = await docparkHostService.GetFormDataList(docTypeId, filter, sorts, input.MstIds, input.PageIndex, input.PageSize, false);
                if (result.Code != ResultCode.Success)
                {
                    return OnError(result.Msg);
                }
                var jObjFormData = JObject.Parse(result.Result.Data);
                var totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
                var items = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jObjFormData["items"].ToString());

                if (!input.IsExport)
                {
                    //获取单据号
                    var billIdList = items.Where(w => IsNotNull(w, "sys_bill_id")).Select(w => new Guid(w["sys_bill_id"].ToString())).ToList();
                    var scanningBill = new List<Dictionary<string, object>>();
                    if (billIdList.Count > 0)
                    {
                        var (data, _total) = objectQuery.GetList<Dictionary<string, object>>("Scanning", billIdList, new List<Filter>(), new List<Sorter>(), 0, 999).Result;
                        scanningBill = data;
                    }
                    //整理数据
                    foreach (var item in items)
                    {
                        if (IsNotNull(item, "sys_bill_id"))
                        {
                            var billId = item["sys_bill_id"].ToString();
                            var billNo = scanningBill.Where(o => o.ContainsKey("MstId") && o["MstId"] != null &&
                                                                      o.ContainsKey("BillNo") && o["BillNo"] != null &&
                                                                      o["MstId"].ToString() == billId).Select(o => o["BillNo"].ToString())
                                                                      .FirstOrDefault();
                            if (!string.IsNullOrWhiteSpace(billNo))
                                item["__billNo"] = billNo;
                        }
                    }
                }
                return OnSuccess(items, totalCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "获取客户索赔发票异常");
                return OnError("异常", ex);
            }
        }
        private bool IsNotNull(Dictionary<string, object> w, string key)
        {
            if (w.ContainsKey(key) && w[key] != null && !string.IsNullOrWhiteSpace(w[key].ToString()))
            {
                if (w[key].ToString() == "NO_AUTH")
                    return false;
                return true;
            }
            return false;
        }
        /// <summary>
        /// 导出客户索赔发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<dynamic> ExportCustomerClaimList(SearchCustomerClaimListDto input)
        {
            input.PageIndex = 1;
            input.PageSize = 1000;
            input.IsExport = true;
            try
            {
                var resultData = await GetCustomerClaimList(input);
                if (resultData.IsSuccess)
                {
                    var data = (List<Dictionary<string, object>>)resultData.Data;
                    var total = resultData.TotalCount;

                    var count = total / input.PageSize - (total % input.PageSize == 0 ? 1 : 0);

                    for (int i = 0; i < count; i++)
                    {
                        input.PageIndex++;
                        var nextResultData = await GetCustomerClaimList(input);
                        if (nextResultData.IsSuccess)
                        {
                            var nextData = (List<Dictionary<string, object>>)nextResultData.Data;
                            data = data.Concat(nextData).ToList();
                        }
                        else
                        {
                            logger.LogError($"获取客户索赔发票失败，参数{JsonConvert.SerializeObject(input)}");
                            return nextResultData;
                        }
                    }

                    var title = new string[] { "公司代码", "发票号码", "发票日期", "凭证号码", "入账金额", "利润中心", "入账日期", "Remark" };
                    var fields = new string[] { "CompanyCode", "number", "date", "null_dlpzhm", "null_dlrzje", "null_dllrzx", "null_dlrzrq", "null_pz_remark" };
                    //var title = new List<string>();
                    //var fields = new List<string>();

                    //foreach (var item in input.ShortFields)
                    //{
                    //    title.Add(item.FieldName);
                    //    fields.Add(string.IsNullOrWhiteSpace(item.Identity) ? item.Field : item.Identity);
                    //}
                    return ExportExcel("客户索赔发票", title.ToArray(), fields.ToArray(), data);
                }
                logger.LogError($"获取客户索赔发票失败，参数{JsonConvert.SerializeObject(input)}");
                return resultData;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "导出客户索赔发票异常");
                return OnError("异常", ex);
            }
        }

        private IActionResult ExportExcel(string sheetName, string[] titles, string[] fields, List<Dictionary<string, object>> data)
        {
            var book = new XSSFWorkbook();
            //var book = new HSSFWorkbook();
            var sheet = book.CreateSheet(sheetName);

            var style = book.CreateCellStyle();
            style.Alignment = HorizontalAlignment.Center;
            style.VerticalAlignment = VerticalAlignment.Center;
            style.BorderBottom = BorderStyle.Thin;//下边框为细线边框
            style.BorderLeft = BorderStyle.Thin;//左边框
            style.BorderRight = BorderStyle.Thin;//上边框
            style.BorderTop = BorderStyle.Thin;//右边框

            var hasSub = titles.Any(w => w.Contains("$|$|$"));
            if (!hasSub)
            {
                var headerrow = sheet.CreateRow(0);
                for (int i = 0; i < titles.Length; i++)
                {
                    var cell = headerrow.CreateCell(i);
                    cell.SetCellValue(titles[i]);
                    cell.CellStyle = style;
                    //sheet.SetColumnWidth(i, 20 * 256);
                }

                if (data != null)
                {
                    for (int i = 0; i < data.Count; i++)
                    {
                        var itemData = data[i];
                        var row = sheet.CreateRow(i + 1);
                        for (int j = 0; j < fields.Length; j++)
                        {
                            var field = fields[j];
                            var value = itemData.ContainsKey(field) && itemData[field] != null ? itemData[field].ToString() : string.Empty;
                            var cell = row.CreateCell(j);
                            cell.SetCellValue(value);
                            cell.CellStyle = style;
                        }
                    }
                }
            }
            else
            {
                #region 表头
                var subStart = 0;
                var subLength = titles.Where(w => w.Contains("$|$|$")).Count();
                var headerRow1 = sheet.CreateRow(0);
                var headerRow2 = sheet.CreateRow(1);
                for (int i = 0; i < titles.Length; i++)
                {
                    if (titles[i].Contains("$|$|$"))
                    {
                        if (subStart == 0)
                            subStart = i;
                        var subTitle = titles[i].Split("$|$|$")[0];
                        var itemTitle = titles[i].Split("$|$|$")[1];

                        var cell = headerRow1.CreateCell(i);
                        cell.SetCellValue(subTitle);
                        cell.CellStyle = style;

                        var subCell = headerRow2.CreateCell(i);
                        subCell.SetCellValue(itemTitle);
                        subCell.CellStyle = style;
                    }
                    else
                    {
                        //合并表单的表头
                        var titleAddress = new CellRangeAddress(0, 1, i, i);
                        sheet.AddMergedRegion(titleAddress);

                        var cell = headerRow1.CreateCell(i);
                        cell.SetCellValue(titles[i]);
                        cell.CellStyle = style;

                        headerRow2.CreateCell(i).CellStyle = style;
                    }
                }
                //合并子表单的表头
                var subTitleAddress = new CellRangeAddress(0, 0, subStart, titles.Length - 1);
                sheet.AddMergedRegion(subTitleAddress);
                #endregion

                if (data != null)
                {
                    var startRow = 2;
                    var strSubField = fields.FirstOrDefault(w => w.Contains("$|$|$"));
                    var subField = strSubField.Split("$|$|$")[0];

                    for (int i = 0; i < data.Count; i++)
                    {
                        var itemData = data[i];

                        //获取子表单数据
                        var subData = new List<Dictionary<string, object>>();
                        if (itemData.ContainsKey(subField) && itemData[subField] != null)
                        {
                            subData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(itemData[subField].ToString());
                        }
                        if (subData?.Count <= 0)
                        {
                            subData = new List<Dictionary<string, object>>()
                            {
                                new Dictionary<string, object>()
                            };
                        }

                        for (int subIndex = 0; subIndex < subData.Count; subIndex++)
                        {
                            var row = sheet.CreateRow(startRow + subIndex);

                            for (int j = 0; j < fields.Length; j++)
                            {
                                var field = fields[j];
                                if (field.Contains("$|$|$"))
                                {
                                    var itemField = field.Split("$|$|$")[1];
                                    var itemSubData = subData[subIndex];
                                    var value = itemSubData.ContainsKey(itemField) && itemSubData[itemField] != null ? itemSubData[itemField].ToString() : string.Empty;
                                    var cell = row.CreateCell(j);
                                    cell.SetCellValue(value);
                                    cell.CellStyle = style;
                                }
                                else
                                {
                                    var value = itemData.ContainsKey(field) && itemData[field] != null ? itemData[field].ToString() : string.Empty;
                                    var cell = row.CreateCell(j);
                                    cell.SetCellValue(value);
                                    cell.CellStyle = style;
                                }
                            }
                        }
                        //合并单元格
                        for (int j = 0; j < fields.Length; j++)
                        {
                            var field = fields[j];
                            if (!field.Contains("$|$|$") && subData.Count > 1)
                            {
                                var cellRangeAddress = new CellRangeAddress(startRow, startRow + subData.Count - 1, j, j);
                                sheet.AddMergedRegion(cellRangeAddress);
                            }
                        }
                        startRow += subData.Count;
                    }
                }
            }

            MemoryStream ms = new MemoryStream();
            book.Write(ms);

            var bytes = ms.ToArray();

            book.Close();
            book.Dispose();
            ms.Close();
            ms.Dispose();

            return File(bytes, "application/vnd.ms-excel", "Export.xlsx");
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<dynamic> ExportInvExcel(ExportExcelDto input)
        {
            if (input.MstIds.Count == 0)
            {
                throw new Exception("MstIds is null");
            }
            try
            {
                var mstIds = input.MstIds.Select(w => w.ToString()).ToList();
                var sorts = new List<Sorter> { new Sorter() { Field = "TaskCreatedTime", Order = "descend" } };
                var result = await docparkHostService.GetFormDataList(input.DocType, new List<Filter>(), sorts, mstIds, 0, 1000, false);

                if (result.Code == ResultCode.Success)
                {
                    var jObjFormData = JObject.Parse(result.Result.Data);
                    var totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
                    var items = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jObjFormData["items"].ToString());

                    var title = new List<string>();
                    var fields = new List<string>();

                    foreach (var item in input.ShortFields)
                    {
                        title.Add(item.FieldName);
                        fields.Add(string.IsNullOrWhiteSpace(item.Identity) ? item.Field : item.Identity);
                    }

                    var (subTitle, subFields) = await BuildFields(input.DocType);
                    title.AddRange(subTitle);
                    fields.AddRange(subFields);

                    return ExportExcel("发票数据", title.ToArray(), fields.ToArray(), items);
                }
                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "导出发票异常");
                return OnError("异常", ex);
            }
        }
        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<dynamic> ExportInvExcelBySearch(ExportExcelBySearchDto input)
        {
            try
            {
                input.PageIndex = 1;
                input.PageSize = 1000;
                var resultData = await GetExportDataBySearch(input);
                if (resultData.IsSuccess)
                {
                    var data = (List<Dictionary<string, object>>)resultData.Data;
                    var total = resultData.TotalCount;

                    var count = total / input.PageSize - (total % input.PageSize == 0 ? 1 : 0);

                    for (int i = 0; i < count; i++)
                    {
                        input.PageIndex++;
                        var nextResultData = await GetExportDataBySearch(input);
                        if (nextResultData.IsSuccess)
                        {
                            var nextData = (List<Dictionary<string, object>>)nextResultData.Data;
                            data = data.Concat(nextData).ToList();
                        }
                        else
                        {
                            logger.LogError($"获取导出发票失败，参数{JsonConvert.SerializeObject(input)}");
                            return nextResultData;
                        }
                    }

                    var title = new List<string>();
                    var fields = new List<string>();

                    foreach (var item in input.ShortFields)
                    {
                        title.Add(item.FieldName);
                        fields.Add(string.IsNullOrWhiteSpace(item.Identity) ? item.Field : item.Identity);
                    }

                    var (subTitle, subFields) = await BuildFields(input.DocType);
                    title.AddRange(subTitle);
                    fields.AddRange(subFields);

                    return ExportExcel("发票数据", title.ToArray(), fields.ToArray(), data);
                }
                return resultData;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "导出发票异常");
                return OnError("异常", ex);
            }
        }

        private async Task<ResultDataDto> GetExportDataBySearch(ExportExcelBySearchDto input)
        {
            try
            {
                var filter = input.Filters;
                if (!string.IsNullOrWhiteSpace(input.BusinessType))
                {
                    var id = CommonUtil.ConvertObjectIdToGuid(input.BusinessType).ToString();
                    filter.Add(new Filter() { Field = "businesstypeid", Mode = "eq", Values = new string[] { id } });
                }
                var sorts = new List<Sorter> { new Sorter() { Field = "TaskCreatedTime", Order = "descend" } };
                var result = await docparkHostService.GetFormDataList(input.DocType, filter, sorts, new List<string>(), input.PageIndex, input.PageSize, input.EnablePremission);
                if (result.Code != ResultCode.Success)
                {
                    return OnError(result.Msg);
                }
                var jObjFormData = JObject.Parse(result.Result.Data);
                var totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
                var items = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jObjFormData["items"].ToString());
                return OnSuccess(items, totalCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "导出发票异常");
                return OnError("异常", ex);
            }
        }

        private async Task<(List<string> subTitle, List<string> subFields)> BuildFields(string docuemntId)
        {
            var subTitle = new List<string>();
            var subFields = new List<string>();

            var enable = configuration["ExportSubConfigs:Enable"];
            if (!string.Equals(enable, "true", StringComparison.OrdinalIgnoreCase))
            {
                return (subTitle, subFields);
            }

            var strForms = configuration["ExportSubConfigs:FormString"];
            if (string.IsNullOrWhiteSpace(strForms))
            {
                return (subTitle, subFields);
            }
            var strArry = strForms.Split(";");
            var formConfigs = new List<(string documentId, string pIdentity)>();
            foreach (var str in strArry)
            {
                if (!string.IsNullOrWhiteSpace(str))
                {
                    var itemArry = str.Split('|');
                    if (itemArry.Length == 2)
                    {
                        formConfigs.Add((itemArry[0], itemArry[1]));
                    }
                }
            }
            var dto = formConfigs.FirstOrDefault(w => w.documentId == docuemntId);
            if (string.IsNullOrWhiteSpace(dto.documentId))
                return (subTitle, subFields);

            var fields = await docparkHostService.GetFields(docuemntId);
            var sfields = fields.Where(w => w.PIdentity == dto.pIdentity || w.PCustomType == dto.pIdentity).ToList();
            foreach (var item in sfields)
            {
                if (!string.IsNullOrWhiteSpace(item.Identity) || !string.IsNullOrWhiteSpace(item.CustomType))
                {
                    subTitle.Add($"{item.ParentName}$|$|${item.Name}");
                    subFields.Add($"{dto.pIdentity}$|$|${item.Identity ?? item.CustomType}");
                }
            }
            return (subTitle, subFields);
        }

        [HttpPost]
        public async Task<dynamic> ExportInvFiles(ExportExcelDto input)
        {
            if (input.MstIds.Count == 0)
            {
                throw new Exception("MstIds is null");
            }
            try
            {
                var businessType = await grpc.GetStringAsync("App.CustomerClaim.BusinessType");
                var docTypeId = await grpc.GetStringAsync("App.CustomerClaim.DocTypeId");
                var isCustomerClaim = businessType?.Trim() == input.BusinessType?.Trim();

                var mstIds = input.MstIds.Select(w => w.ToString()).ToList();
                var fields = await docparkHostService.GetFields(input.DocType);

                var sorts = new List<Sorter> { new Sorter() { Field = "TaskCreatedTime", Order = "descend" } };
                var result = await docparkHostService.GetFormDataList(input.DocType, new List<Filter>(), sorts, mstIds, 0, 1000, false);
                if (result.Code != ResultCode.Success)
                {
                    return result;
                }
                var jObjFormData = JObject.Parse(result.Result.Data);
                var totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
                var items = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jObjFormData["items"].ToString());

                var docView = fields.Find(o => o.PropType == "documentView");
                if (docView == null)
                    return OnError();

                var customers = new List<CustomerEntity>();
                if (isCustomerClaim)
                    customers = (await objectQuery.GetList<CustomerEntity>(new List<Filter>(), new List<Sorter>(), 0, 1000)).data;
                //附件命名
                var files = new List<(string docId, string fileName)>();
                foreach (var item in items)
                {
                    var docId = item.GetValue(docView.Id);
                    var fileName = string.Empty;

                    var caseNode = item.GetValue("CaseNo");
                    if (input.DocType == docTypeId)
                    {
                        var compnayCode = item.GetValue("CompanyCode");
                        var number = item.GetValue("number");
                        fileName = $"{compnayCode}-{number}";
                        if (isCustomerClaim)//客户索赔流程
                        {
                            var gmfsh = item.GetValue("buyer_tax_id");
                            var xsfsh = item.GetValue("seller_tax_id");
                            var customer = customers.Where(w => w.TaxNumber.ToLower() == gmfsh.ToLower() || w.TaxNumber.ToLower() == xsfsh.ToLower()).FirstOrDefault();
                            if (customer != null )
                            {
                                fileName = $"{compnayCode}-{number}-{customer.ShortName}";
                            }
                        }
                    }
                    else
                    {   //对公费用
                        fileName = caseNode;
                    }
                    files.Add((docId, fileName));
                }

                var bytes = await DownLoadFiles(files);
                return File(bytes, "application/octet-stream", "files.zip");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "导出发票异常");
                return OnError("异常", ex);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        private async Task<byte[]> DownLoadFiles(List<(string docId, string fileName)> files)
        {
            byte[] buffer = new byte[6500];
            var zip = new MemoryStream();
            using (var zipStream = new ZipOutputStream(zip))
            {
                foreach (var item in files)
                {
                    var docId = item.docId;
                    var fileName = item.fileName;
                    //下载附件
                    (byte[], MediaTypeHeaderValue, string) fileItem;
                    fileItem = await docparkFileService.DowLoadFile(docId);

                    var ext = Path.GetExtension(fileItem.Item3)?.ToLower().Replace("\"","");

                    fileName = $"{fileName}{ext}";
                    //压缩附件
                    var bytes = fileItem.Item1;

                    Console.WriteLine($"下载文件：文件长度：{fileItem.Item1.Length},文件名称：{fileItem.Item3},扩展名：{ext},DocumentId:{docId}");

                    if (ext == ".zip" || ext == ".rar")
                    {
                        var zipFiles = FileOptHelper.UnZip(fileItem.Item1);
                        bytes = FileOptHelper.GeneratePdfFile(zipFiles.Select(w => w.Item2).ToList());
                        fileName = $"{item.fileName}.pdf";
                    }

                    using (var streamItem = new MemoryStream(bytes))
                    {
                        var tempZipEntry = new ZipEntry(fileName)
                        {
                            IsUnicodeText = true,
                            DateTime = DateTime.Now,//创建时间
                        };
                        zipStream.PutNextEntry(tempZipEntry);
                        while (true)
                        {
                            var readCount = await streamItem.ReadAsync(buffer, 0, buffer.Length);
                            if (readCount > 0)
                                zipStream.Write(buffer, 0, readCount);
                            else
                                break;
                        }
                        zipStream.Flush();
                    }
                }
                zipStream.Finish();
                zip.Position = 0;

                return zip.ToArray();
            }
        }
        private async Task<WatermarkEntity> GetWatermark()
        {
            var host = await grpc.GetStringAsync("App.Api.FTP.Host");

            var api = "api/FTP/Watermark/GetWatermarkPars";
            string strResult = await docparkHttpClient.GetAsync(api.ToUrl(host));
            var jObjFormData = JObject.Parse(strResult);
            if ((bool)jObjFormData["state"])
            {
                var jsonWatermark = jObjFormData["data"] == null ? string.Empty : jObjFormData["data"].ToString();
                if (!string.IsNullOrWhiteSpace(jsonWatermark))
                {
                    return JsonConvert.DeserializeObject<WatermarkEntity>(jsonWatermark);
                }
            }
            return null;
        }

        [HttpGet]
        public async Task<ResultDataDto> GetBillDetailList(string billId, string docTypeId)
        {
            try
            {
                var fields = await docparkHostService.GetFields(docTypeId);
                var docView = fields.Find(o => o.PropType == "documentView");
                if (docView == null)
                {
                    logger.LogError($"文档表单{docTypeId},未配置影像预览控件");
                    return OnError($"文档表单{docTypeId},未配置影像预览控件");
                }
                var filter = new List<Filter>() {
                    new Filter() { Field = "sys_bill_id", Mode = "eq", Values = new string[] { billId } }
                };
                var sorts = new List<Sorter> { new Sorter() { Field = "CaseNo", Order = "ascend" } };
                var result = await docparkHostService.GetFormDataList(docTypeId, filter, sorts, new List<string>(), 1, 100, false);
                if (result.Code != ResultCode.Success)
                {
                    return OnError(result.Msg);
                }
                var jObjFormData = JObject.Parse(result.Result.Data);
                var items = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jObjFormData["items"].ToString());

                var list = items.Select(o => new
                {
                    id = o.ContainsKey("MstId") && o["MstId"] != null ? o["MstId"].ToString() : string.Empty,
                    mstId = o.ContainsKey("MstId") && o["MstId"] != null ? o["MstId"].ToString() : string.Empty,
                    docType = docTypeId,
                    docId = GetValue(o, docView),
                    caseNo = o.ContainsKey("CaseNo") && o["CaseNo"] != null ? o["CaseNo"].ToString() : string.Empty,
                });

                return OnSuccess(list);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "获取影像详情列表异常");
                return OnError("获取异常", ex);
            }
        }

        private string GetValue(Dictionary<string, object> o, DocFormField docView)
        {
            if (!string.IsNullOrWhiteSpace(docView.CustomType) && o.ContainsKey(docView.CustomType) && o[docView.CustomType] != null)
                return o[docView.CustomType].ToString();
            if (o.ContainsKey(docView.Id) && o[docView.Id] != null)
                return o[docView.Id].ToString();
            return string.Empty;
        }

    }
}