using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 监控控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class MonitoringController : ControllerBase
    {
        private readonly IMonitoringService _monitoringService;
        private readonly ILogger<MonitoringController> _logger;

        public MonitoringController(
            IMonitoringService monitoringService,
            ILogger<MonitoringController> logger)
        {
            _monitoringService = monitoringService;
            _logger = logger;
        }

        /// <summary>
        /// 执行健康检查
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>健康检查结果</returns>
        [HttpPost("health-check/{apiConfigId}")]
        public async Task<IActionResult> PerformHealthCheck(string apiConfigId)
        {
            try
            {
                var healthCheck = await _monitoringService.PerformHealthCheckAsync(apiConfigId);
                return Ok(healthCheck);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行健康检查失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "执行健康检查失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 批量执行健康检查
        /// </summary>
        /// <param name="request">批量健康检查请求</param>
        /// <returns>健康检查结果列表</returns>
        [HttpPost("health-check/batch")]
        public async Task<IActionResult> PerformBatchHealthCheck([FromBody] BatchHealthCheckRequest request)
        {
            try
            {
                var healthChecks = await _monitoringService.PerformBatchHealthCheckAsync(request.ApiConfigIds);
                return Ok(healthChecks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量执行健康检查失败");
                return StatusCode(500, new { message = "批量执行健康检查失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取健康检查历史
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="limit">限制数量</param>
        /// <returns>健康检查历史</returns>
        [HttpGet("health-check/history/{apiConfigId}")]
        public async Task<IActionResult> GetHealthCheckHistory(
            string apiConfigId, 
            [FromQuery] DateTime? startTime = null, 
            [FromQuery] DateTime? endTime = null, 
            [FromQuery] int limit = 100)
        {
            try
            {
                var history = await _monitoringService.GetHealthCheckHistoryAsync(apiConfigId, startTime, endTime, limit);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取健康检查历史失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取健康检查历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取系统健康状态概览
        /// </summary>
        /// <returns>系统健康状态概览</returns>
        [HttpGet("health-overview")]
        public async Task<IActionResult> GetSystemHealthOverview()
        {
            try
            {
                var overview = await _monitoringService.GetSystemHealthOverviewAsync();
                return Ok(overview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统健康状态概览失败");
                return StatusCode(500, new { message = "获取系统健康状态概览失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="metric">性能指标</param>
        /// <returns>记录结果</returns>
        [HttpPost("metrics")]
        public async Task<IActionResult> RecordPerformanceMetric([FromBody] PerformanceMetric metric)
        {
            try
            {
                await _monitoringService.RecordPerformanceMetricAsync(metric);
                return Ok(new { message = "性能指标记录成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录性能指标失败");
                return StatusCode(500, new { message = "记录性能指标失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 批量记录性能指标
        /// </summary>
        /// <param name="metrics">性能指标列表</param>
        /// <returns>记录结果</returns>
        [HttpPost("metrics/batch")]
        public async Task<IActionResult> RecordPerformanceMetrics([FromBody] List<PerformanceMetric> metrics)
        {
            try
            {
                await _monitoringService.RecordPerformanceMetricsAsync(metrics);
                return Ok(new { message = "批量性能指标记录成功", count = metrics.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量记录性能指标失败");
                return StatusCode(500, new { message = "批量记录性能指标失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="metricName">指标名称</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="limit">限制数量</param>
        /// <returns>性能指标列表</returns>
        [HttpGet("metrics/{apiConfigId}")]
        public async Task<IActionResult> GetPerformanceMetrics(
            string apiConfigId,
            [FromQuery] string metricName = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int limit = 1000)
        {
            try
            {
                var metrics = await _monitoringService.GetPerformanceMetricsAsync(apiConfigId, metricName, startTime, endTime, limit);
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取性能指标失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>性能统计信息</returns>
        [HttpGet("statistics/{apiConfigId}")]
        public async Task<IActionResult> GetPerformanceStatistics(
            string apiConfigId,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _monitoringService.GetPerformanceStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能统计信息失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取性能统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取API可用性统计
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>可用性统计信息</returns>
        [HttpGet("availability/{apiConfigId}")]
        public async Task<IActionResult> GetApiAvailabilityStatistics(
            string apiConfigId,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var availability = await _monitoringService.GetApiAvailabilityStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(availability);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取API可用性统计失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取API可用性统计失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取响应时间趋势
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="interval">时间间隔（minute/hour/day）</param>
        /// <returns>响应时间趋势</returns>
        [HttpGet("response-time-trend/{apiConfigId}")]
        public async Task<IActionResult> GetResponseTimeTrend(
            string apiConfigId,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] string interval = "hour")
        {
            try
            {
                var trend = await _monitoringService.GetResponseTimeTrendAsync(apiConfigId, startTime, endTime, interval);
                return Ok(trend);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取响应时间趋势失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取响应时间趋势失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的监控数据
        /// </summary>
        /// <param name="olderThanDays">保留天数</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup")]
        public async Task<IActionResult> CleanupExpiredMonitoringData([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var deletedCount = await _monitoringService.CleanupExpiredMonitoringDataAsync(olderThanDays);
                return Ok(new { 
                    message = "清理过期监控数据完成",
                    deletedCount = deletedCount,
                    olderThanDays = olderThanDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期监控数据失败");
                return StatusCode(500, new { message = "清理过期监控数据失败", error = ex.Message });
            }
        }
    }

    #region 请求模型

    /// <summary>
    /// 批量健康检查请求
    /// </summary>
    public class BatchHealthCheckRequest
    {
        public List<string> ApiConfigIds { get; set; }
    }

    #endregion
}
