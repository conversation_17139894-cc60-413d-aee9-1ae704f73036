﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Docpark.MultipleComparisons.Interface.Models;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IVoucherService
    {
        Task<ResponseResult> GenerateVoucherInfo(RequestVoucherDto inputDto);
        Task<(string, string)> GenerateVoucherPdf(AccountingVoucher accountingVoucher);
        Task<bool> UpdateReimbursementState(string archivesKey);
        Task<int> GetMaxVoucherNo();
    }
}