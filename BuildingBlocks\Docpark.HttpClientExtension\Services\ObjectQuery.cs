﻿using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class ObjectQuery : IObjectQuery
    {
        private readonly IDocparkMetaDataService metaDataService;
        private readonly IDocparkHostService docparkHostService;


        public ObjectQuery(IDocparkMetaDataService metaDataService, IDocparkHostService docparkHostService)
        {
            this.metaDataService = metaDataService;
            this.docparkHostService = docparkHostService;
        }
        public async Task<bool> CreateOrUpdate<T>(Guid mstId, T entity)
        {

            var tableName = this.GetTableName<T>();

            return await CreateOrUpdate<T>(tableName, mstId, entity);
        }

        public async Task<bool> CreateOrUpdate(string documentTypeId, Guid mstId, Dictionary<string, object> entity)
        {
            var fields = await docparkHostService.GetFields(documentTypeId);

            var data = entity.ConvertoFormData(fields);

            Dictionary<string, object> formData = new Dictionary<string, object>();
            formData["documentTypeId"] = documentTypeId;
            formData["mstId"] = mstId;
            formData["formData"] = data;

            return await docparkHostService.SaveFormData(formData);
        }

        public async Task<bool> CreateOrUpdate<T>(string tableName, Guid mstId, T entity)
        {
            var objectId = await this.GetObjectId(tableName);

            Dictionary<string, object> jsonData = JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(entity));

            var fields = await docparkHostService.GetFields(objectId);

            var data = jsonData.ConvertoFormData(fields);

            Dictionary<string, object> formData = new Dictionary<string, object>();
            formData["documentTypeId"] = objectId;
            formData["mstId"] = mstId;
            formData["formData"] = data;

            return await docparkHostService.SaveFormData(formData);
        }

        public async Task<bool> BulkCreateOrUpdate<T>(List<T> Entity)
        {
            var tableName = this.GetTableName<T>();
            var objectId = await this.GetObjectId(tableName);
            var fields = await docparkHostService.GetFields(objectId);


            List<Dictionary<string, object>> formDataList = new List<Dictionary<string, object>>();
            foreach (var item in Entity)
            {
                Dictionary<string, object> jsonData = JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(item));
                var data = jsonData.ConvertoFormData(fields);

                var mstId = this.GetMstId(item);
                if (mstId == null)
                    throw new Exception("mstId is null");

                Dictionary<string, object> formData = new Dictionary<string, object>();
                formData["documentTypeId"] = objectId;
                formData["mstId"] = mstId;
                formData["formData"] = data;
                formDataList.Add(formData);
            }

            return await docparkHostService.SaveFormDataList(formDataList);
        }

        public async Task<bool> BulkCreateOrUpdate<T>(string documentTypeId, List<T> Entity, List<DocFormField> fields = null)
        {
            if (fields == null || fields.Count == 0)
            {
                fields = await docparkHostService.GetFields(documentTypeId);
            }

            List<Dictionary<string, object>> formDataList = new List<Dictionary<string, object>>();
            foreach (var item in Entity)
            {
                Dictionary<string, object> jsonData = JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(item));
                var data = jsonData.ConvertoFormData(fields);

                var mstId = this.GetMstId(item);
                if (mstId == null)
                    throw new Exception("mstId is null");

                Dictionary<string, object> formData = new Dictionary<string, object>();
                formData["documentTypeId"] = documentTypeId;
                formData["mstId"] = mstId;
                formData["formData"] = data;
                formDataList.Add(formData);
            }

            return await docparkHostService.SaveFormDataList(formDataList);
        }

        public async Task<T> Get<T>(Guid mstId)
        {
            var result = await docparkHostService.GetFormData<T>(mstId.ToString());
            return result;
        }



        public async Task<(List<T> data, int totalCount)> GetList<T>(List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            var tableName = this.GetTableName<T>();

            return await GetList<T>(tableName, new List<Guid>(), filters, sorts, pageIndex, pageSize);
        }

        public async Task<(List<T> data, int totalCount)> GetList<T>(string tableName, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            return await GetList<T>(tableName, new List<Guid>(), filters, sorts, pageIndex, pageSize);
        }

        public async Task<(List<T> data, int totalCount)> GetList<T>(List<Guid> MstIds, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            var tableName = this.GetTableName<T>();
            return await GetList<T>(tableName, MstIds, filters, sorts, pageIndex, pageSize);
        }

        public async Task<(List<T> data, int totalCount)> GetList<T>(string tableName, List<Guid> MstIds, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {

            var objectId = await this.GetObjectId(tableName);
            var result = await docparkHostService.GetFormDataList(objectId, filters, sorts,
                MstIds.Select(s => s.ToString()).ToList(),
                pageIndex, pageSize, false);
            var jObjFormData = JObject.Parse(result.Result.Data);
            int totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
            List<T> data = JsonConvert.DeserializeObject<List<T>>(jObjFormData["items"].ToString());
            return (data, totalCount);
        }


        public async Task<(List<T> data, int totalCount)> GetUnionList<T>(string tableName, List<Guid> mstIds, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            var objectId = await this.GetObjectId(tableName);
            var result = await docparkHostService.GetFormDataLists(objectId, filters, sorts, mstIds.Select(s => s.ToString()).ToList(), pageIndex, pageSize);
            var jObjFormData = JObject.Parse(result.Result.Data);

            int totalCount = int.Parse(jObjFormData["totalCount"] == null ? "0" : jObjFormData["totalCount"].ToString());
            List<T> data = JsonConvert.DeserializeObject<List<T>>(jObjFormData["items"].ToString());
            return (data, totalCount);
        }

        public async Task<(List<T> data, int totalCount)> GetUnionList<T>(List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            var tableName = this.GetTableName<T>();
            return await GetUnionList<T>(tableName, new List<Guid>(), filters, sorts, pageIndex, pageSize);
        }
        public async Task<(List<T> data, int totalCount)> GetUnionList<T>(List<Guid> mstIds, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            var tableName = this.GetTableName<T>();
            return await GetUnionList<T>(tableName, mstIds, filters, sorts, pageIndex, pageSize);
        }
        public async Task<(List<T> data, int totalCount)> GetUnionList<T>(string tableName, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize)
        {
            return await GetUnionList<T>(tableName, new List<Guid>(), filters, sorts, pageIndex, pageSize);
        }

        public async Task<bool> RemoveAll(List<Guid> mstIds)
        {
            var result = await docparkHostService.BatchRemove(mstIds.Select(s => s.ToString()).ToList());
            return result.Code == Const.ResultCode.Success;
        }

        public async Task<string> GetObjectId(string tableName)
        {
            var documentType = await metaDataService.GetDocumentType(tableName);

            if (documentType == null)
            {
                throw new Exception($"【{tableName}】 identity is null");
            }

            return documentType.id;
        }

        public async Task<int> Count(string tableName, List<Filter> filters, List<Guid> mstIds = null)
        {
            var objectId = await this.GetObjectId(tableName);
            if (mstIds == null)
            {
                mstIds = new List<Guid>();
            }
            var result = await docparkHostService.Count(objectId, filters, new List<Sorter>(),
                mstIds.Select(s => s.ToString()).ToList(),
                0, 0, false);
            return result;
        }
        public async Task<int> Counts(string tableName, List<List<Filter>> filters, List<Guid> mstIds = null)
        {
            if (mstIds == null)
            {
                mstIds = new List<Guid>();
            }
            var objectId = await this.GetObjectId(tableName);
            var result = await docparkHostService.Count(objectId, filters, new List<Sorter>(), mstIds.Select(s => s.ToString()).ToList(), 0, 1);
            return result;
        }

        public async Task<int> Count<T>(List<Filter> filters, List<Guid> mstIds = null)
        {
            var tableName = this.GetTableName<T>();
            return await Count(tableName, filters, mstIds);
        }


        public async Task<int> Counts<T>(List<List<Filter>> filters, List<Guid> mstIds = null)
        {
            var tableName = this.GetTableName<T>();
            return await Counts(tableName, filters, mstIds);
        }

        #region 私有方法
        private string GetTableName<T>()
        {
            var jsonTable = typeof(T).GetCustomAttributes(typeof(JsonTable), true).FirstOrDefault();
            if (jsonTable == null)
            {
                throw new Exception("JsonTable is null");
            }
            var name = ((JsonTable)jsonTable).Name;
            return name;
        }

        private string GetMstId(object entity)
        {
            if (entity.GetType().Name == "Dictionary`2")
            {
                string MstId = null;
                var dic = entity as Dictionary<string, object>;
                if (dic.ContainsKey("MstId"))
                {
                    MstId = dic["MstId"].ToString();
                }
                else if (dic.ContainsKey("MstId"))
                {
                    MstId = dic["mstId"].ToString();
                }
                else if (dic.ContainsKey("mstid"))
                {
                    MstId = dic["mstid"].ToString();
                }
                return MstId;
            }
            else
            {
                var MstId = entity.GetType().GetProperty("MstId");
                if (MstId == null)
                {
                    MstId = entity.GetType().GetProperty("mstId");
                }
                return MstId == null ? null : MstId.GetValue(entity) == null ? null : MstId.GetValue(entity).ToString();
            }
        }

        #endregion
    }
}
