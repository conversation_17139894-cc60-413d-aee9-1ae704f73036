﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class OtherAnnexItems
    {
        /// <summary>
        /// 所属单据id
        /// </summary>
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 发票附件id
        /// </summary>
        public string annex_guid { get; set; }
        /// <summary>
        /// 发票附件名称
        /// </summary>
        public string annex_name { get; set; }
        /// <summary>
        /// 发票附件base64
        /// </summary>
        public string annex_details { get; set; }
        /// <summary>
        /// 文件类型（png/jpg/jpeg/pdf/ofd）
        /// </summary>
        public string img_data_filetype { get; set; }
    }
}
