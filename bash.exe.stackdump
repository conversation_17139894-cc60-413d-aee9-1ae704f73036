Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA170, 0007FFFF9070) msys-2.0.dll+0x1FE8E
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210286019, 0007FFFFA028, 0007FFFFA170, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA170  000210068E24 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA450  00021006A225 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF888EF0000 ntdll.dll
7FF887AC0000 KERNEL32.DLL
7FF886A00000 KERNELBASE.dll
7FF887C70000 USER32.dll
7FF886520000 win32u.dll
7FF886DC0000 GDI32.dll
7FF886550000 gdi32full.dll
7FF886950000 msvcp_win.dll
7FF886800000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF887630000 advapi32.dll
7FF888CC0000 msvcrt.dll
7FF888D80000 sechost.dll
7FF888800000 RPCRT4.dll
7FF885A90000 CRYPTBASE.DLL
7FF886340000 bcryptPrimitives.dll
7FF886FD0000 IMM32.DLL
