﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_Voucher2Bill_Relation_Data
    {

        /// <summary>
        /// 凭证Id<br/>
        /// 用户系统凭证唯一编码。有则传入，无则不传入，数据以 业务实体名，凭证类型，凭证期间，和凭证号 四要素组合确定唯一
        /// </summary>
        public string voucherId { get; set; }

        /// <summary>
        /// 业务实体名称(公司名称)
        /// </summary>
        public string businessEntityName { get; set; }

        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucherType { get; set; }

        /// <summary>
        /// 凭证期间
        /// </summary>
        public string voucherPeriod { get; set; }

        /// <summary>
        /// 凭证号
        /// </summary>
        public string voucherNo { get; set; }

        /// <summary>
        /// 自定义单据列表
        /// </summary>
        public List<Voucher2Bill_Bill> bills { get; set; }
    }

    /// <summary>
    /// 自定义单据
    /// </summary>
    public class Voucher2Bill_Bill
    {
        /// <summary>
        /// 字段代码: 固定值(fd_id)
        /// </summary>
        public string fieldCode { get; set; }
        /// <summary>
        /// 字段值(单据ID)
        /// </summary>
        public string value { get; set; }
    }
}
