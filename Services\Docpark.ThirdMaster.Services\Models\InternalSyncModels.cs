using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.Models
{
    /// <summary>
    /// 内部系统组织单元DTO。
    /// </summary>
    public class OrganizationUnitDto
    {
        public long id { get; set; }
        public long? parentId { get; set; }
        public string displayName { get; set; }
        /// <summary>
        /// 备注字段，根据参考代码，这里似乎用于存储第三方的组织ID，用于映射。
        /// </summary>
        public string remarks { get; set; }
    }

    /// <summary>
    /// 内部系统用户DTO。
    /// </summary>
    public class UserDto
    {
        public string userName { get; set; }
        public long id { get; set; }
    }
}