﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;

namespace Docpark.HttpClientExtension.HttpClientExtions
{
    public static class HttpClientAuthenticate
    {
        private static string token = string.Empty;
        private static int expireInSeconds = 7200;
        private static DateTime tokenTime = DateTime.MinValue;
        private static object lockObject = new object();
        public static HttpClient AddAuthenticate(this HttpClient client, IDGrpc iDGrpc, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {

            if (httpContextAccessor != null && httpContextAccessor.HttpContext != null && httpContextAccessor.HttpContext.Request != null && httpContextAccessor.HttpContext.Request.Headers != null &&
                httpContextAccessor.HttpContext.Request.Headers.ContainsKey("Authorization"))
            {
                var authorizationHeader = httpContextAccessor.HttpContext
                   .Request.Headers["Authorization"];
                lock (lockObject)
                {
                    if (client.DefaultRequestHeaders.Contains("Authorization"))
                    {
                        client.DefaultRequestHeaders.Remove("Authorization");
                    }
                    client.DefaultRequestHeaders.Add("Authorization", authorizationHeader.ToString());
                }
            }
            else if (string.IsNullOrEmpty(token) || (DateTime.Now - tokenTime).TotalSeconds > expireInSeconds - 200)
            {
                var data = new Dictionary<string, string>();
#if DEBUG
                data["UserNameOrEmailAddress"] = "admin";
                data["Password"] = "123qwe";
#else
                data["UserNameOrEmailAddress"] = iDGrpc.GetStringAsync(ConstSettings.SystemAccount).Result;
                data["Password"] = iDGrpc.GetStringAsync(ConstSettings.SystemPassword).Result;
#endif
                data["RememberClient"] = "false";

                var identityServer = configuration["IdentityUrl"].ToString();
                identityServer = identityServer.EndsWith('/') ? identityServer : identityServer + "/";

                HttpClient _client = new HttpClient(new HttpClientHandler() { UseCookies = false });
                var hContent = new StringContent(JsonConvert.SerializeObject(data));
                hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                using (var response = client.PostAsync(identityServer + "api/TokenAuth/Authenticate", hContent).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的

                    string strResult = response.Content.ReadAsStringAsync().Result;
                    var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                    var result = payload["result"];
                    token = result["accessToken"].ToString();
                    expireInSeconds = int.Parse(result["expireInSeconds"].ToString());
                    tokenTime = DateTime.Now;
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
                }
            }
            else if (!client.DefaultRequestHeaders.Contains("Authorization"))
            {
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }

            return client;
        }
    }
}
