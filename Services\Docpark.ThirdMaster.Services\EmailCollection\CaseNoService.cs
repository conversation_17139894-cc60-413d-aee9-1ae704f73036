﻿using Docpark.ThirdMaster.Services.Models.MongoEntities;
using DocPark.MongoDb;
using MongoDB.Driver;
using System.Globalization;
using System;
using System.Threading.Tasks;
using System.Threading;

namespace Docpark.ThirdMaster.Services.EmailCollection
{
    public class CaseNoService : ICaseNoService
    {
        private readonly IMongodbManager mongodbManager;
        public CaseNoService(IMongodbManager mongodbManager)
        {
            this.mongodbManager = mongodbManager;
        }
        /// <summary>
        /// 初始化
        /// </summary>
        /// <returns></returns>
        public async Task Init()
        {
            var coll = mongodbManager.GetCollection<CaseNo_SerialNumber_Settings>("CaseNo_SerialNumber_Settings");
            var count = await coll.Find(w => true).CountDocumentsAsync();
            if (count <= 0)
            {

                var dc = new CaseNo_SerialNumber_Settings()
                {
                    Code = "DC",
                    CreateTime = DateTime.Now,
                    LastTime = DateTime.Now,
                    DateFormat = "yyyyMMdd",
                    Place = 6,
                    Reset = Enum_Rest.Day,
                    Value = 1,
                    ValueFormat = "DC$$$DATE$$$$$$SERIAL$$$"
                };
                var dz = new CaseNo_SerialNumber_Settings()
                {
                    Code = "YXCJ",
                    CreateTime = DateTime.Now,
                    LastTime = DateTime.Now,
                    DateFormat = "yyyyMMdd",
                    Place = 6,
                    Reset = Enum_Rest.Day,
                    Value = 1,
                    ValueFormat = "YXCJ$$$DATE$$$$$$SERIAL$$$"
                };
                await coll.InsertOneAsync(dc);
                await coll.InsertOneAsync(dz);
            }
        }
        //并发锁
        static SemaphoreSlim semaphoreSlim = new SemaphoreSlim(1);
        public async Task<string> Get(string code)
        {
            await semaphoreSlim.WaitAsync();

            var coll = mongodbManager.GetCollection<CaseNo_SerialNumber_Settings>("CaseNo_SerialNumber_Settings");
            var entity = await coll.Find(w => w.Code == code).FirstOrDefaultAsync();
            var result = entity.ValueFormat;
            var dtNow = DateTime.Now;
            //获取自动number
            var next = entity.Value + 1;//下一个value
            var needInit = next.ToString().Length > entity.Place;//如果自动number长度超过 位数需要重置
            switch (entity.Reset)
            {
                case Enum_Rest.Day:
                    if (!needInit)
                        needInit = entity.LastTime.Value.ToString("yyyyMMdd") != dtNow.ToString("yyyyMMdd");
                    break;
                case Enum_Rest.Week:
                    if (!needInit)
                    {
                        GregorianCalendar gc = new GregorianCalendar();
                        int lastWeekOfYear = gc.GetWeekOfYear(entity.LastTime.Value, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                        int nowWeekOfYear = gc.GetWeekOfYear(dtNow, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                        needInit = lastWeekOfYear != nowWeekOfYear;
                    }
                    break;
                case Enum_Rest.Month:
                    if (!needInit)
                        needInit = entity.LastTime.Value.ToString("yyyyMM") != dtNow.ToString("yyyyMM");
                    break;
                case Enum_Rest.Year:
                    if (!needInit)
                        needInit = entity.LastTime.Value.ToString("yyyy") != dtNow.ToString("yyyy");
                    break;
                case Enum_Rest.None:
                default:
                    break;
            }
            result = result.Replace("$$$SERIAL$$$", entity.Value.ToString().PadLeft(entity.Place, '0'));//替换
            entity.Value = needInit ? 1 : next;
            //获取时间
            if (!string.IsNullOrWhiteSpace(entity.DateFormat))
            {
                result = result.Replace("$$$DATE$$$", dtNow.ToString(entity.DateFormat));
            }
            //更新setting
            entity.LastTime = dtNow;

            await coll.ReplaceOneAsync(w => w.Id == entity.Id, entity);

            semaphoreSlim.Release();

            return result;
        }
    }
}
