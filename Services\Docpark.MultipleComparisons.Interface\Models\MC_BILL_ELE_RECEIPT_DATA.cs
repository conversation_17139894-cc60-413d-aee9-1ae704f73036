﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 单据电子回单接收数据
    /// </summary>
    [JsonTable("MC_BILL_ELE_RECEIPT_DATA")]
    public class MC_BILL_ELE_RECEIPT_DATA
    {
        /// <summary>
        /// 电子回单mstid
        /// </summary>
        public Guid mstid { get; set; }
        /// <summary>
        /// 交易流水号
        /// </summary>
        public string trades_serial_no { get; set; }
        /// <summary>
        /// 交易时间
        /// </summary>
        public string trades_time { get; set; }
        /// <summary>
        /// 本方银行账号
        /// </summary>
        public string our_bank_no { get; set; }
        /// <summary>
        /// 本方银行名称
        /// </summary>
        public string our_bank_name { get; set; }
        /// <summary>
        /// 借贷标记, DEBIT 借; CREDIT 贷
        /// </summary>
        public string debit_credit_mark { get; set; }
        /// <summary>
        /// 对方银行账号
        /// </summary>
        public string opp_bank_no { get; set; }
        /// <summary>
        /// 对方银行名称
        /// </summary>
        public string opp_bank_name { get; set; }
        /// <summary>
        /// 对方银行开户行
        /// </summary>
        public string opp_bank_deposit { get; set; }
        /// <summary>
        /// 交易金额
        /// </summary>
        public double trades_amount { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 用途
        /// </summary>
        public string trades_use { get; set; }
        /// <summary>
        /// 回单摘要
        /// </summary>
        public string trades_remark { get; set; }
        /// <summary>
        /// 电子回单编号
        /// </summary>
        public string ele_receipt_no { get; set; }
        /// <summary>
        /// 资金凭证号
        /// </summary>
        public string voucher_no { get; set; }
        /// <summary>
        /// oa流程编码
        /// </summary>
        public string oa_flow_code { get; set; }
        /// <summary>
        /// 影像数据
        /// </summary>
        public string img_data { get; set; }
        /// <summary>
        /// 文件类型（png/jpg/jpeg/pdf/ofd）
        /// </summary>
        public string img_data_filetype { get; set; }
        /// <summary>
        /// 是否发送, 0 未发送; 1 已发送
        /// </summary>
        public int ispost { get; set; }
    }
}
