using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 告警实体
    /// </summary>
    public class Alert
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 告警ID（用于去重和关联）
        /// </summary>
        [Required]
        public string AlertId { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 告警标题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        /// <summary>
        /// 告警描述
        /// </summary>
        [StringLength(1000)]
        public string Description { get; set; }

        /// <summary>
        /// 告警严重程度
        /// </summary>
        [Required]
        public AlertSeverity Severity { get; set; }

        /// <summary>
        /// 告警类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AlertType { get; set; }

        /// <summary>
        /// 告警来源
        /// </summary>
        [StringLength(100)]
        public string Source { get; set; }

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsAcknowledged { get; set; } = false;

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? AcknowledgedAt { get; set; }

        /// <summary>
        /// 确认者
        /// </summary>
        [StringLength(100)]
        public string AcknowledgedBy { get; set; }

        /// <summary>
        /// 是否已解决
        /// </summary>
        public bool IsResolved { get; set; } = false;

        /// <summary>
        /// 解决时间
        /// </summary>
        public DateTime? ResolvedAt { get; set; }

        /// <summary>
        /// 解决者
        /// </summary>
        [StringLength(100)]
        public string ResolvedBy { get; set; }

        /// <summary>
        /// 解决备注
        /// </summary>
        [StringLength(500)]
        public string ResolutionNotes { get; set; }

        /// <summary>
        /// 告警次数
        /// </summary>
        public int AlertCount { get; set; } = 1;

        /// <summary>
        /// 首次告警时间
        /// </summary>
        public DateTime FirstAlertTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后告警时间
        /// </summary>
        public DateTime LastAlertTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 告警数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument AlertData { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string[] Tags { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
