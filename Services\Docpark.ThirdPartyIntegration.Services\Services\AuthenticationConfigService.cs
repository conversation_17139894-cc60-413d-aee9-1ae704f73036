using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 授权配置服务实现
    /// </summary>
    public class AuthenticationConfigService : IAuthenticationConfigService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly IAuthenticationService _authenticationService;
        private readonly ILogger<AuthenticationConfigService> _logger;
        private const string CollectionName = "authentication_configs";

        public AuthenticationConfigService(
            IMongoRepository mongoRepository,
            IAuthenticationService authenticationService,
            ILogger<AuthenticationConfigService> logger)
        {
            _mongoRepository = mongoRepository;
            _authenticationService = authenticationService;
            _logger = logger;
        }

        public async Task<List<AuthenticationConfig>> GetAllAsync()
        {
            try
            {
                var filter = Builders<AuthenticationConfig>.Filter.Empty;
                return await _mongoRepository.FindListAsync<AuthenticationConfig>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all authentication configs");
                throw;
            }
        }

        public async Task<AuthenticationConfig> GetByIdAsync(string id)
        {
            try
            {
                var filter = Builders<AuthenticationConfig>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<AuthenticationConfig>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting authentication config by id: {Id}", id);
                throw;
            }
        }

        public async Task<AuthenticationConfig> CreateAsync(AuthenticationConfig config)
        {
            try
            {
                config.Id = null; // Let MongoDB generate the ID
                config.CreatedAt = DateTime.UtcNow;
                config.UpdatedAt = DateTime.UtcNow;

                await _mongoRepository.InsertOneAsync(CollectionName, config);
                _logger.LogInformation("Created authentication config: {Name}", config.Name);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating authentication config: {Name}", config.Name);
                throw;
            }
        }

        public async Task<AuthenticationConfig> UpdateAsync(AuthenticationConfig config)
        {
            try
            {
                config.UpdatedAt = DateTime.UtcNow;
                var filter = Builders<AuthenticationConfig>.Filter.Eq(x => x.Id, config.Id);
                
                await _mongoRepository.ReplaceOneAsync(CollectionName, filter, config);
                _logger.LogInformation("Updated authentication config: {Id}", config.Id);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating authentication config: {Id}", config.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                var filter = Builders<AuthenticationConfig>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.DeleteOneAsync<AuthenticationConfig>(CollectionName, filter);
                
                _logger.LogInformation("Deleted authentication config: {Id}", id);
                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting authentication config: {Id}", id);
                throw;
            }
        }

        public async Task<AuthenticationResult> TestAuthenticationAsync(string id)
        {
            try
            {
                var config = await GetByIdAsync(id);
                if (config == null)
                {
                    return AuthenticationResult.Failure("授权配置不存在");
                }

                var result = await _authenticationService.AuthenticateAsync(config);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Authentication test successful for config: {Id}", id);
                }
                else
                {
                    _logger.LogWarning("Authentication test failed for config: {Id}, Error: {Error}", id, result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing authentication config: {Id}", id);
                return AuthenticationResult.Failure($"测试过程中发生错误: {ex.Message}");
            }
        }

        public async Task<bool> SetEnabledAsync(string id, bool enabled)
        {
            try
            {
                var filter = Builders<AuthenticationConfig>.Filter.Eq(x => x.Id, id);
                var update = Builders<AuthenticationConfig>.Update
                    .Set(x => x.IsEnabled, enabled)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                var result = await _mongoRepository.UpdateOneAsync(CollectionName, filter, update);
                _logger.LogInformation("Set authentication config {Id} enabled to {Enabled}", id, enabled);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting authentication config enabled: {Id}", id);
                throw;
            }
        }
    }
}
