﻿using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.Services;
using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class WeatherForecastController : ControllerBase
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<WeatherForecastController> _logger;
        private readonly IObjectQuery objectQuery;
        private readonly IThirdpartyOrganizationService organizationService;

        public WeatherForecastController(ILogger<WeatherForecastController> logger,IObjectQuery objectQuery, IThirdpartyOrganizationService organizationService)
        {
            _logger = logger;
            this.objectQuery = objectQuery;
            this.organizationService = organizationService;
        }

        [HttpGet]
        public IEnumerable<WeatherForecast> Get()
        {
            var rng = new Random();
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = rng.Next(-20, 55),
                Summary = Summaries[rng.Next(Summaries.Length)]
            })
            .ToArray();
        }

        [HttpGet,Route("test")]
        public async Task<IActionResult> Test()
        {
            TestModel test = new TestModel() { no="1111", business_type="111"};

            Guid mstId = Guid.NewGuid();
            //新增或更新
            await objectQuery.CreateOrUpdate<TestModel>(mstId, test);

            //获取
            var entity=await objectQuery.Get<TestModel>(mstId);

            //获取列表
            var list= await objectQuery.GetList<TestModel>(new List<HttpClientExtension.IServices.Dto.Filter>(),
                new List<HttpClientExtension.IServices.Dto.Sorter>(),0,
                10);

            return new JsonResult(list.data);
        }

        [HttpGet, Route("TestSyncOrgDept")]
        public async Task<IActionResult> TestSyncOrgDept()
        {
            var list = this.organizationService.SyncThirdpartyOrganization(new Dictionary<string, object>()
            {
                ["returnOrgType"] = "[{\"type\":\"org\"},{\"type\":\"dept\"}]",
                ["count"] = 999,
                ["beginTimeStamp"] = new DateTime(1949,10,1).ToString("yyyy-MM-dd HH:mm:ss.fff")
            }, out string errMsg);

            return new JsonResult(list);
        }


        [HttpGet, Route("TestSyncPerson")]
        public async Task<IActionResult> TestSyncPerson()
        {
            var list = this.organizationService.SyncThirdpartyUser(new Dictionary<string, object>()
            {
                ["returnOrgType"] = "[{\"type\":\"Person\"}]",
                ["count"] = 999,
                ["beginTimeStamp"] = new DateTime(1949, 10, 1).ToString("yyyy-MM-dd HH:mm:ss.fff")
            }, out string errMsg);

            return new JsonResult(list);
        }

    }
}
