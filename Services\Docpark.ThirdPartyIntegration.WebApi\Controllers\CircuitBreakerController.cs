using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 熔断器管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CircuitBreakerController : ControllerBase
    {
        private readonly ICircuitBreakerService _circuitBreakerService;
        private readonly ILogger<CircuitBreakerController> _logger;

        public CircuitBreakerController(ICircuitBreakerService circuitBreakerService, ILogger<CircuitBreakerController> logger)
        {
            _circuitBreakerService = circuitBreakerService;
            _logger = logger;
        }

        /// <summary>
        /// 创建熔断器配置
        /// </summary>
        [HttpPost("configs")]
        public async Task<IActionResult> CreateCircuitBreakerConfig([FromBody] CircuitBreakerConfig config)
        {
            try
            {
                var id = await _circuitBreakerService.CreateCircuitBreakerConfigAsync(config);
                return Ok(new { id, message = "熔断器配置创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建熔断器配置失败");
                return StatusCode(500, new { message = "创建熔断器配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新熔断器配置
        /// </summary>
        [HttpPut("configs/{id}")]
        public async Task<IActionResult> UpdateCircuitBreakerConfig(string id, [FromBody] CircuitBreakerConfig config)
        {
            try
            {
                var success = await _circuitBreakerService.UpdateCircuitBreakerConfigAsync(id, config);
                if (success)
                {
                    return Ok(new { message = "熔断器配置更新成功" });
                }
                return NotFound(new { message = "熔断器配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新熔断器配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "更新熔断器配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取熔断器配置
        /// </summary>
        [HttpGet("configs/{id}")]
        public async Task<IActionResult> GetCircuitBreakerConfig(string id)
        {
            try
            {
                var config = await _circuitBreakerService.GetCircuitBreakerConfigAsync(id);
                if (config != null)
                {
                    return Ok(config);
                }
                return NotFound(new { message = "熔断器配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取熔断器配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据API配置ID获取熔断器配置
        /// </summary>
        [HttpGet("configs/api/{apiConfigId}")]
        public async Task<IActionResult> GetCircuitBreakerConfigByApiId(string apiConfigId)
        {
            try
            {
                var config = await _circuitBreakerService.GetCircuitBreakerConfigByApiIdAsync(apiConfigId);
                if (config != null)
                {
                    return Ok(config);
                }
                return NotFound(new { message = "熔断器配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据API配置ID获取熔断器配置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取熔断器配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有熔断器配置
        /// </summary>
        [HttpGet("configs")]
        public async Task<IActionResult> GetAllCircuitBreakerConfigs([FromQuery] bool enabledOnly = false)
        {
            try
            {
                var configs = await _circuitBreakerService.GetAllCircuitBreakerConfigsAsync(enabledOnly);
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器配置列表失败");
                return StatusCode(500, new { message = "获取熔断器配置列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除熔断器配置
        /// </summary>
        [HttpDelete("configs/{id}")]
        public async Task<IActionResult> DeleteCircuitBreakerConfig(string id)
        {
            try
            {
                var success = await _circuitBreakerService.DeleteCircuitBreakerConfigAsync(id);
                if (success)
                {
                    return Ok(new { message = "熔断器配置删除成功" });
                }
                return NotFound(new { message = "熔断器配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除熔断器配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除熔断器配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 检查熔断器状态
        /// </summary>
        [HttpGet("check/{apiConfigId}")]
        public async Task<IActionResult> CheckCircuitBreaker(string apiConfigId)
        {
            try
            {
                var result = await _circuitBreakerService.IsRequestAllowedAsync(apiConfigId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查熔断器状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "检查熔断器状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取熔断器状态
        /// </summary>
        [HttpGet("status/{apiConfigId}")]
        public async Task<IActionResult> GetCircuitBreakerStatus(string apiConfigId)
        {
            try
            {
                var status = await _circuitBreakerService.GetCircuitBreakerStatusAsync(apiConfigId);
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取熔断器状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动打开熔断器
        /// </summary>
        [HttpPost("open/{apiConfigId}")]
        public async Task<IActionResult> OpenCircuitBreaker(string apiConfigId, [FromBody] string reason = null)
        {
            try
            {
                var success = await _circuitBreakerService.OpenCircuitBreakerAsync(apiConfigId, reason);
                if (success)
                {
                    return Ok(new { message = "熔断器已打开" });
                }
                return BadRequest(new { message = "打开熔断器失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开熔断器失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "打开熔断器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动关闭熔断器
        /// </summary>
        [HttpPost("close/{apiConfigId}")]
        public async Task<IActionResult> CloseCircuitBreaker(string apiConfigId, [FromBody] string reason = null)
        {
            try
            {
                var success = await _circuitBreakerService.CloseCircuitBreakerAsync(apiConfigId, reason);
                if (success)
                {
                    return Ok(new { message = "熔断器已关闭" });
                }
                return BadRequest(new { message = "关闭熔断器失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭熔断器失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "关闭熔断器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重置熔断器状态
        /// </summary>
        [HttpPost("reset/{apiConfigId}")]
        public async Task<IActionResult> ResetCircuitBreaker(string apiConfigId)
        {
            try
            {
                var success = await _circuitBreakerService.ResetCircuitBreakerAsync(apiConfigId);
                if (success)
                {
                    return Ok(new { message = "熔断器状态已重置" });
                }
                return BadRequest(new { message = "重置熔断器状态失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置熔断器状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "重置熔断器状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取熔断器统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetCircuitBreakerStatistics([FromQuery] string apiConfigId = null, [FromQuery] DateTime? startTime = null, [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _circuitBreakerService.GetCircuitBreakerStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取熔断器统计信息失败");
                return StatusCode(500, new { message = "获取熔断器统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 记录请求结果
        /// </summary>
        [HttpPost("record")]
        public async Task<IActionResult> RecordRequestResult([FromQuery] string apiConfigId, [FromQuery] bool isSuccess, [FromQuery] long responseTimeMs = 0)
        {
            try
            {
                await _circuitBreakerService.RecordRequestResultAsync(apiConfigId, isSuccess, responseTimeMs);
                return Ok(new { message = "请求结果记录成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录请求结果失败");
                return StatusCode(500, new { message = "记录请求结果失败", error = ex.Message });
            }
        }
    }
}
