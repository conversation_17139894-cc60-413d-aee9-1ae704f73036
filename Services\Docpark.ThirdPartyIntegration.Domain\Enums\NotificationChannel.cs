namespace Docpark.ThirdPartyIntegration.Domain.Enums
{
    /// <summary>
    /// 通知渠道枚举
    /// </summary>
    public enum NotificationChannel
    {
        /// <summary>
        /// 邮件
        /// </summary>
        Email = 1,

        /// <summary>
        /// 短信
        /// </summary>
        SMS = 2,

        /// <summary>
        /// 微信
        /// </summary>
        WeChat = 3,

        /// <summary>
        /// 钉钉
        /// </summary>
        DingTalk = 4,

        /// <summary>
        /// Slack
        /// </summary>
        Slack = 5,

        /// <summary>
        /// Webhook
        /// </summary>
        Webhook = 6,

        /// <summary>
        /// 系统内通知
        /// </summary>
        InApp = 7
    }
}
