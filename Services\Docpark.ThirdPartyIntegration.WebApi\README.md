# 第三方接口集成系统

## 项目概述

这是一个可配置的第三方RESTful API集成系统，支持多种授权方式、灵活的参数配置、定时调用和数据映射处理。

## 功能特性

### 1. 多种授权方式支持
- **基础认证（BasicAuth）**: 用户名密码认证
- **OAuth2**: 客户端凭证模式
- **API密钥**: 支持Header或Query参数
- **Bearer令牌**: 直接使用访问令牌
- **自定义授权**: 可扩展的授权方式

### 2. 灵活的参数配置
- **固定值**: 静态参数
- **当前时间**: 支持多种时间格式
- **时间戳**: 秒级或毫秒级
- **上次执行时间**: 增量数据获取
- **动态参数**: 可扩展的动态值生成

### 3. 数据存储和管理
- **MongoDB存储**: 原始响应数据和解析后的结构化数据
- **数据去重**: 基于内容哈希的重复检测
- **数据统计**: 执行成功率、数据量等统计信息
- **数据清理**: 自动清理过期数据

### 4. 执行日志和监控
- **详细日志**: 记录每次API调用的完整信息
- **执行状态**: 运行中、成功、失败、超时、取消
- **性能监控**: 执行时间、响应大小等指标
- **错误追踪**: 详细的错误信息和堆栈跟踪

## 快速开始

### 1. 环境要求
- .NET Core 3.1+
- MongoDB 4.0+

### 2. 配置数据库
在 `appsettings.json` 中配置MongoDB连接：

```json
{
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ThirdPartyIntegration"
  }
}
```

### 3. 启动应用
```bash
cd Services/Docpark.ThirdPartyIntegration.WebApi
dotnet run
```

应用启动后，访问 `http://localhost:5000` 查看Swagger API文档。

## API使用示例

### 1. 创建授权配置

**BasicAuth示例：**
```json
{
  "name": "测试API基础认证",
  "type": 1,
  "parameters": {
    "username": "your_username",
    "password": "your_password"
  }
}
```

**OAuth2示例：**
```json
{
  "name": "OAuth2认证",
  "type": 2,
  "parameters": {
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret",
    "tokenUrl": "https://api.example.com/oauth/token",
    "scope": "read write"
  }
}
```

### 2. 创建API配置

```json
{
  "name": "用户数据API",
  "description": "获取用户数据",
  "baseUrl": "https://api.example.com",
  "endpoint": "/api/users",
  "method": "GET",
  "authenticationConfigId": "授权配置ID",
  "parameters": [
    {
      "name": "page",
      "location": "query",
      "type": 1,
      "value": "1",
      "isRequired": true
    },
    {
      "name": "updated_since",
      "location": "query",
      "type": 4,
      "format": "yyyy-MM-ddTHH:mm:ssZ",
      "isRequired": false
    }
  ],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": true
  }
}
```

### 3. 手动执行API

```bash
POST /api/ApiExecution/{apiConfigId}/execute
```

### 4. 查看执行结果

```bash
GET /api/ApiResponseData/by-api/{apiConfigId}
```

## 参数类型说明

| 类型 | 值 | 说明 | 示例 |
|------|----|----- |------|
| Static | 1 | 固定值 | "固定字符串" |
| CurrentTime | 2 | 当前时间 | format: "yyyy-MM-dd" |
| Timestamp | 3 | 时间戳 | format: "seconds" 或 "milliseconds" |
| LastExecutionTime | 4 | 上次执行时间 | format: "iso8601" |
| IncrementalStartTime | 5 | 增量同步起始时间 | format: "iso8601" |
| DataVersion | 6 | 数据版本号 | format: "guid" 或 "timestamp" |
| SyncCheckpoint | 7 | 同步检查点 | format: "auto_increment" |
| Dynamic | 99 | 动态值（可扩展） | 自定义逻辑 |

## 时间格式支持

- `iso8601`: 2023-12-01T10:30:00.000Z
- `date`: 2023-12-01
- `time`: 10:30:00
- `timestamp`: **********
- `timestamp_ms`: **********000
- 自定义格式: 使用.NET DateTime格式字符串

## 数据库集合

- `authentication_configs`: 授权配置
- `api_configurations`: API配置
- `api_execution_logs`: 执行日志
- `api_response_data`: 响应数据
- `sync_configurations`: 同步配置
- `sync_sessions`: 同步会话
- `data_conflicts`: 数据冲突
- `data_hashes`: 数据哈希记录
- `job_execution_history`: 任务执行历史
- `health_checks`: 健康检查记录
- `performance_metrics`: 性能指标数据
- `alert_rules`: 告警规则配置
- `alerts`: 告警记录
- `rate_limit_rules`: 限流规则配置
- `rate_limit_counters`: 限流计数器
- `circuit_breaker_configs`: 熔断器配置
- `load_balancer_configs`: 负载均衡配置
- `api_gateway_logs`: API网关日志

## 扩展开发

### 添加新的授权方式
1. 在 `AuthenticationType` 枚举中添加新类型
2. 在 `AuthenticationService.AuthenticateAsync` 方法中添加处理逻辑

### 添加新的参数类型
1. 在 `ParameterType` 枚举中添加新类型
2. 在 `ParameterProcessingService.ProcessParameter` 方法中添加处理逻辑

## 注意事项

1. **安全性**: 敏感信息（如密码、密钥）建议加密存储
2. **性能**: 大量数据时建议配置适当的MongoDB索引
3. **监控**: 建议配置日志监控和告警
4. **备份**: 定期备份MongoDB数据

## 技术栈

- **后端**: ASP.NET Core 3.1
- **数据库**: MongoDB
- **HTTP客户端**: HttpClient
- **序列化**: Newtonsoft.Json
- **API文档**: Swagger/OpenAPI

## 第二阶段新增功能

### 1. 数据映射和转换功能 ✅

- **灵活的数据映射**: 支持JSON路径映射、字段重命名、数据类型转换
- **多种转换类型**: 字符串、数字、日期、布尔值、JSON对象等转换
- **数据验证**: 支持正则表达式、范围验证、格式验证等
- **映射预览**: 提供映射配置的预览和测试功能

**相关API端点:**
- `POST /api/DataMapping/validate` - 验证映射配置
- `POST /api/DataMapping/preview` - 生成映射预览
- `POST /api/DataMapping/test-transform` - 测试数据转换
- `PUT /api/DataMapping/api-config/{id}/mapping` - 更新API配置的映射设置

### 2. 响应数据处理功能 ✅

- **数据去重**: 基于哈希值、字段值或内容的智能去重
- **数据过滤**: 支持多种过滤条件和逻辑操作符
- **数据聚合**: 支持计数、求和、平均值、最大值、最小值等聚合操作
- **数据验证**: 多种验证规则和失败处理策略
- **批处理**: 支持大数据量的批量处理

**相关API端点:**
- `POST /api/ResponseProcessing/test-filter` - 测试数据过滤
- `POST /api/ResponseProcessing/test-validation` - 测试数据验证
- `POST /api/ResponseProcessing/test-aggregation` - 测试数据聚合
- `PUT /api/ResponseProcessing/api-config/{id}/processing` - 更新响应处理配置

### 3. 定时任务调度系统 ✅

- **基于Quartz.NET**: 企业级任务调度引擎
- **多种调度策略**: 间隔调度、每日调度、Cron表达式调度
- **任务管理**: 启动、停止、暂停、恢复、立即执行
- **执行历史**: 详细的任务执行记录和统计信息
- **错误处理**: 重试机制、超时处理、错失触发策略

**相关API端点:**
- `POST /api/Scheduler/start` - 启动调度器
- `POST /api/Scheduler/schedule/{apiConfigId}` - 调度API任务
- `GET /api/Scheduler/jobs` - 获取所有任务状态
- `POST /api/Scheduler/trigger/{jobKey}` - 立即执行任务
- `GET /api/Scheduler/statistics` - 获取调度器统计信息

### 4. 数据同步和增量更新系统 ✅

- **智能同步策略**: 支持全量同步、增量同步、差异同步、检查点同步、混合同步
- **冲突解决机制**: 多种冲突解决策略，包括跳过、覆盖、保留、合并、版本控制、手动处理
- **同步会话管理**: 完整的同步会话生命周期管理和进度跟踪
- **数据版本控制**: 支持数据版本号和检查点机制
- **同步统计分析**: 详细的同步统计信息和性能分析

**相关API端点:**
- `POST /api/DataSync/configurations` - 创建同步配置
- `PUT /api/DataSync/configurations/{id}` - 更新同步配置
- `GET /api/DataSync/configurations/{id}` - 获取同步配置
- `POST /api/DataSync/execute/incremental/{apiConfigId}` - 执行增量同步
- `POST /api/DataSync/execute/full/{apiConfigId}` - 执行全量同步
- `GET /api/DataSync/sessions/{sessionId}` - 获取同步会话
- `GET /api/DataSync/conflicts/unresolved` - 获取未解决的冲突
- `POST /api/DataSync/conflicts/{conflictId}/resolve` - 解决数据冲突
- `GET /api/DataSync/statistics/{apiConfigId}` - 获取同步统计信息

### 5. 监控和告警系统 ✅

- **健康检查**: 自动化API健康状态检测，支持批量检查和历史记录
- **性能监控**: 响应时间、可用性、吞吐量等关键指标监控
- **告警规则**: 灵活的告警规则配置，支持多种触发条件和阈值设置
- **多渠道通知**: 支持邮件、短信、微信、钉钉、Slack、Webhook等通知方式
- **告警管理**: 告警确认、解决、静默等完整的告警生命周期管理
- **统计分析**: 详细的监控和告警统计信息，支持趋势分析

**相关API端点:**
- `POST /api/Monitoring/health-check/{apiConfigId}` - 执行健康检查
- `POST /api/Monitoring/health-check/batch` - 批量健康检查
- `GET /api/Monitoring/health-overview` - 获取系统健康状态概览
- `POST /api/Monitoring/metrics` - 记录性能指标
- `GET /api/Monitoring/statistics/{apiConfigId}` - 获取性能统计信息
- `POST /api/Alert/rules` - 创建告警规则
- `GET /api/Alert/active` - 获取活跃告警
- `POST /api/Alert/acknowledge/{alertId}` - 确认告警
- `POST /api/Alert/resolve/{alertId}` - 解决告警
- `GET /api/Alert/statistics` - 获取告警统计信息

### 6. API网关和限流系统 🚧

- **限流规则**: 支持固定窗口、滑动窗口、令牌桶、漏桶等多种限流算法
- **熔断器**: 自动故障检测和恢复，支持半开状态试探
- **负载均衡**: 多种负载均衡策略，包括轮询、加权、最少连接等
- **API网关**: 统一的API入口，支持路由、认证、限流、熔断等功能
- **请求日志**: 详细的API网关请求日志和统计分析

**相关API端点:**
- `POST /api/RateLimit/rules` - 创建限流规则
- `GET /api/RateLimit/check/{apiConfigId}` - 检查限流状态
- `POST /api/CircuitBreaker/configs` - 创建熔断器配置
- `GET /api/CircuitBreaker/status/{apiConfigId}` - 获取熔断器状态
- `POST /api/Gateway/process` - 处理网关请求
- `GET /api/Gateway/logs` - 获取网关日志

### 7. 第三阶段开发完成情况

✅ **数据同步和增量更新系统** - 完成
- 智能同步策略和冲突解决机制
- 完整的同步会话管理和进度跟踪
- 数据版本控制和检查点机制

✅ **监控和告警系统** - 完成
- 自动化健康检查和性能监控
- 灵活的告警规则和多渠道通知
- 完整的告警生命周期管理

✅ **Web管理界面增强** - 完成
- 新增数据同步管理界面
- 新增告警管理界面
- 增强监控界面功能

🚧 **API网关和限流系统** - 进行中
- 限流服务基础框架已完成
- 熔断器和负载均衡功能开发中

## 技术栈更新

- **后端**: ASP.NET Core 3.1
- **数据库**: MongoDB
- **任务调度**: Quartz.NET 3.4.0
- **HTTP客户端**: HttpClient
- **序列化**: Newtonsoft.Json
- **API文档**: Swagger/OpenAPI

## 许可证

本项目采用MIT许可证。
