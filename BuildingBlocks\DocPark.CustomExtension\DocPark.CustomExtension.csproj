<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="3.1.3" />
    <PackageReference Include="Autofac" Version="6.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.15.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.Kubernetes" Version="1.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.9" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.9" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="3.1.3" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.9" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.9" />
    <PackageReference Include="Serilog.AspNetCore" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="5.6.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.8.0" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.2.22" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\Devspaces.Support\Devspaces.Support.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\EventBusRabbitMQ\EventBusRabbitMQ.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\EventBusServiceBus\EventBusServiceBus.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\EventBus\EventBus.csproj" />
    <ProjectReference Include="..\DocPark.MongoDb\DocPark.MongoDb.csproj" />
  </ItemGroup>

</Project>
