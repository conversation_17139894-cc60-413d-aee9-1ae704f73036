using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 授权服务实现
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly IMongoRepository _mongoRepository;
        private const string CollectionName = "authentication_configs";

        public AuthenticationService(HttpClient httpClient, ILogger<AuthenticationService> logger, IMongoRepository mongoRepository)
        {
            _httpClient = httpClient;
            _logger = logger;
            _mongoRepository = mongoRepository;
        }

        public async Task<AuthenticationResult> AuthenticateAsync(AuthenticationConfig config)
        {
            try
            {
                switch (config.Type)
                {
                    case AuthenticationType.BasicAuth:
                        return await HandleBasicAuthAsync(config);
                    case AuthenticationType.OAuth2:
                        return await HandleOAuth2Async(config);
                    case AuthenticationType.ApiKey:
                        return HandleApiKey(config);
                    case AuthenticationType.BearerToken:
                        return HandleBearerToken(config);
                    case AuthenticationType.Custom:
                        return await HandleCustomAuthAsync(config);
                    default:
                        return AuthenticationResult.Failure($"Unsupported authentication type: {config.Type}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for config: {ConfigId}", config.Id);
                return AuthenticationResult.Failure($"Authentication failed: {ex.Message}");
            }
        }

        private async Task<AuthenticationResult> HandleBasicAuthAsync(AuthenticationConfig config)
        {
            // 检查是否配置了登录API
            if (config.Parameters.TryGetValue("loginUrl", out var loginUrl) && !string.IsNullOrEmpty(loginUrl))
            {
                return await HandleBasicAuthWithLoginApiAsync(config, loginUrl);
            }
            else
            {
                if (!config.Parameters.TryGetValue("username", out var username) ||
                !config.Parameters.TryGetValue("password", out var password))
                {
                    return AuthenticationResult.Failure("Username and password are required for Basic Auth");
                }
                // 传统的Basic Auth方式
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
                var token = $"Basic {credentials}";

                var result = AuthenticationResult.Success(token);
                result.Headers["Authorization"] = token;
                return result;
            }
        }

        private async Task<AuthenticationResult> HandleBasicAuthWithLoginApiAsync(
            AuthenticationConfig config, string loginUrl)
        {
            try
            {
                // 检查缓存的令牌是否有效
                if (config.TokenCache?.IsValid == true)
                {
                    var cachedResult = AuthenticationResult.Success(config.TokenCache.AccessToken,
                        config.TokenCache.RefreshToken, config.TokenCache.ExpiresAt);
                    cachedResult.Headers["Authorization"] = $"{config.TokenCache.TokenType} {config.TokenCache.AccessToken}";
                    return cachedResult;
                }

                // 构建登录请求 - 使用前端配置的JSON参数
                var loginRequest = new Dictionary<string, object>();

                // 复制所有参数，但排除特殊的配置参数
                var excludeKeys = new HashSet<string> { "loginUrl", "tokenPath", "refreshTokenPath", "expiresInPath" };

                foreach (var param in config.Parameters)
                {
                    if (!excludeKeys.Contains(param.Key))
                    {
                        loginRequest[param.Key] = param.Value;
                    }
                }

                // 序列化并发送请求
                var json = JsonConvert.SerializeObject(loginRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(loginUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    return await ProcessLoginResponseAsync(config, response);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return AuthenticationResult.Failure($"Login failed: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                return AuthenticationResult.Failure($"Login request failed: {ex.Message}");
            }
        }

        private async Task<AuthenticationResult> ProcessLoginResponseAsync(AuthenticationConfig config, HttpResponseMessage response)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var responseJson = JsonConvert.DeserializeObject<dynamic>(responseContent);

            // 获取令牌路径配置，用户必须配置 - 优先从ResponseConfig中获取，如果没有则从Parameters中获取（向后兼容）
            var tokenPath = GetConfigValue(config, "tokenPath");
            if (string.IsNullOrEmpty(tokenPath))
            {
                return AuthenticationResult.Failure("tokenPath is required for login API authentication");
            }

            // 获取可选的路径配置 - 优先从ResponseConfig中获取，如果没有则从Parameters中获取（向后兼容）
            var refreshTokenPath = GetConfigValue(config, "refreshTokenPath");
            var expiresInPath = GetConfigValue(config, "expiresInPath");

            // 从响应中提取令牌
            var token = GetNestedValue(responseJson, tokenPath)?.ToString();
            var refreshToken = !string.IsNullOrEmpty(refreshTokenPath) ? GetNestedValue(responseJson, refreshTokenPath)?.ToString() : null;
            var expiresInStr = !string.IsNullOrEmpty(expiresInPath) ? GetNestedValue(responseJson, expiresInPath)?.ToString() : null;

            if (string.IsNullOrEmpty(token))
            {
                return AuthenticationResult.Failure($"Token not found in response at path: {tokenPath}. Response: {responseContent}");
            }

            // 计算过期时间
            DateTime? expiresAt = null;
            if (!string.IsNullOrEmpty(expiresInStr))
            {
                if (int.TryParse(expiresInStr, out int expiresIn) && expiresIn > 0)
                {
                    expiresAt = DateTime.UtcNow.AddSeconds(expiresIn);
                }
            }

            // 更新令牌缓存
            config.TokenCache = new TokenCache
            {
                AccessToken = token,
                RefreshToken = refreshToken,
                TokenType = "Bearer",
                ExpiresAt = expiresAt,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // 这里应该保存配置到数据库以持久化令牌缓存
            // 但为了简化，我们暂时只在内存中缓存

            var result = AuthenticationResult.Success(token, refreshToken, expiresAt);
            result.Headers["Authorization"] = $"Bearer {token}";
            return result;
        }

        private async Task<AuthenticationResult> HandleOAuth2Async(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("tokenUrl", out var tokenUrl))
            {
                return AuthenticationResult.Failure("TokenUrl is required for OAuth2");
            }

            // 构建OAuth2请求 - 使用前端配置的JSON参数
            var requestData = new Dictionary<string, object>();

            // 复制所有参数，但排除tokenUrl
            foreach (var param in config.Parameters)
            {
                if (param.Key != "tokenUrl")
                {
                    requestData[param.Key] = param.Value;
                }
            }

            // 如果没有配置grant_type，默认使用client_credentials
            if (!requestData.ContainsKey("grantType") && !requestData.ContainsKey("grant_type"))
            {
                requestData["grant_type"] = "client_credentials";
            }
            else if (requestData.ContainsKey("grantType"))
            {
                // 将grantType转换为OAuth2标准的grant_type
                requestData["grant_type"] = requestData["grantType"];
                requestData.Remove("grantType");
            }

            // OAuth2通常使用form-urlencoded格式
            var formData = new List<KeyValuePair<string, string>>();
            foreach (var item in requestData)
            {
                formData.Add(new KeyValuePair<string, string>(item.Key, item.Value?.ToString() ?? ""));
            }

            var content = new FormUrlEncodedContent(formData);
            var response = await _httpClient.PostAsync(tokenUrl, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();

                // 检查是否配置了自定义响应路径
                var tokenPath = GetConfigValue(config, "tokenPath");
                if (!string.IsNullOrEmpty(tokenPath))
                {
                    // 使用自定义路径解析响应
                    var responseJson = JsonConvert.DeserializeObject<dynamic>(responseContent);

                    var token = GetNestedValue(responseJson, tokenPath)?.ToString();
                    if (string.IsNullOrEmpty(token))
                    {
                        return AuthenticationResult.Failure($"Token not found in OAuth2 response at path: {tokenPath}. Response: {responseContent}");
                    }

                    // 获取可选的路径配置 - 优先从ResponseConfig中获取，如果没有则从Parameters中获取（向后兼容）
                    var refreshTokenPath = GetConfigValue(config, "refreshTokenPath");
                    var expiresInPath = GetConfigValue(config, "expiresInPath");

                    var refreshToken = !string.IsNullOrEmpty(refreshTokenPath) ? GetNestedValue(responseJson, refreshTokenPath)?.ToString() : null;
                    var expiresInStr = !string.IsNullOrEmpty(expiresInPath) ? GetNestedValue(responseJson, expiresInPath)?.ToString() : null;

                    // 计算过期时间
                    DateTime? expiresAt = null;
                    if (!string.IsNullOrEmpty(expiresInStr))
                    {
                        if (int.TryParse(expiresInStr, out int expiresIn) && expiresIn > 0)
                        {
                            expiresAt = DateTime.UtcNow.AddSeconds(expiresIn);
                        }
                    }

                    return AuthenticationResult.Success(token, refreshToken, expiresAt);
                }
                else
                {
                    // 使用标准OAuth2响应格式
                    var tokenResponse = JsonConvert.DeserializeObject<OAuth2TokenResponse>(responseContent);

                    var expiresAt = tokenResponse.ExpiresIn.HasValue
                        ? DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn.Value)
                        : (DateTime?)null;

                    return AuthenticationResult.Success(tokenResponse.AccessToken, tokenResponse.RefreshToken, expiresAt);
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                return AuthenticationResult.Failure($"OAuth2 authentication failed: {response.StatusCode} - {errorContent}");
            }
        }

        private AuthenticationResult HandleApiKey(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("keyName", out var keyName) ||
                !config.Parameters.TryGetValue("keyValue", out var keyValue))
            {
                return AuthenticationResult.Failure("KeyName and KeyValue are required for API Key authentication");
            }

            var result = AuthenticationResult.Success(keyValue);
            result.AdditionalData["keyName"] = keyName;
            result.AdditionalData["location"] = config.Parameters.GetValueOrDefault("location", "header");

            // 设置Headers
            var location = config.Parameters.GetValueOrDefault("location", "header");
            if (location.ToLower() == "header")
            {
                result.Headers[keyName] = keyValue;
            }

            return result;
        }

        private AuthenticationResult HandleBearerToken(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("token", out var token))
            {
                return AuthenticationResult.Failure("Token is required for Bearer Token authentication");
            }

            var result = AuthenticationResult.Success(token);
            result.Headers["Authorization"] = $"Bearer {token}";
            return result;
        }

        private async Task<AuthenticationResult> HandleCustomAuthAsync(AuthenticationConfig config)
        {
            // 自定义授权逻辑，可以根据需要扩展
            _logger.LogWarning("Custom authentication not implemented for config: {ConfigId}", config.Id);
            return AuthenticationResult.Failure("Custom authentication not implemented");
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            // 实现令牌验证逻辑
            return !string.IsNullOrEmpty(token);
        }

        public async Task<string> RefreshTokenAsync(string refreshToken)
        {
            // 实现令牌刷新逻辑
            throw new NotImplementedException("Token refresh not implemented");
        }

        public async Task<string> GetAuthorizationHeaderAsync(AuthenticationConfig config)
        {
            var result = await AuthenticateAsync(config);
            if (!result.IsSuccess)
            {
                throw new InvalidOperationException($"Authentication failed: {result.ErrorMessage}");
            }

            switch (config.Type)
            {
                case AuthenticationType.BasicAuth:
                    return result.AccessToken; // Already includes "Basic " prefix
                case AuthenticationType.OAuth2:
                case AuthenticationType.BearerToken:
                    return $"Bearer {result.AccessToken}";
                case AuthenticationType.ApiKey:
                    // API Key handling depends on location (header/query)
                    return result.AccessToken;
                default:
                    return result.AccessToken;
            }
        }

        /// <summary>
        /// 根据ID获取授权配置
        /// </summary>
        public async Task<AuthenticationConfig> GetByIdAsync(string id)
        {
            try
            {
                var filter = Builders<AuthenticationConfig>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<AuthenticationConfig>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取授权配置失败，ID: {Id}", id);
                throw;
            }
        }

        private void SetNestedValue(Dictionary<string, object> dict, string path, object value)
        {
            var parts = path.Split('.');
            var current = dict;

            for (int i = 0; i < parts.Length - 1; i++)
            {
                if (!current.ContainsKey(parts[i]))
                {
                    current[parts[i]] = new Dictionary<string, object>();
                }
                current = (Dictionary<string, object>)current[parts[i]];
            }

            current[parts[parts.Length - 1]] = value;
        }

        /// <summary>
        /// 获取配置值，优先从ResponseConfig中获取，如果没有则从Parameters中获取（向后兼容）
        /// </summary>
        private string GetConfigValue(AuthenticationConfig config, string key)
        {
            // 优先从ResponseConfig中获取
            if (config.ResponseConfig != null && config.ResponseConfig.TryGetValue(key, out var responseValue))
            {
                return responseValue;
            }

            // 如果ResponseConfig中没有，则从Parameters中获取（向后兼容）
            if (config.Parameters != null && config.Parameters.TryGetValue(key, out var paramValue))
            {
                return paramValue;
            }

            return null;
        }

        private object GetNestedValue(dynamic obj, string path)
        {
            var parts = path.Split('.');
            dynamic current = obj;

            foreach (var part in parts)
            {
                if (current == null) return null;

                if (current is Newtonsoft.Json.Linq.JObject jobj)
                {
                    current = jobj[part];
                }
                else if (current.GetType().GetProperty(part) != null)
                {
                    current = current.GetType().GetProperty(part).GetValue(current);
                }
                else
                {
                    return null;
                }
            }

            return current;
        }
    }

    /// <summary>
    /// OAuth2令牌响应模型
    /// </summary>
    public class OAuth2TokenResponse
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("expires_in")]
        public int? ExpiresIn { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }
    }
}
