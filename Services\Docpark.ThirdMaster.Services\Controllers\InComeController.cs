﻿using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.ThirdMaster.Services.Models;
using DocPark.Commons;
using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using Serilog.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.Controllers
{
    /// <summary>
    /// 进项发票控制器
    [Authorize]
    public class InComeController : BaseController
    {
        private readonly IObjectQuery objectQuery;
        private readonly IDocparkHostService docparkHostService;
        private readonly ILogger<InComeController> logger;
        private readonly IConfiguration configuration;
        private readonly IDocparkFileService docparkFileService;
        JsonSerializerSettings serializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new UnderlineSplitContractResolver()
        };

        /// <summary>
        /// 票夹发票的文档标识
        /// </summary>
        private readonly string Piao_Jia_Document_Identity = "Receipt";
        /// <summary>
        /// 税务局发票的文档标识
        /// </summary>
        private readonly string Tax_Invoice_Document_Identity = "InCome-Tax-Invoice";

        public InComeController(IObjectQuery objectQuery, IDocparkHostService docparkHostService,
            ILogger<InComeController> logger,IConfiguration configuration,IDocparkFileService docparkFileService)
        {
            this.objectQuery = objectQuery;
            this.docparkHostService = docparkHostService;
            this.logger = logger;
            this.configuration = configuration;
            this.docparkFileService = docparkFileService;
            // Constructor logic if needed
        }

        /// <summary>
        /// 获取进项发票信息
        /// 给到第三方进行报销的票据池
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultDataDto> GetInvoices(Dictionary<string, object> input)
        {
            StringBuilder message = new StringBuilder();
            try
            {
                var search = input.getJObject("search");
                if ((search == null || !search.HasValues) && input.getString("type") == "inv_tax")
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "请至少输入一个查询条件"
                    };
                }
                if (input.getString("type") != "inv_tax")
                {
                    return await GetReceiptInvoices(input);
                }
                return await GetTaxInvoices(input);
            }
            catch (Exception ex)
            {
                message.AppendLine(ex.Message);
                message.AppendLine(ex.StackTrace);
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "系统处理异常"
                };
            }
            finally
            {
                if (message.Length > 0)
                {
                    logger.LogError($"【income】【GetInvoices】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
                else
                {
                    logger.LogInformation($"【income】【GetInvoices】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
            }
        }

        /// <summary>
        /// 发票报销
        /// 第三方报销流程发起之后更新发票状态和报销单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultDataDto> Reimbursement(Dictionary<string, object> input)
        {
            StringBuilder message = new StringBuilder();
            try
            {
                int status = 2;//报销中
                var documentNo = input.getString("documentNo");
                var invoices = input.getJArray("invoices");
                if (string.IsNullOrEmpty(documentNo))
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "报销单不能为空"
                    };
                if (invoices == null || invoices.Count == 0)
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "发票不能为空"
                    };
                }
                List<Dictionary<string, object>> receipt_invoice_datas = new List<Dictionary<string, object>>(); //票夹发票
                List<Dictionary<string, object>> tax_invoice_invoice_datas = new List<Dictionary<string, object>>(); //税务局发票
                foreach (JObject item in invoices)
                {
                    if (item.getString("type") == Piao_Jia_Document_Identity)
                    {
                        receipt_invoice_datas.Add(new Dictionary<string, object> {
                        {"invoice_status", status },
                        {"CaseNo",documentNo },
                        { "MstId",item.getString("MstId") }
                    });
                    }
                    else
                        tax_invoice_invoice_datas.Add(new Dictionary<string, object> {
                        {"invoice_status", status },
                        {"CaseNo",documentNo },
                        { "MstId",item.getString("MstId") }
                    });
                }
                if (receipt_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Piao_Jia_Document_Identity, receipt_invoice_datas);
                }
                if (tax_invoice_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Tax_Invoice_Document_Identity, tax_invoice_invoice_datas);
                }
                return new ResultDataDto()
                {
                    IsSuccess = true
                };
            }
            catch (Exception ex)
            {
                message.AppendLine(ex.Message);
                message.AppendLine(ex.StackTrace);
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "系统处理异常"
                };
            }
            finally
            {
                if (message.Length > 0)
                {
                    logger.LogError($"【income】【Reimbursement】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
                else
                {
                    logger.LogInformation($"【income】【Reimbursement】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
            }
        }
        /// <summary>
        /// 生成凭证
        /// </summary>
        /// <returns></returns>
        public async Task<ResultDataDto> GenerateVoucher(Dictionary<string, object> input)
        {
            StringBuilder message = new StringBuilder();
            try
            {
                int status = 3;//已报销
                var documentNo = input.getString("documentNo");
                var voucherNo = input.getString("voucherNo");
                var voucherTime = input.getString("voucherTime");
                var invoices = input.getJArray("invoices");
                if (string.IsNullOrEmpty(documentNo) || string.IsNullOrEmpty(voucherNo))
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "报销单或者凭证号不能为空"
                    };
                }
                if (invoices == null || invoices.Count == 0)
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "发票不能为空"
                    };
                }
                List<Dictionary<string, object>> receipt_invoice_datas = new List<Dictionary<string, object>>(); //票夹发票
                List<Dictionary<string, object>> tax_invoice_invoice_datas = new List<Dictionary<string, object>>(); //税务局发票
                foreach (JObject item in invoices)
                {
                    if (item.getString("type") == Piao_Jia_Document_Identity)
                    {
                        receipt_invoice_datas.Add(new Dictionary<string, object> {
                            {"invoice_status",status },
                            {"dlpzhm",voucherNo },
                            {"dlrzrq",voucherTime },
                            { "MstId",item.getString("MstId") }
                         });
                    }
                    else
                        tax_invoice_invoice_datas.Add(new Dictionary<string, object> {
                            {"invoice_status",status },
                            {"dlpzhm",voucherNo },
                            {"dlrzrq",voucherTime },
                            { "MstId",item.getString("MstId") }
                        });
                }
                if (receipt_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Piao_Jia_Document_Identity, receipt_invoice_datas);
                }
                if (tax_invoice_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Tax_Invoice_Document_Identity, tax_invoice_invoice_datas);
                }
                return new ResultDataDto()
                {
                    IsSuccess = true
                };
            }
            catch (Exception ex)
            {
                message.AppendLine(ex.Message);
                message.AppendLine(ex.StackTrace);
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "系统处理异常"
                };
            }
            finally
            {
                if (message.Length > 0)
                {
                    logger.LogError($"【income】【GenerateVoucher】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
                else
                {
                    logger.LogInformation($"【income】【GenerateVoucher】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
            }
        }

        /// <summary>
        /// 删除发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ResultDataDto> RemoveInvoices(Dictionary<string, object> input)
        {
            StringBuilder message = new StringBuilder();
            try
            {
                int status = 1;//未报销
                var documentNo = input.getString("documentNo");
                var invoices = input.getJArray("invoices");
                if (string.IsNullOrEmpty(documentNo))
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "报销单或者凭证号不能为空"
                    };
                }
                if (invoices == null || invoices.Count == 0)
                {
                    return new ResultDataDto()
                    {
                        IsSuccess = false,
                        Message = "发票不能为空"
                    };
                }
                List<Dictionary<string, object>> receipt_invoice_datas = new List<Dictionary<string, object>>(); //票夹发票
                List<Dictionary<string, object>> tax_invoice_invoice_datas = new List<Dictionary<string, object>>(); //税务局发票
                foreach (JObject item in invoices)
                {
                    if (item.getString("type") == Piao_Jia_Document_Identity)
                    {
                        receipt_invoice_datas.Add(new Dictionary<string, object> {
                        {"invoice_status",status },
                        {"CaseNo","" },
                        {"dlpzhm","" },
                        {"dlrzrq","" },
                        { "MstId",item.getString("MstId") }
                    });
                    }
                    else
                        tax_invoice_invoice_datas.Add(new Dictionary<string, object> {
                        {"invoice_status",status },
                        {"CaseNo","" },
                        {"dlpzhm","" },
                        {"dlrzrq","" },
                        { "MstId",item.getString("MstId") }
                    });
                }
                if (receipt_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Piao_Jia_Document_Identity, receipt_invoice_datas);
                }
                if (tax_invoice_invoice_datas.Count > 0)
                {
                    await UpdateInvoice(Tax_Invoice_Document_Identity, tax_invoice_invoice_datas);
                }
                return new ResultDataDto()
                {
                    IsSuccess = true
                };
            }
            catch (Exception ex)
            {
                message.AppendLine(ex.Message);
                message.AppendLine(ex.StackTrace);
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "系统处理异常"
                };
            }
            finally
            {
                if (message.Length > 0)
                {
                    logger.LogError($"【income】【RemoveInvoices】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
                else
                {
                    logger.LogInformation($"【income】【RemoveInvoices】【{DateTime.Now}】【{CurrentUser.name}】【{input.ToJsonString()}】");
                }
            }
        }

        #region 获取发票信息
        /// <summary>
        /// 获取票夹发票信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<ResultDataDto> GetReceiptInvoices(Dictionary<string, object> input)
        {

            var search = input.getJObject("search");
            var pageSize = input.getInt("pageSize", 10);
            var pageIndex = input.getInt("pageIndex", 1);
            var filter = new List<Filter>();
            if (search != null)
            {
                foreach (var item in search)
                {
                    if (item.Key == "folder_name")
                    {
                        var folderIds = await GetFolderIds(item.Value.ToString());
                        filter.Add(new Filter()
                        {
                            Field = "folder_Id",
                            Method = "in",
                            Mode = "in",
                            Type = "text",
                            DisplayType = "文本",
                            Values = folderIds.Select(id => id.ToString()).ToArray()
                        });
                    }
                    else
                    {
                        filter.Add(new Filter()
                        {
                            Field = item.Key,
                            Method = "eq",
                            Mode = "eq",
                            Type = "text",
                            DisplayType = "文本",
                            Values = new List<string>() { item.Value.ToString() }.ToArray()
                        });
                    }
                }
            }

            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "Owner",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { CurrentUser.id }
            });

            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "invoice_status",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { "1" }
            });

            var result = await objectQuery.GetList<Dictionary<string, object>>(Piao_Jia_Document_Identity, filter, new List<Sorter>(), pageIndex, pageSize);

            if (result.totalCount == 0)
            {
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "没有找到相关发票信息"
                };
            }
            var _folderIds = result.data.Select(d => d["folder_Id"].ToString()).Distinct().ToList();
            var folders = await GetFolders(_folderIds);

            var documentIds = result.data.Where(w => !string.IsNullOrEmpty(w.getString("silhouette")))
                .Select(s => s.getString("silhouette")).ToList();
            var documentViewUrls = await GetDocumentViewUrls(documentIds);

            foreach (var item in result.data)
            {
                var folder = folders.FirstOrDefault(f => f["MstId"].ToString() == item["folder_Id"].ToString());
                if (folder != null)
                {
                    item["folder_name"] = folder["Name"];
                }
                item["type"] = Piao_Jia_Document_Identity;
                if (!string.IsNullOrEmpty(item.getString("silhouette")))
                {
                    item["download_url"] = GetDocumentDownloadUrl(item.getString("silhouette"));
                }
                else {
                    item["download_url"] = "";
                }
                if (item.ContainsKey("items"))
                {
                    item.Remove("items");
                }
                if (item.ContainsKey("items_trip"))
                {
                    item.Remove("items_trip");
                }
                item["view_url"] = documentViewUrls.Any(w => w.DocumentId == item.getString("silhouette")) ?
                    documentViewUrls.First(w => w.DocumentId == item.getString("silhouette")).Url : "";
            }
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = result.data,
                TotalCount = result.totalCount
            };
        }

        /// <summary>
        /// 获取税务局发票信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<ResultDataDto> GetTaxInvoices(Dictionary<string, object> input)
        {
            var search = input.getJObject("search");
            var pageSize = input.getInt("pageSize", 10);
            var pageIndex = input.getInt("pageIndex", 1);
            var filter = new List<Filter>();
            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "invoice_status",
                Method = "in",
                Mode = "in",
                Type = "Text",
                Values = new string[] { "1","0" }
            });
            foreach (var item in search)
            {
                filter.Add(new Filter()
                {
                    Field = item.Key,
                    Method = "eq",
                    Mode = "eq",
                    Type = "text",
                    DisplayType = "文本",
                    Values = new List<string>() { item.Value.ToString() }.ToArray()
                });
            }
            List<List<Filter>> filters = new List<List<Filter>>();
            filters.Add(filter);
            if (filter.Where(w => w.Field == "number").Any())
            {
                var filter2 = JsonConvert.DeserializeObject<List<Filter>>(JsonConvert.SerializeObject(filter));
                var item = filter2.Where(w => w.Field == "number").FirstOrDefault();
                item.Field = "electronic_number";
                filters.Add(filter2);
            }
            var result = await objectQuery.GetUnionList<Dictionary<string, object>>(Tax_Invoice_Document_Identity, filters, new List<Sorter>(), pageIndex, pageSize);
            if (result.totalCount == 0)
            {
                return new ResultDataDto()
                {
                    IsSuccess = false,
                    Message = "没有找到相关发票信息"
                };
            }
            var documentIds = result.data.Where(w=> !string.IsNullOrEmpty(w.getString("documentView")))
                .Select(s => s.getString("documentView")).ToList();
            var documentViewUrls = await GetDocumentViewUrls(documentIds);
            foreach (var item in result.data)
            {
                item["type"] = Tax_Invoice_Document_Identity;
                if (!string.IsNullOrEmpty(item.getString("documentView")))
                {
                    item["download_url"] = GetDocumentDownloadUrl(item.getString("documentView"));
                }
                else
                {
                    item["download_url"] = "";
                }
                item["view_url"] = documentViewUrls.Any(w => w.DocumentId == item.getString("documentView")) ?
                    documentViewUrls.First(w => w.DocumentId == item.getString("documentView")).Url : "";
                if (item.ContainsKey("items"))
                {
                    item.Remove("items");
                }
                if (item.ContainsKey("attachment"))
                {
                    item.Remove("attachment");
                }
            }
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = result.data,
                TotalCount = result.totalCount
            };
        }

        /// <summary>
        /// 获取票夹ID列表
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private async Task<List<Guid>> GetFolderIds(string name)
        {
            string document_identity = "Wallets";
            var filters = new List<Filter>
            {
                new Filter
                {
                    Field = "Name",
                    Method = "like",
                    Mode = "like",
                    Type = "text",
                    DisplayType = "文本",
                    Values = new[] { name }
                },
                new Filter
                {
                    Field = "Owner",
                    Method = "eq",
                    Mode = "eq",
                    Type = "text",
                    DisplayType = "文本",
                    Values = new[] { CurrentUser.id }
                }
            };
            var result = await objectQuery.GetList<Dictionary<string, object>>(document_identity, filters, new List<Sorter>(), 0, 100);
            if (result.totalCount == 0)
            {
                return new List<Guid>() { Guid.NewGuid() };
            }
            return result.data.Select(d => Guid.Parse(d["MstId"].ToString())).ToList();
        }

        /// <summary>
        /// 获取票夹信息
        /// </summary>
        /// <param name="folderIds"></param>
        /// <returns></returns>
        private async Task<List<Dictionary<string, object>>> GetFolders(List<string> folderIds)
        {
            return (await objectQuery.GetList<Dictionary<string, object>>("Wallets",
                folderIds.Select(s => new Guid(s)).ToList(),
                new List<Filter>(),
                new List<Sorter>(), 0, 100)).data;
        }

        private string GetDocumentDownloadUrl(string documentId)
        {
            string jwtString = configuration["Authentication:JwtBearer:SecurityKey"].ToString();
            var _accessCode = MsgCrypto.ComputeSha256Hash(jwtString);
            var host= string.IsNullOrEmpty(configuration["Domain"]) ? configuration["IdentityUrl"] : configuration["Domain"];
            return $"{host}storage/api/ftp/inner/download/file?documentId={documentId}&accessCode={_accessCode}";
        }
        /// <summary>
        /// 批量获取文档预览地址
        /// </summary>
        /// <param name="documentIds"></param>
        /// <returns></returns>
        private async Task<List<DocumentFileDto>> GetDocumentViewUrls(List<string> documentIds)
        {
            if (documentIds==null || documentIds.Count == 0)
                return new List<DocumentFileDto>();
            var result = await docparkFileService.GetDocuments(documentIds);
            return result;
        }

        #endregion


        #region 更新发票信息
        /// <summary>
        /// 更新发票状态
        /// </summary>
        /// <param name="documentIdentity"></param>
        /// <param name="documentNo"></param>
        /// <param name="MstIds"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        private async Task<bool> UpdateInvoice(string documentIdentity, List<Dictionary<string, object>> datas)
        {
            var document_type_Id = await objectQuery.GetObjectId(documentIdentity);
            return await objectQuery.BulkCreateOrUpdate(document_type_Id, datas);
        }

        #endregion
    }
}
