﻿using Docpark.MultipleComparisons.Interface.Models.ChinaSoftware;
using Docpark.MultipleComparisons.Interface.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Controllers
{

    [Route("api/[controller]/[action]")]
    [ApiController]
    public class JyptController : ControllerBase
    {
        private readonly ILogger<JyptController> _logger;
        
        private readonly IChinaSoftService _service;
        public JyptController(ILogger<JyptController> logger,
            IChinaSoftService chinaSoftService)
        {
            this._logger=logger;
            this._service = chinaSoftService;
        }

        [HttpPost]
        public async Task<object> Synchronous(SyncInputDto dto)
        {
            return await this._service.Synchronous(dto);
        }
    }
}
