using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Newtonsoft.Json.Linq;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 数据映射服务接口
    /// </summary>
    public interface IDataMappingService
    {
        /// <summary>
        /// 映射响应数据
        /// </summary>
        /// <param name="responseData">原始响应数据</param>
        /// <param name="mappingConfig">映射配置</param>
        /// <returns>映射后的数据</returns>
        Task<object> MapResponseDataAsync(object responseData, DataMappingConfig mappingConfig);

        /// <summary>
        /// 验证映射配置
        /// </summary>
        /// <param name="mappingConfig">映射配置</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateMappingConfigAsync(DataMappingConfig mappingConfig);

        /// <summary>
        /// 应用映射规则
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="rules">映射规则列表</param>
        /// <returns>映射后的数据</returns>
        Task<Dictionary<string, object>> ApplyMappingRulesAsync(JToken sourceData, List<MappingRule> rules);

        /// <summary>
        /// 转换数据类型
        /// </summary>
        /// <param name="value">原始值</param>
        /// <param name="transformType">转换类型</param>
        /// <param name="parameter">转换参数</param>
        /// <returns>转换后的值</returns>
        Task<object> TransformDataAsync(object value, DataTransformType transformType, string parameter = null);

        /// <summary>
        /// 提取嵌套数据
        /// </summary>
        /// <param name="data">源数据</param>
        /// <param name="path">JSON路径</param>
        /// <returns>提取的数据</returns>
        Task<JToken> ExtractDataByPathAsync(JToken data, string path);

        /// <summary>
        /// 生成映射预览
        /// </summary>
        /// <param name="sampleData">示例数据</param>
        /// <param name="mappingConfig">映射配置</param>
        /// <returns>映射预览结果</returns>
        Task<MappingPreviewResult> GenerateMappingPreviewAsync(object sampleData, DataMappingConfig mappingConfig);
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告消息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 映射预览结果
    /// </summary>
    public class MappingPreviewResult
    {
        /// <summary>
        /// 原始数据
        /// </summary>
        public object OriginalData { get; set; }

        /// <summary>
        /// 映射后的数据
        /// </summary>
        public object MappedData { get; set; }

        /// <summary>
        /// 映射统计信息
        /// </summary>
        public MappingStatistics Statistics { get; set; }

        /// <summary>
        /// 映射过程中的错误
        /// </summary>
        public List<MappingError> Errors { get; set; } = new List<MappingError>();
    }

    /// <summary>
    /// 映射统计信息
    /// </summary>
    public class MappingStatistics
    {
        /// <summary>
        /// 总字段数
        /// </summary>
        public int TotalFields { get; set; }

        /// <summary>
        /// 成功映射的字段数
        /// </summary>
        public int MappedFields { get; set; }

        /// <summary>
        /// 失败的字段数
        /// </summary>
        public int FailedFields { get; set; }

        /// <summary>
        /// 跳过的字段数
        /// </summary>
        public int SkippedFields { get; set; }

        /// <summary>
        /// 使用默认值的字段数
        /// </summary>
        public int DefaultValueFields { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }
    }

    /// <summary>
    /// 映射错误
    /// </summary>
    public class MappingError
    {
        /// <summary>
        /// 字段路径
        /// </summary>
        public string FieldPath { get; set; }

        /// <summary>
        /// 错误类型
        /// </summary>
        public MappingErrorType ErrorType { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 原始值
        /// </summary>
        public object OriginalValue { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public string ExceptionDetails { get; set; }
    }

    /// <summary>
    /// 映射错误类型
    /// </summary>
    public enum MappingErrorType
    {
        /// <summary>
        /// 字段不存在
        /// </summary>
        FieldNotFound = 0,

        /// <summary>
        /// 类型转换失败
        /// </summary>
        TypeConversionFailed = 1,

        /// <summary>
        /// 验证失败
        /// </summary>
        ValidationFailed = 2,

        /// <summary>
        /// 路径无效
        /// </summary>
        InvalidPath = 3,

        /// <summary>
        /// 转换参数无效
        /// </summary>
        InvalidTransformParameter = 4,

        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown = 99
    }
}
