﻿using Docpark.ThirdMaster.Services.Utils;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Workflow.Share;
using Elasticsearch.Net;
using EventBus.Abstractions;
using ICSharpCode.SharpZipLib.Zip;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.codec;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Protocols;
using MongoDB.Bson.Serialization.Serializers;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Polly.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Xml;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("ExportInvoice", "发票导出")]
    public class ExportInvoice : IEventService
    {

        private readonly ILogger<ExportInvoice> logger;
        private readonly IObjectQuery objectQuery;
        private readonly IEventBus eventBus;
        private readonly IConfiguration configuration;
        private readonly IStorageService storageService;

        private readonly string[] zipTypes = new string[] { ".zip", ".gzip", ".bzip2", ".tar", ".rar", ".lzip", ".xz" };
        /// <summary>
        /// 图片类型
        /// </summary>
        private readonly string[] imgTypes = new string[] { ".jpeg", ".jpg", ".bpm", ".png", ".gif" };
        /// <summary>
        /// ofd类型
        /// </summary>
        private readonly string ofdType = ".ofd";
        private readonly string pdfType = ".pdf";


        public ExportInvoice(ILogger<ExportInvoice> logger, IObjectQuery objectQuery,
            IEventBus eventBus, IConfiguration configuration, IStorageService storageService)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.eventBus = eventBus;
            this.configuration = configuration;
            this.storageService = storageService;
        }
        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            string configXml = "ExportInvoice.xml";

            string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');

            string stepname = "";
            string businessTypeId = "";
            string OutFlord = "";
            string templatepath = "";
            string fname = "";

            for (int i = 0; i < flownames.Length; i++)
            {
                fname = flownames[i];
                logger.LogInformation("开始遍历流程名称:" + fname);

                stepname = EncryptionHelper.GetDataXMLByName(configXml, fname + "/Stepname");
                //businessTypeId = EncryptionHelper.GetDataXMLByName(configXml, fname + "/BusinessTypeId");
                OutFlord = EncryptionHelper.GetDataXMLByName(configXml, "" + fname + "/OutFlord");


                logger.LogInformation("查询:" + fname + " 步骤：" + stepname);

                var invoice = await objectQuery.Get<InvoiceInfo>(new Guid(mqContent.MstId));

                string filename = invoice.code + "_" + invoice.number;

                if (!string.IsNullOrEmpty(filename))
                {
                    var documentInfo = await storageService.GetDocumentInfo(mqContent.DocumentID);
                    var ext = Path.GetExtension(documentInfo.FileName)?.ToLower();

                    var fileName = documentInfo?.FileName;

                    bool success = true;
                    string message = "";

                    var bytes = Array.Empty<byte>();
                    if (imgTypes.Contains(ext))
                    {
                        bytes = await storageService.GetDocument(mqContent.DocumentID);
                        if (!(CreatePDF(bytes, OutFlord, filename) == "true"))
                        {
                            success = false;
                            message = "PDF生成失败";
                        }
                    }
                    else if (ofdType == ext)
                    {
                        //获取第一张
                        bytes = await storageService.DownloadOfdFirst(mqContent.DocumentID);
                        fileName += ".jpg";
                        if (!(CreatePDF(bytes, OutFlord, filename) == "true"))
                        {
                            success = false;
                            message = "PDF生成失败";
                        }
                    }
                    else if (pdfType == ext)
                    {
                        bytes = await storageService.GetDocument(mqContent.DocumentID);
                        ConvertToPDF(bytes, OutFlord, filename);
                    }
                    else if (zipTypes.Contains(ext))
                    {
                        List<byte[]> imageBytes = new List<byte[]>();
                        for (int p = 0; p < documentInfo.PageCount.Value; p++)
                        {
                            imageBytes.Add(await storageService.DownloadZip(mqContent.DocumentID, p + 1));//获取压缩文件的第一张
                        }

                        if (!(CreatePDFFromImages(imageBytes, OutFlord, filename) == "true"))
                        {
                            success = false;
                            message = "PDF生成失败";
                        }
                    }

                    if (success)
                    {
                        var pdfFile = OutFlord + "/" + filename + ".pdf";
                        if (File.Exists(pdfFile))
                        {
                            File.Copy(pdfFile, OutFlord + "/temptif/" + filename + ".pdf", true);
                        }
                        //System.Threading.Thread.Sleep(3000);
                        templatepath = EncryptionHelper.GetDataXMLByName(configXml, "" + fname + "/templatepath_" + invoice.type);
                        var result = await CreateXml(invoice, OutFlord, templatepath);

                        mqContent.TriggerStatus = success;
                        mqContent.TriggerMessage = message;
                        var msg = JsonConvert.SerializeObject(mqContent);
                        DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
                        eventBus.Publish(publishEventData);

                    }
                    else
                    {
                        mqContent.TriggerStatus = success;
                        mqContent.TriggerMessage = message;
                        var msg = JsonConvert.SerializeObject(mqContent);
                        DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
                        eventBus.Publish(publishEventData);
                    }
                }
                else
                {
                    continue;
                }
            }

            return "";
        }

        public string CreatePDF(byte[] Pic, string OutFlord, string filename)
        {
            try
            {
                GetImageByBytes(Pic, OutFlord, filename);
                List<string> tiffiles = new List<string>();
                tiffiles = new List<string>();
                tiffiles.Add(OutFlord + "/temptif/" + filename + ".jpg");
                TifToPDF(tiffiles, OutFlord + "/" + filename + ".pdf");
                return "true";
            }
            catch (Exception ex)
            {
                logger.LogError("生成PDF错误:" + filename + "    " + ex.Message);
                logger.LogError(ex.StackTrace);
                return "";
            }

        }

        public string CreatePDFFromImages(List<byte[]> imageBytes, string OutFlord, string filename)
        {
            try
            {
                List<string> tiffiles = new List<string>();
                for (int i = 0; i < imageBytes.Count; i++)
                {
                    string newFileName = filename + "_" + i.ToString();
                    GetImageByBytes(imageBytes[i], OutFlord, newFileName);
                    tiffiles.Add(OutFlord + "/temptif/" + newFileName + ".jpg");
                }
                TifToPDF(tiffiles, OutFlord + "/" + filename + ".pdf");
                return "true";
            }
            catch (Exception ex)
            {
                logger.LogError("生成PDF错误:" + filename + "    " + ex.Message);
                logger.LogError(ex.StackTrace);
                return "";
            }
        }
        public void GetImageByBytes(byte[] bytes, string OutFlord, string filename)
        {
            if (!Directory.Exists(OutFlord + "/temptif/"))
            {
                Directory.CreateDirectory(OutFlord + "/temptif/");
            }
            if (File.Exists(OutFlord + "/temptif/" + filename + ".jpg"))
            {
                File.Delete(OutFlord + "/temptif/" + filename + ".jpg");
            }
            File.WriteAllBytes(OutFlord + "/temptif/" + filename + ".jpg", bytes);
        }

        public void ConvertToPDF(byte[] bytes, string OutFlord, string filename)
        {
            if (!Directory.Exists(OutFlord))
            {
                Directory.CreateDirectory(OutFlord);
            }
            File.WriteAllBytes(OutFlord + "/" + filename + ".pdf", bytes);
        }

        public static void TifToPDF(List<string> images, string sFilePdf)
        {
            try
            {
                if (File.Exists(sFilePdf))
                {
                    File.Delete(sFilePdf);
                }

                FileInfo _toFile = new FileInfo(sFilePdf);
                // 创建一个文档对象
                iTextSharp.text.Document doc = new iTextSharp.text.Document();

                //iTextSharp.text.Document doc = new iTextSharp.text.Document();
                //iTextSharp.text.Document doc = null;
                int pages = 0;
                FileStream fs = new FileStream(sFilePdf, FileMode.OpenOrCreate);
                // 定义输出位置并把文档对象装入输出对象中
                PdfWriter writer = PdfWriter.GetInstance(doc, fs);
                // 打开文档对象
                doc.Open();
                foreach (string sFileTif in images)
                {
                    PdfContentByte cb = writer.DirectContent;
                    RandomAccessFileOrArray ra = new RandomAccessFileOrArray(sFileTif);


                    iTextSharp.text.Image img = iTextSharp.text.Image.GetInstance(sFileTif);
                    if (img != null)
                    {
                        {
                            doc.SetPageSize(new iTextSharp.text.Rectangle(img.Width, img.Height));
                            doc.NewPage();

                            img.SetAbsolutePosition(0, 0);
                            cb.AddImage(img);
                            ++pages;
                        }
                    }

                    ra.Close();// 关闭
                }
                doc.Close();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private string GetTempleteString(string templatepath)
        {
            StreamReader sr = new StreamReader(templatepath, System.Text.Encoding.Default);
            string tempstring = sr.ReadToEnd();
            sr.Close();
            return tempstring;
        }

        public async Task<string> CreateXml(InvoiceInfo invoice, string OutFlord, string templatepath)
        {
            string TempPath = Environment.CurrentDirectory + "/" + templatepath;
            string TempString = GetTempleteString(templatepath);
            XmlDocument xdoc = null;
            string WriteString = "";
            WriteString = TempString;
            xdoc = new XmlDocument();
            string filename = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            filename = invoice.code + "_" + invoice.number;
            string PurchaseOrder = "";
            string LIT_OrderNumber = "";
            List<Dictionary<string, string>> SPAitem = new List<Dictionary<string, string>>();

            if (invoice.PO != null && invoice.PO.Split(';').Length > 1)
            {
                PurchaseOrder = "";
                string[] PurchaseOrderList = invoice.PO.Split(';');
                for (int i = 0; i < PurchaseOrderList.Length; i++)
                {
                    LIT_OrderNumber += "<LIT_OrderNumber" + (i + 1) + ">" + PurchaseOrderList[i] + "</LIT_OrderNumber" + (i + 1) + ">";
                }
            }
            else
            {
                LIT_OrderNumber = "";
                PurchaseOrder = invoice.PO == null ? "" : invoice.PO;
            }
            var result = await objectQuery
            .GetList<CompanyInfo>(configuration["DocumentType:Company"], new List<Filter>() {
                    new Filter(){
                     Field="TaxNo",
                      Method="eq",
                       Mode="eq",
                        Values=new string[]{ invoice.buyer_tax_id }
                    }
            }, new List<Sorter>(), 0, 10);
            CompanyInfo companyInfo = new CompanyInfo();
            if (result.totalCount > 0)
            {
                companyInfo = result.data.First();

            }

            var properties = invoice.GetType().GetProperties();

            foreach (var p in properties)
            {
                string key = p.Name;
                if (key == "date")
                {
                    WriteString = WriteString.Replace("{" + key + "}", DateTime.Parse(p.GetValue(invoice).ToString()).ToString("yyyyMMdd"));
                }
                else if (key == "pretax_amount")
                {
                    WriteString = WriteString.Replace("{" + key + "}",
                        Math.Round(
                            decimal.Parse(invoice.pretax_amount)
                            , 2).ToString());
                }
                else if (key == "tax")
                {
                    WriteString = WriteString.Replace("{" + key + "}",
                        Math.Round(
                            decimal.Parse(invoice.tax)
                            , 2).ToString());
                }
                else if (key == "total")
                {
                    WriteString = WriteString.Replace("{" + key + "}",
                        Math.Round(
                            decimal.Parse(invoice.total)
                            , 2).ToString());
                }
                else if (key == "number")
                {
                    if (invoice.number.Length > 16)
                    {
                        WriteString = WriteString.Replace("{" + key + "}",
                        invoice.number.Substring(invoice.number.Length - 16, 16));
                    }
                    else
                    {
                        WriteString = WriteString.Replace("{" + key + "}",
                        invoice.number);
                    }
                }
                else if (key == "tax_rate")
                {
                    WriteString = WriteString.Replace("{" + key + "}",
                            invoice.tax_rate.Replace("%", ""));
                }
                else
                {
                    if (p.GetValue(invoice) != null)
                    {
                        WriteString = WriteString.Replace("{" + key + "}", p.GetValue(invoice).ToString());
                    }
                    else
                    {
                        WriteString = WriteString.Replace("{" + key + "}", "");
                    }
                }
            }
            WriteString = WriteString.Replace("{" + "TaskCreatedData" + "}", invoice.CreationTime.ToString("yyyyMMdd"));
            WriteString = WriteString.Replace("{" + "PurchaseOrder" + "}", PurchaseOrder);
            WriteString = WriteString.Replace("{" + "LIT_OrderNumber" + "}", LIT_OrderNumber);
            WriteString = WriteString.Replace("{" + "SAP_BUKRS" + "}", companyInfo.SAP_BUKRS + "_" + companyInfo.CompanyCode);
            WriteString = WriteString.Replace("{" + "PlantInvoice" + "}", companyInfo.PlantInvoice);
            xdoc.LoadXml(WriteString);
            try
            {

                if (filename != "")
                {
                    xdoc.Save(OutFlord + "/" + filename + ".xml");
                    if (File.Exists(OutFlord + "/temptif/" + filename + ".xml"))
                    {
                        File.Delete(OutFlord + "/temptif/" + filename + ".xml");
                    }
                    xdoc.Save(OutFlord + "/temptif/" + filename + ".xml");
                    return filename;
                }
                else
                {
                    logger.LogError("xml文件名为空");
                    return "";
                    //ShowOutPutInfo.Text += filename + "导出失败！\r\n";

                }

            }
            catch (Exception EX)
            {
                logger.LogError(EX.Message);
                return "";
                //MessageBox.Show(EX.Message);
            }
        }
    }
}
