using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 性能指标实体
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称
        /// </summary>
        [StringLength(100)]
        public string ApiName { get; set; }

        /// <summary>
        /// 指标名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string MetricName { get; set; }

        /// <summary>
        /// 指标值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 指标单位
        /// </summary>
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 指标类型
        /// </summary>
        [StringLength(50)]
        public string MetricType { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 标签
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument Labels { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument AdditionalData { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
