﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;

namespace Docpark.ThirdMaster.Services.Utils
{
    public static class FileClassHelper
    {
        public static string GetXMLFilestring(string FilePath)
        {
            string str;
            StreamReader sr = new StreamReader(FilePath, false);
            str = sr.ReadToEnd().ToString();
            sr.Close();
            return str;
        }
        public static void UpdateXMLFilestring(string FilePath, string filestring)
        {
            try
            {
                string str;
                FileStream aFile = new FileStream(FilePath, FileMode.OpenOrCreate);
                StreamWriter sw = new StreamWriter(aFile);
                //StreamWriter sw = new StreamWriter(FilePath, false);
                sw.WriteLine(filestring);
                sw.Close();//写入
                aFile.Close();
            }
            catch (Exception ex)
            {

            }
        }

        public static string[] FindAllFiles(string dir, string seachpatt)
        {
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            string[] FilesPath = Directory.GetFiles(dir, seachpatt, SearchOption.AllDirectories);
            return FilesPath;
        }

        public static void ReName(string sfile, string tfile)
        {
            File.Move(sfile, tfile);
        }


        public static void CreateImage(string imagepath, string filename, string imagestream)
        {
            try
            {
                byte[] imageBytes = Convert.FromBase64String(imagestream);
                //读入MemoryStream对象
                MemoryStream memoryStream = new MemoryStream(imageBytes, 0, imageBytes.Length);
                memoryStream.Write(imageBytes, 0, imageBytes.Length);
                //转成图片
                Image image = Image.FromStream(memoryStream);
                image.Save(imagepath + "//" + filename);
                memoryStream.Close();
                //return image;
            }
            catch (Exception)
            {

                //Image image = null;
                //return image;
            }
            finally
            {

            }
        }


        public static void CreateImageastif(string imagepath, string filename, byte[] imageBytes)
        {
            try
            {
                // byte[] imageBytes = Convert.FromBase64String(imagestream);
                //读入MemoryStream对象
                MemoryStream memoryStream = new MemoryStream(imageBytes, 0, imageBytes.Length);
                memoryStream.Write(imageBytes, 0, imageBytes.Length);
                //转成图片
                Image image = Image.FromStream(memoryStream);
                image.Save(imagepath + "//" + filename, System.Drawing.Imaging.ImageFormat.Tiff);
                memoryStream.Close();
                //return image;
            }
            catch (Exception)
            {

                //Image image = null;
                //return image;
            }
            finally
            {

            }
        }



        /// <summary>
        /// 把json字符串转成对象
        /// </summary>
        /// <typeparam name="T">对象</typeparam>
        /// <param name="data">json字符串</param>
        public static Dictionary<string, string> Deserialize(string data)
        {
            try
            {
                return JsonConvert.DeserializeObject<Dictionary<string, string>>(data);
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public static string Serialize(Dictionary<string, Object> Obj)
        {
            return JsonConvert.SerializeObject(Obj);
        }

        /// <summary>
        /// 根据图片路径返回图片的字节流byte[]
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <returns>返回的字节流</returns>
        public static byte[] getImageByte(string imagePath)
        {
            FileStream files = new FileStream(imagePath, FileMode.Open);
            byte[] imgByte = new byte[files.Length];
            files.Read(imgByte, 0, imgByte.Length);
            files.Close();
            return imgByte;
        }

    }
}
