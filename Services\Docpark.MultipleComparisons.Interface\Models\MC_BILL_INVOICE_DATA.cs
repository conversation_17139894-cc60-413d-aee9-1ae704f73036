﻿using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 单据发票凭证详情数据
    /// </summary>
    [JsonTable("MC_BILL_INVOICE_DATA")]
    public class MC_BILL_INVOICE_DATA
    {
        /// <summary>
        /// 单据id
        /// </summary>
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string bill_no { get; set; }
        /// <summary>
        /// 单据mstid
        /// </summary>
        public Guid bill_mstid { get; set; }
        /// <summary>
        /// Mstid
        /// </summary>
        public Guid MstId { get; set; }
        /// <summary>
        /// 发票明细id
        /// </summary>
        public string third_invoice_guid { get; set; }
        /// <summary>
        /// 第三方发票类型
        /// </summary>
        public string third_type { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string number { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string date { get; set; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string check_code { get; set; }
        /// <summary>
        /// 税前金额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double pretax_amount { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double tax { get; set; }
        /// <summary>
        /// 发票行明细
        /// </summary>
        public List<MC_Invoice_Item> items { get; set; }
        /// <summary>
        /// 发票差旅详情
        /// </summary>
        public List<MC_Travel_Detail> travel_details { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double total { get; set; }
        /// <summary>
        /// 购买方名称
        /// </summary>
        public string buyer { get; set; }
        /// <summary>
        /// 购买方纳税人识别号
        /// </summary>
        public string buyer_tax_id { get; set; }
        /// <summary>
        /// 销售方名称
        /// </summary>
        public string seller { get; set; }
        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        public string seller_tax_id { get; set; }
        /// <summary>
        /// 是否必须匹配, 00 无需匹配, 01 需要匹配(默认)
        /// </summary>
        public string is_required_match { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 验真结果
        /// </summary>
        public string verifiy_result { get; set; }
        /// <summary>
        /// 附件数据集合
        /// </summary>
        public List<Annex_items> annex_items { get; set; }
        /// <summary>
        /// 发票影像标识
        /// </summary>
        /// <remarks>
        /// 发票报销时从票池中选择的发票标识
        /// </remarks>
        public string image_mstid { get; set; }
    }

    /// <summary>
    /// 发票行明细
    /// </summary>
    public class MC_Invoice_Item
    {
        /// <summary>
        /// 税率
        /// </summary>
        public string tax_rate { get; set; }
    }

    /// <summary>
    /// 发票差旅详情
    /// </summary>
    public class MC_Travel_Detail
    {
        /// <summary>
        /// 乘车人姓名<br/>
        /// 注: 二次比对规则: 飞机票比对票小秘扫描的user_name, 火车票比对票小秘扫描的name
        /// </summary>
        public string passenger_name { get; set; }
        /// <summary>
        /// 出发时间(格式: yyyy-MM-dd HH:mm:dd)
        /// </summary>
        public string departure_time { get; set; }
    }
}
