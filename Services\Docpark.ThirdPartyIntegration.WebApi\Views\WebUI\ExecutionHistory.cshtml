@{
    Layout = "_Layout";
}

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 筛选器 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">筛选条件</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="jobKeyFilter" class="form-label">任务键</label>
                        <select class="form-select" id="jobKeyFilter">
                            <option value="">全部任务</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">执行状态</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="Success">成功</option>
                            <option value="Failed">失败</option>
                            <option value="Running">运行中</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="startDateFilter" class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="startDateFilter">
                    </div>
                    <div class="col-md-3">
                        <label for="endDateFilter" class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="endDateFilter">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button class="btn btn-primary" onclick="loadExecutionHistory()">
                            <i class="fas fa-search me-2"></i>查询
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                            <i class="fas fa-undo me-2"></i>重置
                        </button>
                        <button class="btn btn-success ms-2" onclick="exportHistory()">
                            <i class="fas fa-download me-2"></i>导出
                        </button>
                        <button class="btn btn-danger ms-2" onclick="cleanupHistory()">
                            <i class="fas fa-trash me-2"></i>清理历史
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行历史列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">执行历史记录</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="executionHistoryTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>任务键</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>执行状态</th>
                                <th>耗时(秒)</th>
                                <th>错误信息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="executionHistoryTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span class="ms-2">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="执行历史分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 执行详情模态框 -->
<div class="modal fade" id="executionDetailModal" tabindex="-1" aria-labelledby="executionDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="executionDetailModalLabel">执行详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="executionDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    let currentPage = 1;
    let pageSize = 20;
    let totalPages = 1;

    $(document).ready(function() {
        loadJobKeys();
        loadExecutionHistory();
    });

    function loadJobKeys() {
        callAPI('/api/Scheduler/jobs')
            .done(function(data) {
                var select = $('#jobKeyFilter');
                select.empty().append('<option value="">全部任务</option>');
                
                if (data && data.length > 0) {
                    data.forEach(function(job) {
                        select.append('<option value="' + job.key + '">' + job.key + '</option>');
                    });
                }
            })
            .fail(function() {
                console.error('加载任务列表失败');
            });
    }

    function loadExecutionHistory(page = 1) {
        currentPage = page;
        
        var params = {
            page: currentPage,
            pageSize: pageSize,
            jobKey: $('#jobKeyFilter').val(),
            status: $('#statusFilter').val(),
            startDate: $('#startDateFilter').val(),
            endDate: $('#endDateFilter').val()
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        var queryString = $.param(params);
        var url = '/api/Scheduler/execution-history' + (queryString ? '?' + queryString : '');

        callAPI(url)
            .done(function(data) {
                displayExecutionHistory(data.items || data);
                updatePagination(data.totalPages || 1, data.currentPage || 1);
            })
            .fail(function() {
                $('#executionHistoryTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>');
            });
    }

    function displayExecutionHistory(histories) {
        var tbody = $('#executionHistoryTableBody');
        tbody.empty();
        
        if (!histories || histories.length === 0) {
            tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无执行历史记录</td></tr>');
            return;
        }

        histories.forEach(function(history) {
            var statusBadge = getStatusBadge(history.status);
            var duration = history.endTime && history.startTime ? 
                ((new Date(history.endTime) - new Date(history.startTime)) / 1000).toFixed(2) : '-';
            
            var row = `
                <tr>
                    <td>${history.jobKey || '-'}</td>
                    <td>${formatDateTime(history.startTime)}</td>
                    <td>${formatDateTime(history.endTime)}</td>
                    <td>${statusBadge}</td>
                    <td>${duration}</td>
                    <td class="text-truncate" style="max-width: 200px;" title="${history.errorMessage || ''}">${history.errorMessage || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="showExecutionDetail('${history.id}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getStatusBadge(status) {
        switch (status) {
            case 'Success':
                return '<span class="badge bg-success">成功</span>';
            case 'Failed':
                return '<span class="badge bg-danger">失败</span>';
            case 'Running':
                return '<span class="badge bg-primary">运行中</span>';
            default:
                return '<span class="badge bg-secondary">未知</span>';
        }
    }

    function formatDateTime(dateTime) {
        if (!dateTime) return '-';
        return new Date(dateTime).toLocaleString('zh-CN');
    }

    function updatePagination(total, current) {
        totalPages = total;
        currentPage = current;
        
        var pagination = $('#pagination');
        pagination.empty();

        if (totalPages <= 1) return;

        // 上一页
        var prevDisabled = currentPage === 1 ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="loadExecutionHistory(${currentPage - 1})">上一页</a>
            </li>
        `);

        // 页码
        var startPage = Math.max(1, currentPage - 2);
        var endPage = Math.min(totalPages, currentPage + 2);

        for (var i = startPage; i <= endPage; i++) {
            var active = i === currentPage ? 'active' : '';
            pagination.append(`
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="loadExecutionHistory(${i})">${i}</a>
                </li>
            `);
        }

        // 下一页
        var nextDisabled = currentPage === totalPages ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="loadExecutionHistory(${currentPage + 1})">下一页</a>
            </li>
        `);
    }

    function showExecutionDetail(executionId) {
        callAPI('/api/Scheduler/execution-detail/' + executionId)
            .done(function(data) {
                var content = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>任务键:</strong> ${data.jobKey || '-'}
                        </div>
                        <div class="col-md-6">
                            <strong>执行状态:</strong> ${getStatusBadge(data.status)}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>开始时间:</strong> ${formatDateTime(data.startTime)}
                        </div>
                        <div class="col-md-6">
                            <strong>结束时间:</strong> ${formatDateTime(data.endTime)}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <strong>错误信息:</strong>
                            <pre class="mt-2 p-2 bg-light border rounded">${data.errorMessage || '无'}</pre>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <strong>执行日志:</strong>
                            <pre class="mt-2 p-2 bg-light border rounded" style="max-height: 300px; overflow-y: auto;">${data.logs || '无日志记录'}</pre>
                        </div>
                    </div>
                `;
                $('#executionDetailContent').html(content);
                $('#executionDetailModal').modal('show');
            })
            .fail(function() {
                showError('加载执行详情失败');
            });
    }

    function resetFilters() {
        $('#jobKeyFilter').val('');
        $('#statusFilter').val('');
        $('#startDateFilter').val('');
        $('#endDateFilter').val('');
        loadExecutionHistory();
    }

    function exportHistory() {
        var params = {
            jobKey: $('#jobKeyFilter').val(),
            status: $('#statusFilter').val(),
            startDate: $('#startDateFilter').val(),
            endDate: $('#endDateFilter').val(),
            export: true
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        var queryString = $.param(params);
        var url = '/api/Scheduler/execution-history/export' + (queryString ? '?' + queryString : '');
        
        // 创建下载链接
        var link = document.createElement('a');
        link.href = url;
        link.download = 'execution_history_' + new Date().toISOString().slice(0, 10) + '.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function cleanupHistory() {
        var days = prompt('请输入要保留的天数（将删除更早的记录）:', '30');
        if (days === null || days === '') return;
        
        days = parseInt(days);
        if (isNaN(days) || days < 1) {
            showError('请输入有效的天数');
            return;
        }

        if (!confirm(`确定要删除 ${days} 天前的执行历史记录吗？此操作不可恢复。`)) {
            return;
        }

        callAPI('/api/Scheduler/cleanup-history?olderThanDays=' + days, 'DELETE')
            .done(function(result) {
                showSuccess(`清理完成，删除了 ${result.deletedCount} 条记录`);
                loadExecutionHistory();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '清理失败';
                showError('清理失败: ' + message);
            });
    }
</script>
}
