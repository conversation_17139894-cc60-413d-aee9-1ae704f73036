﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Commons;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("MatchCompany", "匹配公司信息")]
    public class MatchCompanyService : IEventService
    {

        private readonly ILogger<MatchCompanyService> logger;
        private readonly IObjectQuery objectQuery;
        private readonly IEventBus eventBus;
        private readonly IConfiguration configuration;
        private readonly IDGrpc grpc;

        public MatchCompanyService(ILogger<MatchCompanyService> logger, IObjectQuery objectQuery, IEventBus eventBus, IConfiguration configuration, IDGrpc grpc)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.eventBus = eventBus;
            this.configuration = configuration;
            this.grpc = grpc;
        }
        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            var resMessage = new StringBuilder();
            var businessType = await grpc.GetStringAsync("App.CustomerClaim.BusinessType");//客户索赔的文档类型
            try
            {
                var invoice = await objectQuery.Get<InvoiceInfo>(new Guid(mqContent.MstId));
                var filters = new List<Filter>()
                {
                    new Filter() { Field = "TaxNo", Mode = "eq", Values = new string[] { invoice.buyer_tax_id } }
                };
                var isCustomerClaim = string.Compare(invoice.businesstypeid, CommonUtil.ConvertObjectIdToGuid(businessType).ToString(), true) == 0;
                if (isCustomerClaim)//客户索赔流程
                {
                    var filter = filters.First();
                    filter.Mode = "in";
                    filter.Values = new string[] { invoice.buyer_tax_id, invoice.seller_tax_id };
                }
                var result = await objectQuery.GetList<CompanyInfo>(configuration["DocumentType:Company"], filters, new List<Sorter>(), 0, 10);
                if (result.totalCount > 0)
                {
                    var company = result.data.First();
                    var data = new Dictionary<string, object>
                    {
                        ["CompanyCode"] = company.CompanyCode,
                        ["CompanyName"] = company.Name
                    };
                    await objectQuery.CreateOrUpdate(mqContent.DocumentType, new Guid(mqContent.MstId), data);
                    mqContent.TriggerStatus = true;

                    //如果是销售方税号匹配到大陆公司表得数据，那么认为是负数发票，然后强制提示“此发票为负数发票，请维护发票金额！”
                    if (isCustomerClaim)//客户索赔流程
                    {
                        if (await GetSellerExist(invoice.seller_tax_id))
                        {
                            mqContent.TriggerStatus = false;
                            mqContent.TriggerMessage = "此发票为负数发票，请维护发票金额！";
                        }
                    }
                }
                else
                {
                    resMessage.AppendLine($"MstId:{mqContent.MstId};TaxNo:{invoice?.buyer_tax_id};Company:{configuration["DocumentType:Company"]}");

                    mqContent.TriggerStatus = false;
                    mqContent.TriggerMessage = "未获取到公司信息";
                }
            }
            catch (Exception ex)
            {
                mqContent.TriggerStatus = false;
                mqContent.TriggerMessage = "取到公司信息异常";

                logger.LogError(ex, "取到公司信息异常");
                resMessage.AppendLine(ex.Message);
            }

            var msg = JsonConvert.SerializeObject(mqContent);
            var publishEventData = new DocumentServiceMQEventData(msg);
            eventBus.Publish(publishEventData);

            return resMessage.ToString();
        }

        private async Task<bool> GetSellerExist(string tax_id)
        {
            var filters = new List<Filter>()
            {
                new Filter() { Field = "TaxNo", Mode = "eq", Values = new string[] { tax_id } }
            };
            var result = await objectQuery.GetList<CompanyInfo>(configuration["DocumentType:Company"], filters, new List<Sorter>(), 0, 10);
            return result.totalCount > 0;
        }
    }
}
