﻿using System;
using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class SearchTaskResultDto
    {
        public int Total { get; set; }
        public List<SearchTaskCreateTimeDto> Data { get; set; } = new List<SearchTaskCreateTimeDto>();
    }
    public class SearchTaskCreateTimeDto
    {
        public string TaskID { get; set; }
        public string TaskCreatedTime { get; set; }
        public string Title { get; set; }
        public DateTime TaskModifiedTime { get; set; }
    }
}
