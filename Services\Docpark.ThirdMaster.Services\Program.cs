using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using DocPark.CustomExtension.Extensions;
using Autofac.Extensions.DependencyInjection;

namespace Docpark.ThirdMaster.Services
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).UseSerilogWithElasticsearch().Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                })
            .UseServiceProviderFactory(new AutofacServiceProviderFactory());
    }
}
