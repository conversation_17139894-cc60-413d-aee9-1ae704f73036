using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// API配置服务接口
    /// </summary>
    public interface IApiConfigurationService
    {
        /// <summary>
        /// 获取所有API配置
        /// </summary>
        /// <returns></returns>
        Task<List<ApiConfiguration>> GetAllAsync();

        /// <summary>
        /// 根据ID获取API配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<ApiConfiguration> GetByIdAsync(string id);

        /// <summary>
        /// 创建API配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<ApiConfiguration> CreateAsync(ApiConfiguration config);

        /// <summary>
        /// 更新API配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<ApiConfiguration> UpdateAsync(ApiConfiguration config);

        /// <summary>
        /// 删除API配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// 启用/禁用API配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="enabled"></param>
        /// <returns></returns>
        Task<bool> SetEnabledAsync(string id, bool enabled);

        /// <summary>
        /// 获取启用的API配置
        /// </summary>
        /// <returns></returns>
        Task<List<ApiConfiguration>> GetEnabledAsync();
    }
}
