﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    public class UserDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int id { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 姓氏
        /// </summary>
        public string surname { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string userName { get; set; }
        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string emailAddress { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string phoneNumber { get; set; }
        /// <summary>
        /// 头像ID
        /// </summary>
        public string profilePictureId { get; set; }
        /// <summary>
        /// 邮箱是否确认
        /// </summary>
        public bool isEmailConfirmed { get; set; }
        /// <summary>
        /// 角色
        /// </summary>
        public List<Dictionary<string, object>> roles { get; set; }
        /// <summary>
        /// 最后登录时间
        /// </summary>
        public string lastLoginTime { get; set; }
        /// <summary>
        /// 是否激活
        /// </summary>
        public string isActive { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string creationTime { get; set; }
    }
}
