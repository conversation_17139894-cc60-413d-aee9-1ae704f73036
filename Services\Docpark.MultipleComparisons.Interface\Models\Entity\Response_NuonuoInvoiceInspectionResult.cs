﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    public class Response_NuonuoInvoiceInspectionResult
    {
        /// <summary>
        /// 返回码<br/>
        /// 0000 查验成功<br/>
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 返回描述
        /// </summary>
        public string describe { get; set; }
        /// <summary>
        /// 返回的发票结果
        /// </summary>
        public Response_NuonuoInvoiceResult result { get; set; }
    }

    public class Response_NuonuoInvoiceResult
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }
        /// <summary>
        /// 开票日期（格式:yyyy-MM-dd）
        /// </summary>
        public string invoiceDate { get; set; }
        /// <summary>
        /// 付款方名称（购方名称）
        /// </summary>
        public string payerName { get; set; }
        /// <summary>
        /// 付款方税号（购方税号）
        /// </summary>
        public string payerTaxNo { get; set; }
        /// <summary>
        /// 付款方地址电话（购方地址电话）
        /// </summary>
        public string payerAddressTel { get; set; }
        /// <summary>
        /// 付款方银行账号（购方银行账号）
        /// </summary>
        public string payerBankAccount { get; set; }
        /// <summary>
        /// 收款方名称（销方名称）
        /// </summary>
        public string payeeName { get; set; }
        /// <summary>
        /// 收款方税号（销方税号）
        /// </summary>
        public string payeeTaxNo { get; set; }
        /// <summary>
        /// 收款方地址电话（销方地址电话） 
        /// </summary>
        public string payeeAddressTel { get; set; }
        /// <summary>
        /// 收款方银行账号（销方银行账号）
        /// </summary>
        public string payeeBankAccount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 机器编码（设备编码） 
        /// </summary>
        public string deviceCode { get; set; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string checkCode { get; set; }
        /// <summary>
        /// 发票种类（01:专用发票 03:机动车发票 04:普票发票 08:电子专用发票 10:电子发票 11:卷式发票 14:通行费发票 15:二手车发票
        /// </summary>
        public string invoiceKind { get; set; }
        /// <summary>
        /// 作废标志（0:正常 2:作废 3:红冲）
        /// </summary>
        public string invalidFlag { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string taxAmount { get; set; }
        /// <summary>
        /// 价税合计（含税金额）
        /// </summary>
        public string sumAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        public string exTaxAmount { get; set; }
        /// <summary>
        /// 版式文件地址
        /// </summary>
        public string pdfurl { get; set; }
        /// <summary>
        /// 发票明细
        /// </summary>
        public List<Response_NuonuoInvoiceItemResult> itemInfos { get; set; }
    }

    /// <summary>
    /// 发票明细
    /// </summary>
    public class Response_NuonuoInvoiceItemResult
    {
        /// <summary>
        /// 项目序号（商品序号）
        /// </summary>
        public string itemNo { get; set; }
        /// <summary>
        /// 项目名称（商品名称）
        /// </summary>
        public string itemName { get; set; }
        /// <summary>
        /// 型号 
        /// </summary>
        public string itemSpec { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string itemUtil { get; set; }
        /// <summary>
        /// 数量 
        /// </summary>
        public string itemQuantity { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public string itemExTaxPrice { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string itemTaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string itemTaxAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        public string temExTaxAmount { get; set; }
    }
}
