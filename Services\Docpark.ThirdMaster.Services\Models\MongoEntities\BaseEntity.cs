﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using Newtonsoft.Json;

namespace Docpark.ThirdMaster.Services.Models.MongoEntities
{
    [BsonIgnoreExtraElements]
    public class BaseEntity
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [JsonIgnore]
        public string Id { get; set; }
        ///// <summary>
        ///// 创建时间
        ///// </summary>
        //[BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        //public DateTime CreationTime { get; set; } = DateTime.Now;
        ///// <summary>
        ///// 是否删除 true  删除 false 未删除
        ///// </summary>
        //public bool IsDelete { get; set; } = false;
        ///// <summary>
        ///// 创建人
        ///// </summary>
        //public string CreationUser { get; set; }
        ///// <summary>
        ///// 删除人
        ///// </summary>
        //public string DeletionUser { get; set; }
        
    }
}
