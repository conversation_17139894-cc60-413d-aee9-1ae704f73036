﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.HttpClientExtions;
using Docpark.HttpClientExtension.IServices;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Profiling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class DocparkHttpClient : IDocparkHttpClient
    {

        public DocparkHttpClient(ILogger<DocparkHttpClient> logger, IDGrpc grpc,
            IConfiguration configuration, IHttpClientFactory httpClientFactory,
            IHttpContextAccessor httpContextAccesor)
        {
            _logger = logger;
            _grpc = grpc;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _httpContextAccesor = httpContextAccesor;
        }

        public ILogger<DocparkHttpClient> _logger { get; }
        public IDGrpc _grpc { get; }
        public IConfiguration _configuration { get; }
        public IHttpClientFactory _httpClientFactory { get; }
        public IHttpContextAccessor _httpContextAccesor { get; }

        public async Task<string> GetAsync(string url)
        {
            using (MiniProfiler.Current.CustomTiming("HTTP", "GET:" + url, "GET"))
            {
                _logger.LogInformation($"get请求{url};");
                var client = _httpClientFactory.CreateClient();
                client.DefaultRequestHeaders.Accept.TryParseAdd("application/json");
                client.AddAuthenticate( _grpc, _configuration, _httpContextAccesor);
                using (var response = client.GetAsync(url).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return await response.Content.ReadAsStringAsync();
                }
            }
        }

        public async Task<T> GetAsync<T>(string url)
        {
            var strResult = await this.GetAsync(url);
            return JsonConvert.DeserializeObject<T>(strResult);
        }


        public Task<string> PostAsync(string url, object content)
        {
            using (MiniProfiler.Current.CustomTiming("HTTP", "Post:" + url, "POST"))
            {
                _logger.LogInformation($"Post请求{url};请求内容:{JsonConvert.SerializeObject(content)};");

                HttpClient client = _httpClientFactory.CreateClient();
                var hContent = new StringContent(JsonConvert.SerializeObject(content));
                hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                client.AddAuthenticate(_grpc, _configuration, _httpContextAccesor);
                using (var response = client.PostAsync(url, hContent).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return response.Content.ReadAsStringAsync();
                }
            }
        }

        public async Task<T> PostAsync<T>(string url, object content)
        {
            var strResult = await this.PostAsync(url, content);
            return JsonConvert.DeserializeObject<T>(strResult);
        }

        public Task<string> PostJsonAsync(string url, string apiName, object model, string token = "")
        {
            using (MiniProfiler.Current.CustomTiming("HTTP", "Post:" + url, "Post"))
            {
                _logger.LogInformation($"PostJson请求{url};{apiName};{JsonConvert.SerializeObject(model)}");

                HttpClient client = _httpClientFactory.CreateClient();
                var hContent = new StringContent(JsonConvert.SerializeObject(model));
                hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                if (!string.IsNullOrEmpty(token))
                    client.DefaultRequestHeaders.Add("token", token);

                using (HttpResponseMessage response = client.PostAsync(url + "/" + apiName, hContent).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return response.Content.ReadAsStringAsync();
                }
            }
        }

        public async Task<T> DeleteAsync<T>(string url)
        {
            using (MiniProfiler.Current.CustomTiming("HTTP", "DELETE:" + url, "DELETE"))
            {
                var client = _httpClientFactory.CreateClient();
                using (var response = await client.DeleteAsync(url))
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的

                    return JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
                }
            }
        }

        public async Task<String> UploadFile(String url, KeyValuePair<string, KeyValuePair<string, byte[]>> formData)
        {
            using (MiniProfiler.Current.CustomTiming("HTTP", "Post:" + url))
            {
                using (var client = _httpClientFactory.CreateClient())
                {
                    try
                    {
                        //_logger.LogInformation($"上传UploadFile方法入参 >>> Url:{url}，formData:{JsonConvert.SerializeObject(formData)}");
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                        client.AddAuthenticate(_grpc, _configuration, _httpContextAccesor);
                        //初始化参数
                        var content = new MultipartFormDataContent();
                        string boundary = string.Format("--{0}", DateTime.Now.Ticks.ToString("x"));
                        content.Headers.Add("ContentType", $"multipart/form-data, boundary={boundary}");
                        content.Add(new ByteArrayContent(formData.Value.Value), formData.Value.Key, formData.Key);
                        HttpResponseMessage httpResponseMessage = await client.PostAsync(url, content).ConfigureAwait(false);
                        var result = await httpResponseMessage.Content.ReadAsStringAsync().ConfigureAwait(false);
                        _logger.LogInformation($"上传UploadFile方法出参 >>> result:{result}");
                        return result;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"上传UploadFile方法异常 >>> 异常原因:{ex.Message}");
                        return string.Empty;
                    }
                }
            }
        }
        /// <summary>
        /// 使用get下载文件
        /// </summary>
        /// <param name="docId">文档连接</param>
        /// <returns>返回文件流</returns>
        public async Task<(byte[], MediaTypeHeaderValue, string)> DowLoadFile(string url)
        {
            using (var client = _httpClientFactory.CreateClient("DocparkHost"))
            {
                try
                {
                    client.AddAuthenticate(_grpc, _configuration, _httpContextAccesor);
                    using (var response = await client.GetAsync(url))
                    {
                        response.EnsureSuccessStatusCode();//用来抛异常的
                        return (await response.Content.ReadAsByteArrayAsync(), response.Content.Headers.ContentType, response.Content.Headers?.ContentDisposition?.FileName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"DowLoadFile请求{url};");
                    throw;
                }
            }
        }
    }
}
