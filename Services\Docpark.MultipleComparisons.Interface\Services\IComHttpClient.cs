﻿using Docpark.MultipleComparisons.Interface.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{

    public interface IComHttpClient
    {
        /// <summary>
        /// 获取Token
        /// </summary>
        Dictionary<string, object> GetApiToken();

        /// <summary>
        /// 获取指定的ApiHost地址
        /// </summary>
        /// <param name="apiName">ApiHost名称</param>
        /// <returns>返回ApiHost地址</returns>
        string GetApiHost(string apiName);
        /// <summary>
        /// 获取AbpUserConfiguration的配置数据
        /// </summary>
        /// <returns>返回abpConfig</returns>
        JObject GetAbpConfig();

        string Get(string url);
        string Post(string url, object content);
        string PostByText(string url, object context);
        string PostJson(string url, string apiName, object model, string token = "");
        string PostJson(string url, object model, string token = "");
        string PostJson(string url, object model, string tokenHeadersName, string token = "");
        string PostFile(string url, Dictionary<string, object> dic, byte[] bytes, string fileName = "image_file");

        /// <summary>
        /// 获取文件消息
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        string GetDocumentURL(string docId);

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        Task<(string documentId, string fileDownloadUrl, string errMsg)> UploadFile(KeyValuePair<string, byte[]> file);

        /// <summary>
        /// 图片地址转为base64
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        (byte[], MediaTypeHeaderValue) DowLoadFile(string docId);

        Task<ReimbursementConfigManagementDto> GetReimbursementConfig();
        /// <summary>
        /// 获取第三方组织架构信息数据
        /// </summary>
        /// <param name="url">请求url</param>
        /// <param name="requestData">请求参数</param>
        /// <returns>返回第三方组织架构信息数据</returns>
        string GetThirdpartyOrganization(string url, Dictionary<string, object> requestData);
        /// <summary>
        /// 获取组织架构树
        /// </summary>
        /// <returns></returns>
        string GetOrganizationTreeData();
        /// <summary>
        /// 获取用户数据集
        /// </summary>
        /// <returns></returns>
        string GetUsers();
        /// <summary>
        /// 同步第三方机构数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <returns></returns>
        string SyncThirdpartyOrganization(List<Model_ThirdpartyCreateOrgUnit> requestData);
        /// <summary>
        /// 同步第三方人员数据
        /// </summary>
        /// <param name="requestData">请求参数</param>
        /// <returns></returns>
        string SyncThirdpartyUser(List<Model_ThirdpartyCreateUser> requestData);
        /// <summary>
        /// 发送进项税统计数据到统计报表
        /// </summary>
        /// <param name="mstIds"></param>
        /// <returns></returns>
        string SendInputTaxDataToReport(Dictionary<string, object> mstIds);


        #region 异步
        Task<string> PostAsync(string url, object content);


        Task<string> SyncThirdpartyUserAsync(List<Model_ThirdpartyCreateUser> requestData);
        #endregion
    }
}
