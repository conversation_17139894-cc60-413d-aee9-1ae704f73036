﻿using Docpark.ThirdMaster.Services.Models.Dtos;
using iTextSharp.text.pdf;
using SharpCompress.Readers;
using System.Collections.Generic;
using System.IO;
using System;
using DocPark.Commons;
using System.Drawing;
using iTextSharp.text;

namespace Docpark.ThirdMaster.Services.Utils
{
    public static class FileOptHelper
    {
        public static List<(string, byte[])> UnZip(byte[] bytes)
        {
            var result = new List<(string, byte[])>();
            using (var reader = ReaderFactory.Open(new MemoryStream(bytes)))
            {
                while (reader.MoveToNextEntry())
                {
                    if (!reader.Entry.IsDirectory)
                    {
                        using (var imgStream = new MemoryStream())
                        {
                            reader.WriteEntryTo(imgStream);
                            result.Add((reader.Entry.Key, imgStream.ToArray()));
                        }
                    }
                }
            }
            return result;
        }

        public static byte[] GeneratePdfFile(List<byte[]> imgs)
        {
            var width = PageSize.A4.Width;
            var height = PageSize.A4.Height;

            var fileStream = new MemoryStream();
            var document = new Document(PageSize.A4, 36f, 72f, 108f, 180f);
            var pdfWriter = PdfWriter.GetInstance(document, fileStream);
            document.Open();
            for (int i = 0; i < imgs.Count; i++)
            {
                if (i > 0)
                {
                    document.NewPage();
                }
                var img = iTextSharp.text.Image.GetInstance(imgs[i]);
                img.ScaleToFit(width, height);
                img.SetAbsolutePosition((width - img.ScaledWidth) / 2, (height - img.ScaledHeight) / 2);
                pdfWriter.DirectContent.AddImage(img);
            }
            document.Close();
            pdfWriter.Close();
            return fileStream.ToArray();
        }
    }
}
