{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  //公司编号:continental/yaopi
  "CompanyCode": "yaopi",
  "AllowedHosts": "*",
  "MongoDBConnectionString": "*********************************************/",
  "MongoDBDataBaseName": "docpark_datafactorydb",
  "IdentityUrl": "https://bate.ele-arch.com/",
  "Grpc_Host": "http://*************:5002",
  "Authentication": {
    "JwtBearer": {
      "SecurityKey": "DocPark_C421AAEE0D114E9C12312qweqwewe32dfbnos",
      "Issuer": "Docpark_NRA",
      "Audience": "Docpark_NRA"
    }
  },
  "Serilog": {
    "elkServerUrl": "http://localhost:9200",
    "indexFormat": "docpark_continental_service",
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "System": "Information"
      }
    }
  },
  "EventBusConnection": "*************", //"bate-v1-app.gooneymc.com", //"127.0.0.1",
  "EventBusUserName": "admin", //"admin",
  "EventBusPassword": "docpark123", //"docpark123",
  "EventBusPort": "31002", //"31002",
  "EventBusRetryCount": 5,
  "SubscriptionClientName": "Document_Continental",
  "AzureServiceBusEnabled": "False",
  "DocumentType": {
    "Company": "BillingCompany",
    "Invoice": "dl_invoice"
  },
  "TimeInterval": 1,
  "ArchiveTimeInterval": 1,
  "UpdateCol": 1,
  "AEWebService": "http://************:8400/AEWebService.asmx",
  "WFWebService_TaskHelper": "http://************:8400/TaskService/WFWebService_TaskHelper.asmx",
  "AgentService": "http://************:8400/AgentWebService.asmx",
  "TifToZip_Url": "http://************:8013/api/tiff/tiftozip",
  "MailMoveFloder": "01_Gooney Finished",
  "IsTestSystem": false,
  "ExportSubConfigs": {
    "Enable": false,
    "FormString": "64e486a08ff5752b540c828b|items;"
  },
  "IAMSyncJobSettings": {
    "Enabled": true,
    "Schedule": "everyminute",
    "DailyRunTime": "02:00",
    "ApiBaseUrl": "https://esc.paraview.cn",
    "AppKey": "your_app_key_here",
    "AppSecret": "your_app_secret_here",
    "PageSize": 100,
    "OrgMongoDbCollectionName": "iam_organizations",
    "AccountMongoDbCollectionName": "iam_accounts",
    "SyncStateCollectionName": "iam_sync_state"
  }
}