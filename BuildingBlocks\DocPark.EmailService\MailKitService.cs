﻿using DocPark.Commons;
using HtmlAgilityPack;
using MailKit;
using MailKit.Net.Imap;
using MailKit.Net.Pop3;
using MailKit.Net.Smtp;
using MailKit.Search;
using MailKit.Security;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MimeKit;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DocPark.EmailService
{
    /// <summary>
    /// MailKit服务
    /// </summary>
    public class MailKitService
    {
        private readonly IHttpClientFactory httpClientFactory;

        /// <summary>
        /// 邮箱账户
        /// </summary>
        public String Account { get; set; }
        /// <summary>
        /// 邮箱密码
        /// </summary>
        public String PassWord { get; set; }
        /// <summary>
        /// 判断是否 添加ID COMMOND命令
        /// </summary>
        public Boolean ImapIdComond { get; set; }
        /// <summary>
        /// 邮箱Imap主机
        /// </summary>
        public String ImapHost { get; set; }
        /// <summary>
        /// 邮箱Imap端口
        /// </summary>
        public Int32 ImapPort { get; set; }
        /// <summary>
        /// Smtp主机
        /// </summary>
        public String SmtpHost { get; set; }
        /// <summary>
        /// 邮箱Smtp端口
        /// </summary>
        public Int32 SmtpPort { get; set; }
        /// <summary>
        /// 日志对象
        /// </summary>
        public ILogger _logger { get; }

        public MailKitService(ILogger logger, IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            this.httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="receive">接收邮箱</param>
        /// <param name="subject">邮件主题</param>
        /// <param name="body">邮件内容</param>
        /// <param name="logStr">日志对象</param>
        /// <returns></returns>
        public Boolean SendMail(String receive, String subject, String body, ref StringBuilder logStr)
        {
            var result = false;
            try
            {
                logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<邮箱发送<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                logStr.AppendLine($"收件人地址:{receive}、收件人标题:{subject}、收件人内容:{body};SmtpHost:{this.SmtpHost};SmtpPort:{this.SmtpPort};Account:{this.Account};");
                if (receive == this.Account)
                {
                    logStr.AppendLine($"邮件发送执行 >>> 【禁止向自身邮箱发送邮件】");
                }
                else
                {
                    //MimeMessage代表一封电子邮件的对象
                    var message = new MimeMessage();
                    //添加发件人地址 Name 发件人名字 sender 发件人邮箱 // 首页邮箱列表显示名字
                    message.From.Add(new MailboxAddress("邮箱收取提示", this.Account));
                    //添加收件人地址
                    var receives = receive.Split(new char[] { ';', ',', '|' });
                    foreach (var item in receives)
                    {
                        if (!string.IsNullOrWhiteSpace(item))
                        {
                            message.To.Add(new MailboxAddress("邮箱收取提示", item));
                        }
                    }
                    //设置邮件主题信息
                    message.Subject = subject;
                    //设置邮件内容
                    var bodyBuilder = new BodyBuilder() { HtmlBody = body };
                    message.Body = bodyBuilder.ToMessageBody();
                    using (var client = new SmtpClient())
                    {
                        // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                        client.ServerCertificateValidationCallback = (s, c, h, e) => true;
                        // Note: since we don't have an OAuth2 token, disable 
                        // the XOAUTH2 authentication mechanism. 
                        client.AuthenticationMechanisms.Remove("XOAUTH2");
                        client.CheckCertificateRevocation = false;
                        //client.SslProtocols = System.Security.Authentication.SslProtocols.Tls12;
                        client.Connect(this.SmtpHost, this.SmtpPort, SecureSocketOptions.StartTls);
                        // Note: only needed if the SMTP server requires authentication
                        client.Authenticate(this.Account, this.PassWord);
                        client.Send(message);
                        client.Disconnect(true);
                        logStr.AppendLine($"邮件发送执行 >>> 【正常】");
                        result = true;
                    }
                }
            }
            catch (Exception ex)
            {
                logStr.AppendLine($"邮件发送执行 >>> 【失败】 >>> 【异常】原因:{ex.Message}");
            }
            logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<邮箱发送<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
            return result;
        }

        /// <summary>
        /// 获取邮件附件
        /// </summary>
        /// <param name="emailVerifyData"></param>
        /// <returns></returns>
        public async Task<List<EmailEntity>> ReceiveEmails(IConfiguration configuration = null)
        {
            var logStr = new StringBuilder();
            var data = new List<EmailEntity>();
            try
            {
                //配置邮箱收集文件类型
                //var extList = new string[] { "pdf", "ofd", "png", "jpg", "jpeg", "bmp", "gif", "xml" };
                var extList = new string[] { "pdf", "ofd", "xml" };
                if (configuration != null)
                {
                    var ext = configuration["ReceiveEmail_File_Ext"];
                    if (!string.IsNullOrWhiteSpace(ext))
                    {
                        extList = ext.Split(',', '|');
                    }
                }

                logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<{DateTime.Now:yyyy-MM-dd hh:mm:ss fff}邮箱收集未读邮件<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");

                using (var client = new ImapClient())
                {
                    // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                    client.ServerCertificateValidationCallback = (s, c, h, e) => true;

                    //登录
                    await client.ConnectAsync(this.ImapHost, this.ImapPort, true);
                    //授权
                    await client.AuthenticateAsync(this.Account, this.PassWord);

                    if (ImapIdComond == true)
                    {
                        if (HasImapCapabilitiesId(client.Capabilities, ImapCapabilities.Id))
                        {
                            var clientImplementation = new ImapImplementation
                            {
                                Name = "MeSince",
                                Version = "2.0"
                            };
                            var serverImplementation = client.Identify(clientImplementation);
                        }
                    }
                    //只获取收件箱文件夹
                    var folder = await client.GetFolderAsync("INBOX");
                    // 已读写的方式打开文件夹
                    var folderAccess = await folder.OpenAsync(FolderAccess.ReadWrite);
                    //所有未读邮件的唯一Id
                    var uniqueIds = await folder.SearchAsync(SearchQuery.NotSeen);
                    logStr.AppendLine().AppendFormat("查询未读邮件:{0}封", uniqueIds.Count).AppendLine();
                    if (uniqueIds.Count > 0)
                    {
                        logStr.AppendLine("邮件附件处理【进行中】");

                        var basePath = AppDomain.CurrentDomain.SetupInformation.ApplicationBase;

                        //邮件读取之后转移的其他文件夹
                        IMailFolder desFolder = null;
                        logStr.AppendLine($"MailMoveFloder:{configuration["MailMoveFloder"]}");
                        if (configuration != null && !string.IsNullOrWhiteSpace(configuration["MailMoveFloder"]))
                        {
                            //desFolder = await client.GetFolderAsync(configuration["MailMoveFloder"]);
                            desFolder = folder.GetSubfolder(configuration["MailMoveFloder"]);
                            logStr.AppendLine($"desFolder:{desFolder?.FullName}");
                        }

                        //获取邮件信息
                        foreach (var uniqueId in uniqueIds)
                        {
                            try
                            {
                                //根据唯一id获取邮件信息
                                MimeMessage message = folder.GetMessage(uniqueId);
                                var from = message.From[0] as MailboxAddress;

                                logStr.AppendLine().AppendFormat("维护 >>> 【正常】 昵称：{0} ，邮件地址：{1} ，标题：{2} ，附件总数{3}, >>> 处理中", from.Name, from.Address, message.Subject, message.Attachments.Count()).AppendLine();
                                var saveFilePath = string.Empty;
                                var bigHasFileDownload = true;
                                if (message.Attachments.Count() > 0)
                                {
                                    saveFilePath = Path.Combine(basePath, "EmailCollFiles", from.Address, Guid.NewGuid().ToString("N"));
                                    var dir = new DirectoryInfo(saveFilePath);
                                    if (!dir.Exists)
                                        dir.Create();
                                    //提取该邮件所有普通附件
                                    foreach (var itemAttachment in message.Attachments)
                                    {
                                        if (itemAttachment is MimePart attachment)
                                        {
                                            //下载附件
                                            using (var cancel = new System.Threading.CancellationTokenSource())
                                            {
                                                var filePath = Path.Combine(saveFilePath, attachment.FileName);
                                                using (var stream = File.Create(filePath))
                                                {
                                                    await attachment.Content.DecodeToAsync(stream, cancel.Token);
                                                }
                                                logStr.AppendFormat("附件名: {0} 路径: {0} 已经被下载。", attachment.FileName, filePath).AppendLine();
                                            }
                                        }
                                        else
                                        {
                                            logStr.AppendFormat("附件类型为MessagePart,跳过读取。").AppendLine();
                                        }
                                    }
                                }
                                else
                                {
                                    logStr.AppendLine("判断是否有超大附件 >>> ");
                                    var uids = new UniqueIdSet { uniqueId };
                                    var summaries = client.Inbox.Fetch(uids, MessageSummaryItems.BodyStructure);
                                    var bigAttr = 0;
                                    foreach (var summary in summaries)
                                    {
                                        logStr.Append("summaries  >>> ");
                                        if (summary.HtmlBody != null)
                                        {
                                            logStr.Append("HtmlBody >>> ");
                                            var mimeEntity = folder.GetBodyPart(summary.UniqueId, summary.HtmlBody);
                                            if (mimeEntity != null)
                                            {
                                                logStr.Append("mimeEntity >>> ");
                                                (Boolean bigFileHandleState, Boolean bigFileDownload, var i) = HandleTransitFile(from.Address, mimeEntity, ref saveFilePath, ref logStr);
                                                bigAttr += i;
                                                bigHasFileDownload = bigFileDownload;//是否全部下载
                                            }
                                        }

                                        if (summary.Attachments.Count() > 0)
                                        {
                                            foreach (var attachment in summary.Attachments)
                                            {
                                                if (String.IsNullOrEmpty(saveFilePath))
                                                {
                                                    saveFilePath = Path.Combine(basePath, "EmailCollFiles", from.Address, Guid.NewGuid().ToString("N"));
                                                    var dir = new DirectoryInfo(saveFilePath);
                                                    if (!dir.Exists) dir.Create();
                                                }
                                                // 像我们对内容所做的那样下载附件
                                                var entity = client.Inbox.GetBodyPart(uniqueId, attachment);
                                                // 附件可以是message / rfc822部件或常规MIME部件
                                                var messagePart = entity as MessagePart;
                                                if (messagePart != null)
                                                {
                                                    var rfc822 = messagePart;
                                                    var filePath = Path.Combine(saveFilePath, attachment.PartSpecifier + ".eml");
                                                    await rfc822.Message.WriteToAsync(filePath);
                                                    logStr.AppendFormat("附件名: {0} 路径: {0} 已经被下载。", attachment.FileName, filePath).AppendLine();
                                                    bigAttr++;
                                                }
                                                else
                                                {
                                                    var part = (MimePart)entity;
                                                    using var cancel = new System.Threading.CancellationTokenSource();
                                                    var filePath = Path.Combine(saveFilePath, attachment.FileName);
                                                    using (var stream = File.Create(filePath))
                                                        await part.Content.DecodeToAsync(stream, cancel.Token);
                                                    bigAttr++;
                                                    logStr.AppendFormat("附件名: {0} 路径: {0} 已经被下载。", attachment.FileName, filePath).AppendLine();
                                                }
                                            }
                                        }
                                    }
                                    if (bigAttr <= 0)
                                    {
                                        logStr.AppendLine("附件信息：【无附件信息】");
                                        //var exec = SendMail(from.Address, "邮箱收取失败", "无附件信息！", ref logStr);
                                    }
                                }

                                (Int32 state, List<Attachments> attachments) = HandleFile(saveFilePath, extList, ref logStr);
                                if (state == 0 && bigHasFileDownload == true)
                                {
                                    if (attachments?.Count > 0)
                                        data.Add(new EmailEntity()
                                        {
                                            UniqueId = uniqueId + "",
                                            SendAddress = from.Address,
                                            SendlName = from.Name,
                                            Subject = message.Subject,
                                            Date = message.Date.DateTime,
                                            Attachments = attachments,
                                        });
                                }
                                else if (state == 1)
                                {
                                    var exec = SendMail(from.Address, "邮箱收取失败", "附件存在格式不正确的文件。当前仅支持png,jpg,jpeg,pdf,ofd,bmp,zip,rar格式，检查后重新发送邮件！", ref logStr);
                                }
                                else if (state == 2)
                                {
                                    var exec = SendMail(from.Address, "邮箱收取失败", "单个文件大小不超过8M，压缩包大小不超过20M，检查后重新发送邮件！", ref logStr);
                                }
                                else if (state == 3 && bigHasFileDownload == false) //两个条件确定文件未下载，则因文件大小超过20M
                                {
                                    var exec = SendMail(from.Address, "邮箱收取失败", "单个文件大小不超过8M，压缩包大小不超过20M，检查后重新发送邮件！", ref logStr);
                                }

                                /*将邮件设为已读*/
                                MessageFlags flags = MessageFlags.Seen;
                                await folder.SetFlagsAsync(uniqueId, flags, true);
                                if (desFolder != null)
                                    await folder.MoveToAsync(uniqueId, desFolder);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "读取邮件异常");
                            }
                        }
                    }
                    //关闭文件夹
                    await folder.CloseAsync();
                    await client.DisconnectAsync(true);
                    client.Dispose();
                }
                logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<{DateTime.Now:yyyy-MM-dd hh:mm:ss fff}获取邮箱未读邮件附件<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                _logger.LogInformation(logStr.ToString());
            }
            catch (Exception ex)
            {
                logStr.AppendLine($"邮件执行异常 >>> 异常原因:{ex.Message}");
                logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<{DateTime.Now:yyyy-MM-dd hh:mm:ss fff}获取邮箱未读邮件附件<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                _logger.LogError(logStr.ToString());
            }
            return data;
        }

        /// <summary>
        /// 中转附件处理  bigFileDownload->如有部分未下载则false(条件大于20M)
        /// </summary>
        /// <param name="mimeEntity"></param>
        /// <param name="saveFilePath"></param>
        /// <param name="logStr"></param>
        /// <returns></returns>
        private (Boolean bigFileHandleState, bool bigFileDownload, Int32 i) HandleTransitFile(String address, MimeEntity mimeEntity, ref String saveFilePath, ref StringBuilder logStr)
        {
            var i = 0;
            var bigFileDownload = true;
            var bigFileHandleState = true;
            try
            {
                var multipart = mimeEntity as TextPart;
                if (!String.IsNullOrEmpty(multipart.Text))
                {
                    logStr.Append("mimeEntity >>> ");
                    HtmlDocument doc = new HtmlDocument();
                    doc.LoadHtml(multipart.Text);
                    var mailBigAttachNodes = doc.DocumentNode.SelectSingleNode("div[@id='QQMailBigAttach']");
                    if (mailBigAttachNodes != null)
                    {
                        logStr.Append("QQMailBigAttach >>> ");
                        var qqmailbgattach = mailBigAttachNodes.SelectNodes("//span[@class='qqmailbgattach']");
                        logStr.Append($"mimeEntity >>> {qqmailbgattach.Count}");
                        if (qqmailbgattach.Count > 0)
                        {
                            if (String.IsNullOrEmpty(saveFilePath))
                            {
                                var basePath = AppDomain.CurrentDomain.SetupInformation.ApplicationBase;
                                saveFilePath = Path.Combine(basePath, "EmailCollFiles", address, Guid.NewGuid().ToString("N"));
                                var dir = new DirectoryInfo(saveFilePath);
                                if (!dir.Exists) dir.Create();
                            }
                            foreach (var attachItem in qqmailbgattach)
                            {
                                var downloadlink = attachItem.GetAttributeValue("downloadlink", "");
                                logStr.Append($"downloadlink >>> {downloadlink}");
                                if (!String.IsNullOrEmpty(downloadlink))
                                {
                                    var url = HttpUtility.HtmlDecode(downloadlink);
                                    HtmlWeb web = new HtmlWeb();
                                    var htmlDoc = web.Load(url);
                                    var itemNodes = htmlDoc.DocumentNode.SelectSingleNode("//div[@class='ft_d_mainWrapper']");
                                    if (itemNodes != null)
                                    {
                                        var fileSize = itemNodes.SelectSingleNode("//span[@class='ft_d_fileSize']");
                                        if (fileSize != null)
                                        {
                                            var fileSizeValue = fileSize.InnerText;
                                            if (fileSizeValue.EndsWith("M"))
                                            {
                                                fileSizeValue = fileSizeValue.TrimEnd('M');
                                                if (Convert.ToDouble(fileSizeValue) > 20)
                                                {
                                                    i++;
                                                    bigFileDownload = false;
                                                    continue;
                                                }
                                            }
                                        }
                                        var diggnumnode = itemNodes.SelectSingleNode("//img[@class='ft_d_fileIcon']");//获取文件图标以及名字
                                        if (diggnumnode != null)
                                        {
                                            string filename = diggnumnode.GetAttributeValue("filename", "");
                                            var ft_d_btnDownload = itemNodes.SelectSingleNode("//a[@class='ft_d_btnDownload btn_blue']");
                                            if (!String.IsNullOrEmpty(filename) && ft_d_btnDownload != null)
                                            {
                                                var fileDownload = ft_d_btnDownload.GetAttributeValue("href", "");
                                                if (!String.IsNullOrEmpty(fileDownload))
                                                {
                                                    var httpClient = httpClientFactory.CreateClient();
                                                    var filePath = Path.Combine(saveFilePath, filename);
                                                    Stream stream = httpClient.GetStreamAsync(fileDownload).Result;
                                                    using (FileStream fs = new FileStream(filePath, FileMode.Create))
                                                    {
                                                        stream.CopyTo(fs);
                                                    }
                                                    i++;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }

                        if (qqmailbgattach.Count != i)
                        {
                            logStr.AppendLine($"处理超大附件文件 >>> 【失败】");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logStr.AppendLine($"处理超大附件文件异常 >>> 异常原因:{ex.Message}");
                bigFileHandleState = false;
                return (bigFileHandleState, bigFileDownload, i);
            }
            return (bigFileHandleState, bigFileDownload, i);
        }

        /// <summary>
        /// 处理文件
        /// </summary>
        /// <param name="saveFilePath"></param>
        /// <param name="extList"></param>
        /// <param name="logStr"></param>
        /// <returns></returns>
        public (Int32 state, List<Attachments> attachments) HandleFile(String saveFilePath, String[] extList, ref StringBuilder logStr)
        {
            var state = 0;
            var attachments = new List<Attachments>();
            try
            {
                logStr.AppendFormat("解压文件目录：{0} ", saveFilePath).AppendLine();
                if (!String.IsNullOrEmpty(saveFilePath))
                {
                    UnZipAllFiles(saveFilePath, ref logStr);
                    var allFiles = FileHelper.GetAllFiles(saveFilePath);
                    logStr.AppendFormat("处理后文件的总数：{0} ", allFiles.Count).AppendLine();
                    if (allFiles.Count > 0)
                    {
                        var fileFormatVerify = true;
                        var fileSizeVerify = true;
                        foreach (DictionaryEntry file in allFiles)
                        {
                            var uploadFilePath = file.Key.ToString();
                            var ext = Path.GetExtension(uploadFilePath).Replace(".", String.Empty).ToLower();
                            if (extList.Contains(ext))
                            {
                                attachments.Add(new Attachments() { FileName = Path.GetFileName(uploadFilePath), FilePath = uploadFilePath });
                            }
                            // 过滤判断文件存在出现不合法的文件格式
                            //if (!new String[] { "pdf", "ofd", "png", "jpg", "jpeg", "bmp", "gif", "rar", "zip" }.Contains(ext))
                            //{
                            //    fileFormatVerify = false;
                            //    logStr.AppendLine($"附件中出现不合法的文件：【文件格式不合法】，文件名：{Path.GetFileName(uploadFilePath)}，路径：{uploadFilePath}");
                            //}
                            //判断文件大小,单个文件不大于8M，压缩包不大于20M
                            //FileInfo fi = new FileInfo(uploadFilePath);
                            //long fileSize = fi.Length;
                            //if (new String[] { "rar", "zip" }.Contains(ext) && fileSize > 20971520 || extList.Contains(ext) && fileSize > 8388608)
                            //{
                            //    fileSizeVerify = false;
                            //    logStr.AppendLine($"附件中文件大小超出上限：【文件大小不合法】，文件名：{Path.GetFileName(uploadFilePath)}，路径：{uploadFilePath}");
                            //}
                        }
                        state = (fileFormatVerify, fileSizeVerify) switch
                        {
                            (false, true) => 1,
                            (true, false) => 2,
                            (_, _) => 0
                        };
                    }
                    else
                    {
                        state = 3;
                    }
                }
                else
                {
                    state = 3;
                }
            }
            catch (Exception ex)
            {
                logStr.AppendLine($"处理文件处理异常 >>> 异常原因:{ex.Message}");
            }
            return (state, attachments);
        }

        /// <summary>
        /// 解压文件
        /// </summary>
        /// <param name="attsPath"></param>
        /// <param name="logStr"></param>
        public void UnZipAllFiles(String attsPath, ref StringBuilder logStr)
        {
            var tally = 0;
            try
            {
                logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<解压文件处理<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                for (var i = tally; i < 1; i++)
                {
                    var zipFiles = FileHelper.GetAllFiles(attsPath);
                    logStr.AppendLine($"第：{i}，获取上传根目录文件总数:{zipFiles.Count}");
                    foreach (DictionaryEntry zipFile in zipFiles)
                    {
                        var filePath = zipFile.Key.ToString();
                        var ext = Path.GetExtension(filePath).Replace(".", String.Empty).ToLower();
                        if (new String[] { "rar", "zip" }.Contains(ext))
                        {
                            var newZipfileName = Path.GetFileNameWithoutExtension(filePath);
                            var directoryName = Path.GetDirectoryName(filePath);
                            var extractFilePath = Path.Combine(directoryName, newZipfileName);
                            var zipDir = new DirectoryInfo(extractFilePath);
                            if (!zipDir.Exists) zipDir.Create();
                            FileHelper.UnCompress(filePath, extractFilePath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logStr.AppendLine($"解压文件处理异常 >>> 异常原因:{ex.Message}");
            }
            logStr.AppendLine($"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<解压文件处理<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        }

        /// <summary>
        /// mailkit还提供查询是否支持命令的语句，如果你不知道邮箱是否支持这个命令可以使用下面的函数来判断
        /// </summary>
        /// <param name="sourceFlag"></param>
        /// <param name="targetFlag"></param>
        /// <returns></returns>
        private static bool HasImapCapabilitiesId(ImapCapabilities sourceFlag, ImapCapabilities targetFlag)
        {
            return ((sourceFlag | targetFlag) == sourceFlag);
        }
    }
}
