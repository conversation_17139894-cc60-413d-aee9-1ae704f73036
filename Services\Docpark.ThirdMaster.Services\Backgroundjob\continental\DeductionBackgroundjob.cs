﻿using Docpark.ThirdMaster.Services.Utils;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.Backgroundjob.continental
{
    [MasterServiceType("continental")]
    /// <summary>
    /// 发票抵扣
    /// </summary>
    public class DeductionBackgroundjob : BackgroundService,IThirdMasterBackgroundJob
    {
        private readonly ILogger<DeductionBackgroundjob> _logger;
        private readonly IObjectQuery objectQuery;
        private readonly IConfiguration configuration;
        private readonly IDocumentWorkflowService documentWorkflowService;
        private readonly IEventBus eventBus;
        protected string configXml = "Deduction.xml";


        public DeductionBackgroundjob(ILogger<DeductionBackgroundjob> logger,
            IObjectQuery objectQuery, IConfiguration configuration,
            IDocumentWorkflowService documentWorkflowService, IEventBus eventBus)
        {
            _logger = logger;
            this.objectQuery = objectQuery;
            this.configuration = configuration;
            this.documentWorkflowService = documentWorkflowService;
            this.eventBus = eventBus;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["TimeInterval"])), stoppingToken);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await RunAsync();
                }
                catch (TaskCanceledException)
                {
                    // When the stopping token is canceled, for example, a call made from services.msc,
                    // we shouldn't exit with a non-zero exit code. In other words, this is expected...
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "{Message}", ex.Message);
                }
                await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["TimeInterval"])), stoppingToken);
            }
        }
        bool ExcelSubmitIsOK = true;

        protected async Task RunAsync()
        {
            bool flag = true;
            StringBuilder strMessage = new StringBuilder();
            strMessage.AppendLine("=====开始==========================================================");
            strMessage.AppendLine("[抵扣认证]:RunAsync");
            try
            {
                //string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');
                NPOIHelper eh = new NPOIHelper();
                DataSet ds = new DataSet();
                DataTable dt = new DataTable();

                string OutFlord = "";

                OutFlord = EncryptionHelper.GetDataXMLByName(configXml, "flowset" + "/OutFlord");
                List<FileInfo> FileList = GetReadExcelInfo("flowset");

                FileList = FileList.OrderBy(f => f.LastWriteTime).ToList();

                eh.RowExcelNow += new NPOIHelper.delegateReadRow(Eh_RowExcelNow);

                var checkFields = EncryptionHelper.GetDataXMLByName(configXml, "CheckFields").Split(',').ToList();

                foreach (FileInfo fi in FileList)
                {
                    strMessage.AppendLine($"执行文件:{fi.Name}");
                    ExcelSubmitIsOK = true;
                    string guidstring = DateTime.Now.ToString("yyyyMMddHHmssfff");
                    string _filenamename = fi.Name.Replace(fi.Extension, "_" + guidstring + fi.Extension);
                    string temppath = fi.DirectoryName + "/Temp/" + _filenamename;
                    string Bakpath = fi.DirectoryName + "/backup/" + _filenamename;

                    if (!Directory.Exists(fi.DirectoryName + "/Temp/"))
                    {
                        Directory.CreateDirectory(fi.DirectoryName + "/Temp/");
                    }
                    if (!Directory.Exists(fi.DirectoryName + "/backup/"))
                    {
                        Directory.CreateDirectory(fi.DirectoryName + "/backup/");
                    }

                    if (!Directory.Exists(fi.DirectoryName + "/none/"))
                    {
                        Directory.CreateDirectory(fi.DirectoryName + "/none/");
                    }
                    string none = fi.DirectoryName + "/none/" + _filenamename;

                    string errorMessage = "";
                    if (File.Exists(fi.FullName))
                    {
                        bool Isok = eh.ReadExcel(fi.FullName, none, temppath, OutFlord + "/" + _filenamename, Bakpath, configuration,
                            checkFields, out errorMessage, true);
                        if (!string.IsNullOrEmpty(errorMessage))
                        {
                            strMessage.AppendLine($"{fi.FullName}错误:" + errorMessage);
                        }

                        if (ExcelSubmitIsOK)
                        {
                            if (File.Exists(fi.FullName))
                                File.Delete(fi.FullName);
                        }
                    }
                }
                strMessage.AppendLine("运行完成");
            }
            catch (Exception ex)
            {
                flag = false;
                strMessage.AppendLine("错误" + ex.Message);
                strMessage.AppendLine(ex.StackTrace);
            }
            strMessage.AppendLine("=====结束==========================================================");
            if (flag)
            {
                _logger.LogInformation(strMessage.ToString());
            }
            else
            {
                _logger.LogError(strMessage.ToString());
            }
        }

        private string Eh_RowExcelNow(Dictionary<string, string> RowData)
        {
            string[] PrPKs = EncryptionHelper.GetDataXMLByName(configXml, "ExcelColumns").Split(',');
            string eleInvoiceNumber = EncryptionHelper.GetDataXMLByName(configXml, "EleInvoiceNumber");
            List<int> hasValues = new List<int>();
            //判断当前行是否有数据
            foreach (var p in PrPKs)
            {
                if (!RowData.ContainsKey(p) || string.IsNullOrEmpty(RowData[p]))
                {
                    hasValues.Add(0);
                }
                else
                {
                    hasValues.Add(1);
                }
            }
            if (RowData.ContainsKey(eleInvoiceNumber) && !string.IsNullOrEmpty(RowData[eleInvoiceNumber]))
            {
                hasValues.Add(1);
            }
            if (hasValues.Count(w => w == 0) == hasValues.Count)
            {
                return "无数据";
            }
            StringBuilder strMessage = new StringBuilder();
            strMessage.AppendLine("=====开始==========================================================")
                .AppendLine("[抵扣认证]:Eh_RowExcelNow")
                .AppendLine($"数据:{JsonConvert.SerializeObject(RowData)}");
            string IsDK = "没有流程";
            try
            {
                string[] Parper_PrPKs = EncryptionHelper.GetDataXMLByName(configXml, "DocumentColumns").Split('|');
                string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');

                List<Dictionary<string, string>> ReturnDataList = new List<Dictionary<string, string>>();


                bool IsHasPrkey = true;
                List<Filter> filters = new List<Filter>();
                string numberFieldName = "";

                //非电票
                for (int i = 0; i < PrPKs.Length; i++)
                {
                    if (string.IsNullOrEmpty(RowData[PrPKs[i]]))
                    {
                        IsHasPrkey = false;
                    }
                    else
                    {
                        ///判断是否包含中文,如果包含中文则按照为空的逻辑处理

                        bool containsChinese = Regex.IsMatch(RowData[PrPKs[i]], @"[\u4e00-\u9fa5]");
                        Console.WriteLine($"判断是否包含中文:[{RowData[PrPKs[i]]}],结果:{containsChinese.ToString()}");
                        if (containsChinese)
                        {
                            IsHasPrkey = false;
                        }
                        else
                        {
                            filters.Add(new Filter()
                            {
                                Field = Parper_PrPKs[i],
                                Method = "eq",
                                Mode = "eq",
                                Values = new string[] { RowData[PrPKs[i]] }
                            });
                        }
                    }
                    if (Parper_PrPKs[i] == "number")
                    {
                        numberFieldName = PrPKs[i];
                    }
                }
                //如果发票号码找不到信息则找数电票字段
                if (!IsHasPrkey && RowData.ContainsKey(eleInvoiceNumber) && !string.IsNullOrEmpty(RowData[eleInvoiceNumber]))
                {
                    filters = new List<Filter>();
                    //电票逻辑
                    filters.Add(new Filter()
                    {
                        Field = "number",
                        Method = "eq",
                        Mode = "eq",
                        Values = new string[] { RowData[eleInvoiceNumber] }
                    });
                    numberFieldName = eleInvoiceNumber;
                }

                ///发票号码大于15为电票, 只需要发票号码也可以匹配数据
                if (RowData[numberFieldName].Length > 15)
                {
                    IsHasPrkey = true;
                }
                if (IsHasPrkey && filters.Count > 0)
                {
                    var invoiceResult = objectQuery.GetList<InvoiceInfo>(configuration["DocumentType:Invoice"], filters, new List<Sorter>(), 0, 10).Result;

                    foreach (var invoice in invoiceResult.data)
                    {
                        string _stepName = invoice.StepName;
                        bool checkFlow = false;
                        foreach (var flowName in flownames)
                        {
                            var stepname = EncryptionHelper.GetDataXMLByName(configXml, flowName + "/Stepname");
                            var businessTypeId = EncryptionHelper.GetDataXMLByName(configXml, flowName + "/BusinessTypeId");
                            if (_stepName == "等待归档" && invoice.businesstypeid == businessTypeId)
                            {
                                checkFlow = true;
                                break;
                            }
                            if (_stepName == stepname && invoice.businesstypeid == businessTypeId)
                            {
                                checkFlow = true;
                                break;
                            }
                        }

                        if (_stepName == "等待归档" && checkFlow)
                        {
                            strMessage.AppendLine("流程已经等待归档：");
                            IsDK = "有流程提交成功";
                            break;
                        }
                        else if (checkFlow)
                        {
                            var documentTask = documentWorkflowService.GetLasterActiveDocumentTask(invoice.MstId).Result;
                            if (documentTask == null)
                            {
                                strMessage.AppendLine($"未找到任务");
                                continue;
                            }

                            var message = new WorkflowMQContent()
                            {
                                RequestId = Guid.NewGuid().ToString("N").ToUpper(),
                                MessageType = documentTask.FileType,
                                DocumentID = documentTask.DocumentIdentity,
                                DocumentType = documentTask.DocumentTypeIdentity,
                                MstId = documentTask.MstId,
                                BusinessType = documentTask.BusinessType,
                                DocumentTaskID = documentTask.Id.ToString(),
                                DocumentTaskActionID = documentTask.CurrentTaskActionIdentity,
                                ScanUser = documentTask.ScanUser,
                                OwnUser = documentTask.OwnUser,
                                Name = documentTask.TriggerName,
                                Code = documentTask.TriggerCode,
                                WorkflowId = documentTask.WorkflowIdentity,
                                TriggerMessage = "",
                                TriggerStatus = true,
                                Data = null
                            };

                            string publishMessage = JsonConvert.SerializeObject(message);

                            DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(publishMessage);
                            eventBus.Publish(publishEventData);
                            IsDK = "有流程提交成功";
                        }
                        else
                        {
                            strMessage.AppendLine($"Current Process：{_stepName},{invoice.businesstypeid}");
                            continue;
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                strMessage.AppendLine("异常：" + JsonConvert.SerializeObject(RowData));
                strMessage.AppendLine(ex.Message);
                strMessage.AppendLine(ex.StackTrace);
                ExcelSubmitIsOK = false;
                IsDK = "查询失败";
            }
            finally
            {
                strMessage.AppendLine("IsDK:" + IsDK);
                strMessage.AppendLine("=====结束==========================================================");
                if (IsDK == "有流程提交成功")
                {
                    _logger.LogInformation(strMessage.ToString());
                }
                else
                {
                    _logger.LogError(strMessage.ToString());
                }
            }
            return IsDK;
        }

        public List<FileInfo> GetReadExcelInfo(string fileName)
        {
            List<FileInfo> FileList = new List<FileInfo>();
            string folder = EncryptionHelper.GetDataXMLByName(configXml, fileName + "/ReadFlord");
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }

            DirectoryInfo TheFolder = new DirectoryInfo(folder);
            //遍历文件
            foreach (FileInfo NextFile in TheFolder.GetFiles())
            {
                if (NextFile.Extension.ToLower() == ".xls" || NextFile.Extension.ToLower() == ".xlsx")
                {
                    FileList.Add(NextFile);
                }
            }
            return FileList;
        }
    }
}
