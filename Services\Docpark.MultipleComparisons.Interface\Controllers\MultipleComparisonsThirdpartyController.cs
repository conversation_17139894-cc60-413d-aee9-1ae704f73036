﻿using Docpark.HttpClientExtension.IServices;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Docpark.MultipleComparisons.Interface.Models;
using Microsoft.Extensions.Logging;
using Docpark.MultipleComparisons.Interface.Services;
using Docpark.HttpClientExtension.IServices.Dto;
using Microsoft.Extensions.Configuration;
using Docpark.HttpClientExtension.Grpc;
using System.Text;

namespace Docpark.MultipleComparisons.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MultipleComparisonsThirdpartyController : ControllerBase
    {
        private readonly ILogger<MultipleComparisonsThirdpartyController> _logger;
        private IDocparkMetaDataService _docparkMetaDataService { get; }
        private readonly IObjectQuery objectQuery;
        private readonly IMultipComparisonsService multipComparisons;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IConfiguration configuration;
        private readonly IDGrpc dGrpc;
        private readonly IComHttpClient httpClient;

        private readonly string[] fileTypes = new string[] { "png", "jpg", "jpeg", "pdf", "ofd" };

        public MultipleComparisonsThirdpartyController(IDocparkMetaDataService _docparkMetaDataService, ILogger<MultipleComparisonsThirdpartyController> logger, IObjectQuery objectQuery, IMultipComparisonsService multipComparisons, IThirdPartyMongoDbService callLogService, IConfiguration configuration, IDGrpc dGrpc, IComHttpClient httpClient)
        {
            _logger = logger;
            this._docparkMetaDataService = _docparkMetaDataService;
            this.objectQuery = objectQuery;
            this.multipComparisons = multipComparisons;
            this.callLogService = callLogService;
            this.configuration = configuration;
            this.dGrpc = dGrpc;
            this.httpClient = httpClient;
        }

        #region 单据凭证及票据附件影像数据接收API
        /// <summary>
        /// 单据、发票及票据附件数据接收API
        /// </summary>
        /// <param name="request_MC01"></param>
        /// <returns></returns>
        [HttpPost, Route("MC01")]
        public async Task<ResponseResult> MC01(Request_MC01 request_MC01)
        {
            var prevDate = DateTime.Now;
            TimeSpan diff = TimeSpan.Zero;
            StringBuilder stringBuilder = new StringBuilder();
            var responseResult = new ResponseResult();

            var issuccess = false;
            //主数据表单模型接收参数
            MC_BILL_MASTER_DATA mC_BILL_MASTER_DATA = new MC_BILL_MASTER_DATA()
            {
                bill_no = request_MC01.bill_no,
                third_bill_guid = request_MC01.third_bill_guid,
                third_bill_status = request_MC01.third_bill_status,
                third_bill_source = request_MC01.third_bill_source,
                business_type = request_MC01.business_type,
                third_bill_title = request_MC01.third_bill_title,
                proposer_code = request_MC01.proposer_code,
                proposer_name = request_MC01.proposer_name,
                bill_submit_time = request_MC01.bill_submit_time,
                proposer_dept_code = request_MC01.proposer_dept_code,
                proposer_dept_name = request_MC01.proposer_dept_name,
                cost_center_code = request_MC01.cost_center_code,
                cost_center_name = request_MC01.cost_center_name,
                company_code = request_MC01.company_code,
                company_name = request_MC01.company_name,
                expense_details = new List<MC_Expense_details>(),
                annex_items = new List<Annex_items>(),
                supplier_code = request_MC01.supplier_code,
                supplier_name = request_MC01.supplier_name,
                total_amount = request_MC01.total_amount,
                total_approved_amount = request_MC01.total_approved_amount,
                expense_voucher_no = request_MC01.expense_voucher_no,
                payment_voucher_no = request_MC01.payment_voucher_no,
                invoice_qty = request_MC01.invoice_details != null ? request_MC01.invoice_details.Count : 0,
                remark = request_MC01.remark,
                business_status = request_MC01.business_status,
                sys_status = configuration.GetValue<string>("bill_init_status") // 系统状态, 默认: 00 待处理
            };
            //主数据表单费用明细模型
            MC_Expense_details expense_Details = null;
            //主数据表单附件数据模型
            Annex_items annex_Items = null;
            //将MC01参数中的费用明细集合存入到主表单的费用明细模型中
            foreach (var item in request_MC01.expense_details)
            {
                if (item.third_bill_guid.ToString() == mC_BILL_MASTER_DATA.third_bill_guid)
                {
                    expense_Details = new MC_Expense_details();
                    expense_Details.expense_detail_id = item.expense_details_guid;
                    expense_Details.expense_type_code = item.expense_type_code;
                    expense_Details.expense_type_name = item.expense_type_name;
                    expense_Details.apply_amount = item.apply_amount;
                    expense_Details.approved_amount = item.approved_amount;
                    expense_Details.expense_remark = item.expense_remark;

                    //将费用明细模型存入到主表单费用明细集合中
                    mC_BILL_MASTER_DATA.expense_details.Add(expense_Details);
                }
            }
            //将MC01参数中的单据附件集合存入到主表单的附件数据集合模型中
            foreach (var item in request_MC01.other_annex_items)
            {
                var imgdata = string.Empty;
                if (!string.IsNullOrEmpty(item.annex_details))
                {
                    if (!string.IsNullOrEmpty(item.img_data_filetype))
                    {
                        // 2021-8-16 沟通单据其他附件, 不做类型限制
                        //if (this.fileTypes.Contains(item.img_data_filetype))
                        //{
                        var filetype = item.img_data_filetype;
                        imgdata = await this.multipComparisons.UploadImage(item.annex_details, item.annex_guid, filetype);
                        //}
                        //else
                        //{
                        //    imgdata = $"[其他附件({item.annex_guid})]传输的文件类型不符合要求";
                        //}
                    }
                    else
                    {
                        imgdata = "文件类型不能为空";
                    }
                }

                annex_Items = new Annex_items();
                annex_Items.annex_guid = item.annex_guid;
                annex_Items.annex_name = item.annex_name;
                annex_Items.annex_data_type = item.img_data_filetype;
                annex_Items.annex_details = imgdata;
                //将发票附件模型存入到主表单附件数据集合中
                mC_BILL_MASTER_DATA.annex_items.Add(annex_Items);
            }
            Guid bill_mstId = Guid.Empty;
            #region 验真 bill_no 是否已存在
            //查找单据信息
            var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
            filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
            {
                DisplayType = "文本",
                Field = "bill_no",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { request_MC01.bill_no }
            });
            var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();


            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("1. 主表单基础数据准备结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            var select_BillList = this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 1).Result;

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("2. 主表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            int ispost = 0;
            var sys_status = mC_BILL_MASTER_DATA.sys_status;
            if (select_BillList.data != null && select_BillList.totalCount > 0)
            {
                bill_mstId = select_BillList.data[0].mstId;
                ispost = select_BillList.data[0].ispost;
                sys_status = select_BillList.data[0].sys_status;
            }
            else
            {
                bill_mstId = this.dGrpc.NewGuid();
            }
            mC_BILL_MASTER_DATA.ispost = request_MC01.task_type == 1 ? 0 : ispost;

            if (request_MC01.task_type != 2)
                mC_BILL_MASTER_DATA.match_mode = "01"; // 刚开始进入OA单据时, 设置为系统匹配
            else
                mC_BILL_MASTER_DATA.sys_status = sys_status;

            //报销文件-附件
            var reim_file_details = string.Empty;
            if (!string.IsNullOrEmpty(request_MC01.reim_file_base64))
            {
                var filetype = request_MC01.reim_filetype;
                reim_file_details = await this.multipComparisons.UploadImage(request_MC01.reim_file_base64, this.dGrpc.NewGuid().ToString(), filetype);
            }
            mC_BILL_MASTER_DATA.reim_file_details = reim_file_details;
            mC_BILL_MASTER_DATA.reim_filetype = request_MC01.reim_filetype;
            #endregion

            //主数据是否接收成功
            var issucces = await objectQuery.CreateOrUpdate<MC_BILL_MASTER_DATA>(bill_mstId, mC_BILL_MASTER_DATA);

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("3. 主表单-数据保存结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            if (issucces)
            {
                //无实物预付款和无实物报销单  系统状态默认状态为03（已签收）
                if (request_MC01.business_type == "无实物预付款" || request_MC01.business_type == "无实物报销单" || request_MC01.business_type == "有实物预付款")
                {
                    responseResult.isSuccess = true;
                    responseResult.msg = "主数据接收成功";
                }
                //有实物报销单-纯电子发票类和有实物报销单-非纯电子发票类   存在发票明细信息
                else
                {
                    List<MC_BILL_INVOICE_DATA> selectSavedInvoiceList = null;
                    List<MC_BILL_MATCH_RESULT_DATA> selectSavedMatcList = null;
                    if (select_BillList.data != null && select_BillList.totalCount > 0)
                    {
                        #region 验真 bill_no 下是否已经保存了发票明细数据
                            
                        //查找单据信息
                        filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                        filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "bill_no",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { request_MC01.bill_no }
                        });

                        sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();

                        diff = DateTime.Now - prevDate;
                        prevDate = DateTime.Now;
                        stringBuilder.Append("3.1. 发票明细表单-单据号查询前的监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                        var selectSavedInvoiceResult = this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filters, sorters, 0, 999).Result;
                        if (selectSavedInvoiceResult.data != null)
                        {
                            selectSavedInvoiceList = selectSavedInvoiceResult.data;
                        }

                        diff = DateTime.Now - prevDate;
                        prevDate = DateTime.Now;
                        stringBuilder.Append("3.2. 发票明细表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                        #endregion

                        #region 查找 bill_no 对应的发票比对结果数据, 应用于无需匹配的发票数据存储
                        var selectSavedMatchResult = this.objectQuery.GetList<MC_BILL_MATCH_RESULT_DATA>(filters, sorters, 0, 999).Result;
                        if (selectSavedMatchResult.data != null)
                        {
                            selectSavedMatcList = selectSavedMatchResult.data;
                        }

                        diff = DateTime.Now - prevDate;
                        prevDate = DateTime.Now;
                        stringBuilder.Append("3.3. 单据比对表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                        #endregion
                    }

                    List<Guid> toBeSaveInvoiceMstIds = new List<Guid>();
                    List<MC_BILL_INVOICE_DATA> saveInvoiceDatas = new List<MC_BILL_INVOICE_DATA>();
                    //单据发票凭证详情数据
                    MC_BILL_INVOICE_DATA mC_BILL_INVOICE_DATA = null;
                    //获取到发票明细集合中的数据
                    foreach (var item in request_MC01.invoice_details)
                    {
                        mC_BILL_INVOICE_DATA = new MC_BILL_INVOICE_DATA();
                        mC_BILL_INVOICE_DATA.bill_no = item.bill_no;
                        mC_BILL_INVOICE_DATA.third_bill_guid = item.third_bill_guid;
                        mC_BILL_INVOICE_DATA.bill_mstid = bill_mstId;
                        mC_BILL_INVOICE_DATA.third_invoice_guid = item.third_invoice_guid;
                        mC_BILL_INVOICE_DATA.third_type = item.third_type;
                        mC_BILL_INVOICE_DATA.type = item.third_type;
                        mC_BILL_INVOICE_DATA.code = item.code;
                        mC_BILL_INVOICE_DATA.number = item.number;
                        mC_BILL_INVOICE_DATA.date = item.date.ToString();
                        mC_BILL_INVOICE_DATA.pretax_amount = item.pretax_amount;
                        mC_BILL_INVOICE_DATA.tax = item.tax;
                        mC_BILL_INVOICE_DATA.items = item.line_items;
                        mC_BILL_INVOICE_DATA.total = item.total;
                        mC_BILL_INVOICE_DATA.annex_items = new List<Annex_items>();
                        mC_BILL_INVOICE_DATA.buyer = item.buyer;
                        mC_BILL_INVOICE_DATA.buyer_tax_id = item.buyer_tax_id;
                        mC_BILL_INVOICE_DATA.seller = item.seller;
                        mC_BILL_INVOICE_DATA.image_mstid = item.image_mstid;

                        // 设置发票状态为: 报销中
                        var setResult = await this.multipComparisons.SetImageInvoiceStatus(item.image_mstid, "2");
                        if (!setResult)
                        {
                            responseResult.msg = $"更新影像发票状态为报销中失败, 发票影像标识:{item.image_mstid}";
                            return responseResult;
                        }

                        mC_BILL_INVOICE_DATA.seller_tax_id = item.seller_tax_id;
                        mC_BILL_INVOICE_DATA.check_code = item.check_code;
                        mC_BILL_INVOICE_DATA.travel_details = item.travel_details;
                        mC_BILL_INVOICE_DATA.is_required_match = string.IsNullOrEmpty(item.is_required_match) ? "01" : item.is_required_match; // 是否必须匹配, 00 无需匹配, 01 需要匹配(默认值)
                        var selectAnnexItems = request_MC01.annex_items.Where(w => w.third_invoice_guid == item.third_invoice_guid).ToList();
                        if (selectAnnexItems != null)
                        {
                            foreach (var annexItem in selectAnnexItems)
                            {
                                var imgdata = string.Empty;
                                if (!string.IsNullOrEmpty(annexItem.annex_details))
                                {
                                    if (!string.IsNullOrEmpty(annexItem.img_data_filetype))
                                    {
                                        if (this.fileTypes.Contains(annexItem.img_data_filetype.ToLower()))
                                        {
                                            var filetype = annexItem.img_data_filetype.ToLower();
                                            imgdata = await this.multipComparisons.UploadImage(annexItem.annex_details, annexItem.annex_guid, filetype);
                                        }
                                        else
                                        {
                                            imgdata = $"[发票附件({item.third_invoice_guid})]传输的文件类型不符合要求, 无法解析附件文件";
                                            return responseResult;
                                        }
                                    }
                                    else
                                    {
                                        imgdata = "推送数据中文件类型为空值, 无法解析附件文件";
                                    }
                                }

                                annex_Items = new Annex_items();
                                annex_Items.annex_guid = annexItem.annex_guid;
                                annex_Items.annex_data_type = annexItem.img_data_filetype;
                                annex_Items.annex_details = imgdata;
                                //将发票附件存到发票凭证详情表中
                                mC_BILL_INVOICE_DATA.annex_items.Add(annex_Items);
                            }
                        }

                        Guid invoice_mstId = this.dGrpc.NewGuid();
                        if (selectSavedInvoiceList != null && selectSavedInvoiceList.Count > 0)
                        {
                            var selectSavedInvoiceEntity = selectSavedInvoiceList.Where(w => w.third_invoice_guid == item.third_invoice_guid).FirstOrDefault();
                            if (selectSavedInvoiceEntity != null)
                            {
                                invoice_mstId = selectSavedInvoiceEntity.MstId;
                            }
                        }
                        mC_BILL_INVOICE_DATA.MstId = invoice_mstId;
                        toBeSaveInvoiceMstIds.Add(invoice_mstId);
                        saveInvoiceDatas.Add(mC_BILL_INVOICE_DATA);
                    }

                    diff = DateTime.Now - prevDate;
                    prevDate = DateTime.Now;
                    stringBuilder.Append("4. 发票明细表单-数据保存前的监控(发票数量: " + request_MC01.invoice_details.Count + "), 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                    #region 批量保存发票明细数据
                    //单据发票凭证详情数据是否接收成功
                    issuccess = await this.objectQuery.BulkCreateOrUpdate<MC_BILL_INVOICE_DATA>(saveInvoiceDatas);
                    if (issuccess)
                    {
                        #region 发票无需匹配设置
                        var saveInvoiceNotRequiredMatchList = saveInvoiceDatas.Where(w => w.is_required_match == "00").ToList();
                        if (saveInvoiceNotRequiredMatchList != null && saveInvoiceNotRequiredMatchList.Count > 0)
                        {
                            var saveInvoiceMatchResultDatas = new List<MC_BILL_MATCH_RESULT_DATA>();
                            foreach (var item in saveInvoiceNotRequiredMatchList)
                            {
                                Guid match_mstid = this.dGrpc.NewGuid();
                                if (selectSavedMatcList != null && selectSavedMatcList.Count > 0)
                                {
                                    var noSavedMatchMstId = selectSavedMatcList.Where(w => w.inv_mstid == item.MstId.ToString()).Select(s => s.MstId).FirstOrDefault();
                                    if (noSavedMatchMstId != null)
                                    {
                                        match_mstid = Guid.Parse(noSavedMatchMstId);
                                    }
                                }
                                MC_BILL_MATCH_RESULT_DATA mC_BILL_MATCH_RESULT_DATA = new MC_BILL_MATCH_RESULT_DATA();
                                mC_BILL_MATCH_RESULT_DATA.MstId = match_mstid.ToString();
                                mC_BILL_MATCH_RESULT_DATA.bill_no = item.bill_no;
                                mC_BILL_MATCH_RESULT_DATA.bill_mstid = bill_mstId.ToString();
                                mC_BILL_MATCH_RESULT_DATA.scan_mstid = "";
                                mC_BILL_MATCH_RESULT_DATA.inv_mstid = item.MstId.ToString();
                                mC_BILL_MATCH_RESULT_DATA.match_result = "01";
                                mC_BILL_MATCH_RESULT_DATA.is_auto_match = "01";
                                mC_BILL_MATCH_RESULT_DATA.match_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                                mC_BILL_MATCH_RESULT_DATA.remark = "";

                                saveInvoiceMatchResultDatas.Add(mC_BILL_MATCH_RESULT_DATA);
                            }

                            issuccess = await this.objectQuery.BulkCreateOrUpdate<MC_BILL_MATCH_RESULT_DATA>(saveInvoiceMatchResultDatas);
                            if (!issuccess)
                            {
                                _logger.LogError($"主数据(单据号: {request_MC01.bill_no})接收失败, 原因: 发票无需匹配设置。");
                            }

                            diff = DateTime.Now - prevDate;
                            prevDate = DateTime.Now;
                            stringBuilder.Append("4.1. [MC01]发票明细无需匹配比对设置保存结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                        }
                        #endregion

                        #region 删除不存在的发票明细数据
                        if (selectSavedInvoiceList != null && selectSavedInvoiceList.Count > 0)
                        {
                            var noSavedInvoiceMstIds = selectSavedInvoiceList.Where(w => !toBeSaveInvoiceMstIds.Contains(w.MstId)).Select(s => s.MstId).ToList();
                            if (noSavedInvoiceMstIds != null)
                            {
                                if (!await this.objectQuery.RemoveAll(noSavedInvoiceMstIds))
                                {
                                    this._logger.LogError($"[MC01]删除单据({request_MC01.bill_no})下已保存的发票明细失败, 未能成功删除的发票明细[MSTID]: {JsonConvert.SerializeObject(noSavedInvoiceMstIds)}。");
                                }

                                diff = DateTime.Now - prevDate;
                                prevDate = DateTime.Now;
                                stringBuilder.Append("4.2. [MC01]删除单据({request_MC01.bill_no})下已保存的发票明细结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                            }
                        }
                        #endregion

                        responseResult.isSuccess = true;
                        responseResult.msg = "主数据接收成功";
                    }
                    else
                    {
                        responseResult.msg = "主数据接收失败, errorCode: 1401";
                    }
                    #endregion

                    diff = DateTime.Now - prevDate;
                    prevDate = DateTime.Now;
                    stringBuilder.Append("5. 发票明细表单-数据保存结束监控(发票数量: " + request_MC01.invoice_details.Count + "), 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                }
            }
            else
            {
                responseResult.msg = "主数据接收失败, errorCode: 1402";
            }


            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC01",
                ApiDesc = "单据、发票及票据附件数据接收API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC01),
                Remark = ""
            });
            #endregion

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("6. [MC01]写入调用日志结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            this._logger.LogError($"[MC01]执行效率监控: {stringBuilder.ToString()}");

            return responseResult;
        }
        #endregion

        /// <summary>
        /// 单据发票附件或单据扫描影像数据预览API
        /// </summary>
        /// <param name="request_MC02"></param>
        /// <returns></returns>
        [HttpPost, Route("MC02")]
        public ResponseResult MC02(Request_MC02 request_MC02)
        {
            var responseResult = new ResponseResult();
            var title = request_MC02.mark == 1 ? "单据发票附件预览" : "单据扫描影像预览"; // 1 单据发票附件预览, 2 单据扫描影像预览
            try
            {
                var htmlUrl = this.multipComparisons.GetBillPreviewHtml(request_MC02.bill_no, request_MC02.mark);
                if (!string.IsNullOrEmpty(htmlUrl))
                {
                    responseResult.isSuccess = true;
                    responseResult.data = htmlUrl;
                }
                else
                {
                    responseResult.msg = $"获取{title}地址失败。";
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, ex.Message); 
                responseResult.msg = $"获取{title}地址失败, errorCode: 1404";
            }
            return responseResult;
        }

        /// <summary>
        /// 单据回退/通过通知API
        /// </summary>
        /// <param name="request_MC03"></param>
        /// <returns></returns>
        [HttpPost, Route("MC03")]
        public async Task<ResponseResult> MC03(Request_MC03 request_MC03)
        {
            var responseResult = new ResponseResult();
            try
            {
                bool result = false;
                string errMsg = "";
                List<HttpClientExtension.IServices.Dto.Filter> filter = new List<HttpClientExtension.IServices.Dto.Filter>();
                filter.Add(new HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { request_MC03.bill_no }
                });
                List<HttpClientExtension.IServices.Dto.Sorter> sorters = new List<HttpClientExtension.IServices.Dto.Sorter>();
                //获取单据主数据信息
                var listmaster = await objectQuery.GetList<MC_BILL_MASTER_DATA>(filter, sorters, 0, 1);
                List<Guid> masterIds = new List<Guid>();
                if (listmaster.totalCount > 0)
                {
                    switch (request_MC03.status)
                    {
                        case "01"://发起人修改单据, 删除扫描的影像数据(封面, 附件, 票据, 发票);
                            var delResult_01 = await multipComparisons.RemoveBillDataAsync(0, request_MC03.bill_no);
                            result = delResult_01.result;
                            errMsg = delResult_01.errMsg;
                            if (result)
                            {
                                var mstid = listmaster.data[0].mstId;
                                Dictionary<string, object> saveData01 = new Dictionary<string, object>();
                                saveData01["sys_status"] = "01";
                                result = await objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", mstid, saveData01);
                            }
                            if (!string.IsNullOrEmpty(errMsg))
                            {
                                this._logger.LogInformation($"发起人修改单据, 删除扫描的影像数据(封面, 附件, 票据, 发票)失败。原因: {errMsg}");
                            }
                            break;
                        case "02"://发起人废弃单据, 删除单据/发票/凭证数据/电子回单数据/扫描的影像数据(封面, 发票, 附件.,票据)
                            var delResult_02 = await multipComparisons.RemoveBillDataAsync(1, request_MC03.bill_no);
                            result = delResult_02.result;
                            errMsg = delResult_02.errMsg;
                            if (!string.IsNullOrEmpty(errMsg))
                            {
                                this._logger.LogInformation($"发起人废弃单据, 删除数据信息失败。原因: {errMsg}");
                            }
                            break;
                        case "03"://只修改业务状态: 审批中
                            var id = listmaster.data[0].mstId;
                            Dictionary<string, object> saveData03 = new Dictionary<string, object>();
                            saveData03["bill_no"] = request_MC03.bill_no;
                            saveData03["business_status"] = "审批中";
                            result = await objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", id, saveData03);
                            break;
                        case "04"://业务领导终审通过，只修改业务状态: 审批通过
                            var guid = listmaster.data[0].mstId;
                            Dictionary<string, object> saveData04 = new Dictionary<string, object>();
                            saveData04["bill_no"] = request_MC03.bill_no;
                            saveData04["business_status"] = "审批通过";
                            result = await objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", guid, saveData04);
                            if (listmaster.data[0].business_type == "无实物预付款" || listmaster.data[0].business_type == "无实物报销单")
                            {
                                Dictionary<string, object> updateData = new Dictionary<string, object>();
                                updateData["sys_status"] = "03";
                                var upIsSuccess = await this.objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", guid, updateData);
                            }
                            break;
                        default:
                            errMsg = $"单据({request_MC03.bill_no})的状态标识不正确，请确认（01 发起人修改单据，02 发起人废弃单据，03 财务退回）。";
                            break;
                    }
                    if (result)
                    {
                        responseResult.isSuccess = true;
                        responseResult.msg = $"单据({ request_MC03.bill_no})回退通知接收成功。";
                    }
                    else
                    {
                        responseResult.msg = string.IsNullOrEmpty(errMsg) ? $"单据({ request_MC03.bill_no})回退通知接收失败, 请重试。" : errMsg;
                    }
                }
                else
                {
                    responseResult.msg = $"单据编号({request_MC03.bill_no})在影像系统中不存在。";
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, ex.Message); 
                responseResult.msg = "单据回退通知失败, errorCode: 1404";
            }
            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC03",
                ApiDesc = "单据回退通知API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC03),
                Remark = ""
            });
            #endregion
            return responseResult;
        }

        /// <summary>
        /// 单据费用/付款凭证接收API
        /// </summary>
        /// <param name="request_MC04"></param>
        /// <returns></returns>
        [HttpPost, Route("MC04")]
        public async Task<ResponseResult> MC04(Request_MC04 request_MC04)
        {
            var responseResult = new ResponseResult();
            try
            {
                var imgdata = string.Empty;
                if (!string.IsNullOrEmpty(request_MC04.img_data))
                {
                    if (!string.IsNullOrEmpty(request_MC04.img_data_filetype))
                    {
                        if (this.fileTypes.Contains(request_MC04.img_data_filetype))
                        {
                            var filetype = request_MC04.img_data_filetype;
                            imgdata = await this.multipComparisons.UploadImage(request_MC04.img_data, request_MC04.voucher_no, filetype);
                        }
                        else
                        {
                            responseResult.msg = "传输的文件类型不符合要求";
                        }
                    }
                    else
                    {
                        responseResult.msg = "文件类型不能为空";
                    }
                }

                if (string.IsNullOrEmpty(responseResult.msg))
                {
                    if (!string.IsNullOrEmpty(request_MC04.oa_flow_code))
                    {
                        var filters = new List<HttpClientExtension.IServices.Dto.Filter>();
                        filters.Add(new HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "bill_no",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { request_MC04.oa_flow_code }
                        });
                        var sorters = new List<HttpClientExtension.IServices.Dto.Sorter>();
                        var listmster = await objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 1);
                        if (listmster.data != null && listmster.totalCount > 0)
                        {
                            string bill_mstid = listmster.data[0].mstId.ToString();

                            #region 接收到对方的记账凭证后, 则更改发票状态为: 已报销
                            List<string> errMstIds = new List<string>();
                            foreach (var img_mstId in request_MC04.image_mstids)
                            {
                                //发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销
                                var result = await this.multipComparisons.SetImageInvoiceStatus(img_mstId, "3");
                                if (!result)
                                {
                                    errMstIds.Add(img_mstId);
                                }
                            }

                            if (errMstIds != null && errMstIds.Count > 0)
                            {
                                responseResult.msg = $"以下发票影像状态变更为已报销失败|{string.Join("|", errMstIds)}";
                                return responseResult;
                            }
                            #endregion

                            #region 添加票据数据
                            MC_BILL_VOUCHER_DATA mC_BILL_VOUCHER_DATA = new MC_BILL_VOUCHER_DATA()
                            {
                                company_name = request_MC04.company_name,
                                company_code = request_MC04.company_code,
                                accounting_date = request_MC04.accounting_date,
                                voucher_no = request_MC04.voucher_no,
                                batch_name = request_MC04.batch_name,
                                accounting_period = request_MC04.accounting_period,
                                write_off_status = request_MC04.write_off_status,
                                accounting_name = request_MC04.accounting_name,
                                voucher_source = request_MC04.voucher_source,
                                voucher_type = request_MC04.voucher_type,
                                remark = request_MC04.remark,
                                voucher_total_amount = request_MC04.voucher_total_amount,
                                debit_total_amount = request_MC04.debit_total_amount,
                                credit_total_amount = request_MC04.credit_total_amount,
                                voucher_approver_name = request_MC04.voucher_approver_name,
                                voucher_approver_job_num = request_MC04.voucher_approver_job_num,
                                voucher_creator_name = request_MC04.voucher_creator_name,
                                voucher_creator_job_num = request_MC04.voucher_creator_job_num,
                                oa_flow_code = request_MC04.oa_flow_code,
                                img_data = imgdata,
                                img_data_filetype = request_MC04.img_data_filetype,
                                batch_status = request_MC04.batch_status,
                                voucher_ledger = request_MC04.voucher_ledger,
                                currency = request_MC04.currency,
                                ispost = 0,
                                oa_send = 0,
                                inv_send = 0,
                                nc_send = 0,
                                summary_details = new List<summary_details>()
                            };

                            summary_details summary_Details = null;
                            foreach (var item in request_MC04.summary_details)
                            {
                                summary_Details = new summary_details();
                                summary_Details.summary = item.summary;
                                summary_Details.accounting_title = item.accounting_title;
                                summary_Details.account_code = item.accounting_code;
                                summary_Details.dept_name = item.dept_name;
                                summary_Details.dept_code = item.dept_code;
                                summary_Details.user_name = item.user_name;
                                summary_Details.user_code = item.user_code;
                                summary_Details.item_supplier_code = item.item_supplier_code;
                                summary_Details.item_supplier_name = item.item_supplier_name;
                                summary_Details.original_currency_amount = item.original_currency_amount;
                                summary_Details.debit_amount = item.debit_amount;
                                summary_Details.credit_amount = item.credit_amount;
                                summary_Details.item_company_name = item.item_company_name;
                                summary_Details.item_company_code = item.item_company_code;
                                summary_Details.item_product_name = item.item_product_name;
                                summary_Details.item_product_code = item.item_product_code;
                                summary_Details.item_product_name = item.item_product_name;
                                summary_Details.item_project_code = item.item_project_code;
                                summary_Details.item_current_name = item.item_current_name;
                                summary_Details.item_current_code = item.item_current_code;
                                mC_BILL_VOUCHER_DATA.summary_details.Add(summary_Details);
                            }
                            Guid mstId = this.dGrpc.NewGuid();
                            if (!string.IsNullOrEmpty(request_MC04.voucher_no))
                            {
                                filters = new List<HttpClientExtension.IServices.Dto.Filter>();
                                filters.Add(new HttpClientExtension.IServices.Dto.Filter()
                                {
                                    DisplayType = "文本",
                                    Field = "voucher_no",
                                    Method = "eq",
                                    Mode = "eq",
                                    Type = "Text",
                                    Values = new string[] { request_MC04.voucher_no }
                                });
                                sorters = new List<HttpClientExtension.IServices.Dto.Sorter>();
                                var list = await objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 999);
                                if (list.data != null && list.totalCount > 0)
                                {
                                    mstId = list.data.Select(s => s.mstId).FirstOrDefault();
                                }

                                //数据是否接收成功
                                var issucces = await objectQuery.CreateOrUpdate<MC_BILL_VOUCHER_DATA>(mstId, mC_BILL_VOUCHER_DATA);
                                if (issucces)
                                {
                                    Dictionary<string, object> savedata = new Dictionary<string, object>();
                                    savedata["sys_status"] = "10";// 10 系统状态为: 已归档
                                    await this.objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", Guid.Parse(bill_mstid), savedata);

                                    responseResult.isSuccess = true;
                                    responseResult.msg = $"单据凭证({request_MC04.voucher_no})接收成功。";
                                }
                                else
                                {
                                    responseResult.msg = $"单据凭证({request_MC04.voucher_no})接收失败, errorCode: 1401";
                                }
                            }
                            else
                            {
                                responseResult.msg = $"单据凭证({request_MC04.voucher_no})接收失败, 凭证编号不可为空，请确认。";
                            }
                            #endregion
                        }
                        else
                        {
                            responseResult.msg = "OA流程编码在影像系统中不存在";
                        }
                    }
                    else
                    {
                        responseResult.msg = "OA流程编码不可为空";
                    }
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, ex.Message); 
                responseResult.msg = $"单据凭证({request_MC04.voucher_no})接收失败, errorCode: 1404";
            }

            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC04",
                ApiDesc = "单据费用/付款凭证接收API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC04),
                Remark = ""
            });
            #endregion
            return responseResult;
        }

        /// <summary>
        /// 单据电子回单接收API
        /// </summary>
        /// <param name="request_MC05"></param>
        /// <returns></returns>
        [HttpPost, Route("MC05")]
        public async Task<ResponseResult> MC05(Request_MC05 request_MC05)
        {
            var responseResult = new ResponseResult();
            try
            {
                var imgdata = string.Empty;
                if (!string.IsNullOrEmpty(request_MC05.img_data))
                {
                    if (!string.IsNullOrEmpty(request_MC05.img_data_filetype))
                    {
                        if (this.fileTypes.Contains(request_MC05.img_data_filetype))
                        {
                            var filetype = request_MC05.img_data_filetype;
                            imgdata = await this.multipComparisons.UploadImage(request_MC05.img_data, request_MC05.ele_receipt_no, filetype);
                        }
                        else
                        {
                            responseResult.msg = "传输的文件类型不符合要求";
                            return responseResult;
                        }
                    }
                    else
                    {
                        responseResult.msg = "文件类型不能为空";
                        return responseResult;
                    }
                }
                MC_BILL_ELE_RECEIPT_DATA mC_BILL_ELE_RECEIPT_DATA = new MC_BILL_ELE_RECEIPT_DATA()
                {
                    trades_serial_no = request_MC05.trades_serial_no,
                    trades_time = request_MC05.trades_time,
                    our_bank_no = request_MC05.our_bank_no,
                    our_bank_name = request_MC05.our_bank_name,
                    debit_credit_mark = request_MC05.debit_credit_mark,
                    opp_bank_no = request_MC05.opp_bank_no,
                    opp_bank_name = request_MC05.opp_bank_name,
                    opp_bank_deposit = request_MC05.opp_bank_deposit,
                    trades_amount = request_MC05.trades_amount,
                    currency = request_MC05.currency,
                    trades_remark = request_MC05.trades_remark,
                    ele_receipt_no = request_MC05.ele_receipt_no,
                    voucher_no = request_MC05.voucher_no,
                    oa_flow_code = request_MC05.oa_flow_code,
                    img_data = imgdata,
                    img_data_filetype = request_MC05.img_data_filetype,
                    ispost = 0
                };
                Guid mstId = this.dGrpc.NewGuid();
                if (!string.IsNullOrEmpty(request_MC05.voucher_no))
                {
                    List<HttpClientExtension.IServices.Dto.Filter> filters = new List<HttpClientExtension.IServices.Dto.Filter>();
                    filters.Add(new HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "voucher_no",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { request_MC05.voucher_no }
                    });
                    List<HttpClientExtension.IServices.Dto.Sorter> sorters = new List<HttpClientExtension.IServices.Dto.Sorter>();
                    var list = await objectQuery.GetList<MC_BILL_ELE_RECEIPT_DATA>(filters, sorters, 0, 999);
                    if (list.data != null && list.totalCount > 0)
                    {
                        mstId = list.data.Select(s => s.mstid).FirstOrDefault();
                    }

                    //数据是否接收成功
                    var issucces = await objectQuery.CreateOrUpdate<MC_BILL_ELE_RECEIPT_DATA>(mstId, mC_BILL_ELE_RECEIPT_DATA);
                    if (issucces)
                    {
                        responseResult.isSuccess = true;
                        responseResult.msg = $"单据电子回单(资金凭证编号: {request_MC05.voucher_no})接收成功。";
                    }
                    else
                    {
                        responseResult.msg = $"单据电子回单(资金凭证编号: {request_MC05.voucher_no})接收失败, errorCode: 1401";
                    }
                }
                else
                {
                    responseResult.msg = $"单据电子回单接收失败, 付款凭证编号不可空。";
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, ex.Message);
                responseResult.msg = $"单据电子回单(资金凭证编号: {request_MC05.voucher_no})接收失败, errorCode: 1404";
            }
            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC05",
                ApiDesc = "单据电子回单接收API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC05),
                Remark = ""
            });
            #endregion

            return responseResult;
        }

        /// <summary>
        /// 查询单据归档(单据信息、发票数据、发票影像、记账|付款凭证信息、电子回单信息)数据API
        /// </summary>
        /// <param name="request_MC06"></param>
        /// <returns></returns>
        [HttpPost, Route("MC06")]
        public async Task<ResponseResult> MC06(Request_MC06 request_MC06)
        {
            var responseResult = new ResponseResult();
            try
            {

                List<Filter> filters = new List<Filter>();
                filters.Add(new HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { request_MC06.bill_no }
                });

                List<Sorter> sorters = new List<Sorter>();
                //获取单据信息
                var masterlist = await objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 999);
                if (masterlist.data != null && masterlist.totalCount > 0)
                {
                    var selectBillEntity = masterlist.data.FirstOrDefault();
                    Response_BillArchiveData billArchiveData = JsonConvert.DeserializeObject<Response_BillArchiveData>(JsonConvert.SerializeObject(selectBillEntity));

                    if (billArchiveData != null)
                    {
                        //初始化数据
                        billArchiveData.invoice_items = new List<Invoice_Items>();
                        billArchiveData.voucher_items = new List<Voucher_Items>();
                        billArchiveData.ele_receipt_items = new List<Ele_Receipt_Items>();

                        //获取发票数据
                        var invoicelist = await objectQuery.GetList<MC_BILL_INVOICE_DATA>(filters, sorters, 0, 999);
                        if (invoicelist.data != null)
                        {
                            foreach (var item in invoicelist.data)
                            {
                                billArchiveData.invoice_items.Add(JsonConvert.DeserializeObject<Invoice_Items>(JsonConvert.SerializeObject(item)));
                            }
                        }

                        filters = new List<Filter>();
                        filters.Add(new HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "oa_flow_code",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { selectBillEntity.bill_no }
                        });
                        //获取付款凭证信息
                        var voucherlist = await objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 999);
                        if (voucherlist.data != null)
                        {
                            foreach (var item in voucherlist.data)
                            {
                                billArchiveData.voucher_items.Add(JsonConvert.DeserializeObject<Voucher_Items>(JsonConvert.SerializeObject(item)));
                            }
                        }
                        //获取电子回单信息
                        var receiptlist = await objectQuery.GetList<MC_BILL_ELE_RECEIPT_DATA>(filters, sorters, 0, 999);
                        if (receiptlist.data != null)
                        {
                            foreach (var item in receiptlist.data)
                            {
                                billArchiveData.ele_receipt_items.Add(JsonConvert.DeserializeObject<Ele_Receipt_Items>(JsonConvert.SerializeObject(item)));
                            }
                        }

                        responseResult.isSuccess = true;
                        responseResult.data = billArchiveData;
                    }
                    else
                    {
                        responseResult.msg = "查询单据归档数据失败。";
                    }
                }
                else
                {
                    responseResult.msg = $"查询单据归档数据失败, 单据编号({request_MC06.bill_no})未找到。";
                }
            }
            catch (Exception ex)
            {
                //_logger.LogError($"查询单据({request_MC06.bill_no})归档数据失败, 原因: {ex.Message}");
                this._logger.LogError(ex, ex.Message);
                responseResult.msg = $"查询单据({request_MC06.bill_no})归档数据失败, errorCode: 1404";
            }

            return responseResult;
        }


        /// <summary>
        /// 设置发票影像状态为已报销
        /// </summary>
        /// <param name="request_MC07"></param>
        /// <returns></returns>
        [HttpPost, Route("MC07")]
        public async Task<ResponseResult> MC07(Request_MC07 request_MC07)
        {
            var responseResult = new ResponseResult();
            try
            {
                List<string> errMstIds = new List<string>();
                foreach (var mstId in request_MC07.image_mstids)
                {
                    //发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销
                    var result =await this.multipComparisons.SetImageInvoiceStatus(mstId, request_MC07.status);
                    if (!result)
                    {
                        errMstIds.Add(mstId);
                    }
                }
                if (errMstIds == null || errMstIds.Count <= 0)
                {
                    responseResult.isSuccess = true;

                    // 将数据写入到进项税统计报表中
                    var sendResult = this.httpClient.SendInputTaxDataToReport(new Dictionary<string, object>() { 
                     ["mstId"] = request_MC07.image_mstids.ToArray()
                    });
                    if (string.IsNullOrEmpty(sendResult))
                    {
                        responseResult.msg = "推送进项税统计报表发票失败.";
                    }
                    else
                    {
                        var sendResultJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(sendResult);
                        if (sendResultJson["code"].ToString() != "1")
                        {
                            responseResult.msg = string.IsNullOrEmpty(sendResultJson["code"].ToString()) ? "推送进项税统计报表发票失败." : sendResultJson["code"].ToString();
                        }
                    }
                }
                else if (errMstIds != null && errMstIds.Count > 0 && errMstIds.Count < request_MC07.image_mstids.Count)
                {
                    responseResult.isSuccess = true;
                    responseResult.msg = "部分发票影像状态设置失败, 详见data参数值.";
                    responseResult.data = errMstIds.ToArray();
                }
                else
                {
                    responseResult.msg = "发票影像状态设置失败!";
                }
            }
            catch (Exception ex)
            {
                //_logger.LogError($"查询单据({request_MC06.bill_no})归档数据失败, 原因: {ex.Message}");
                this._logger.LogError(ex, ex.Message);
                responseResult.msg = $"设置发票影像状态为已签收失败, errorCode: 1404";
            }

            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC07",
                ApiDesc = "设置发票影像状态为已报销API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC07),
                Remark = ""
            });
            #endregion

            return responseResult;
        }

        /// <summary>
        /// 获取调用日志
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("GetLog")]
        public async Task<ResponseResult> GetLog(string apiName, string keywords, DateTime beginTime, DateTime endTime)
        {
            var responseResult = new ResponseResult();
            try
            {
                if (string.IsNullOrEmpty(apiName))
                {
                    apiName = "";
                }
                if (string.IsNullOrEmpty(keywords))
                {
                    keywords = "";
                }
                if (beginTime == DateTime.MinValue)
                {
                    beginTime = DateTime.Now.AddDays(-1);
                }
                if (endTime == DateTime.MinValue)
                {
                    endTime = DateTime.Now.AddDays(1);
                }
                responseResult.data = await this.callLogService.GetCallLog(apiName, keywords, beginTime, endTime);
                responseResult.isSuccess = true;
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, ex.Message);
                responseResult.msg = $"查询单失败, errorCode: 1404";
            }
            return responseResult;
        }
    }
}
