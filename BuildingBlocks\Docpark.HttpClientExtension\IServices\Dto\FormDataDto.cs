﻿using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class FormDataDto
    {
        public string MstId { get; set; }

        public string CreatorUserId { get; set; }

        public DateTime CreationTime { get; set; }
        public string CreatorUserName { get; set; }

        public DateTime LastModificationTime { get; set; }

        public string FormName { get; set; }

        [JsonConverter(typeof(CustomerJsonConvert<int>))]
        public int Status { get; set; }

    }
}
