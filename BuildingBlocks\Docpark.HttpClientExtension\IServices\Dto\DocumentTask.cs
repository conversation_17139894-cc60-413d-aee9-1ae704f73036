﻿
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class DocumentTask
    {

        public string Id { get; set; }

        public string TriggerName { get; set; }

        public string TriggerCode { get; set; }
        /// <summary>
        /// 文档任务ID
        /// </summary>
        public string Identity { get; set; }
        /// <summary>
        /// 文档ID
        /// </summary>
        public string DocumentIdentity { get; set; }
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowIdentity { get; set; }
        /// <summary>
        /// 文档类型ID
        /// </summary>
        public string DocumentTypeIdentity { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 文档元数据关键ID
        /// </summary>
        public string MstId { get; set; }
        /// <summary>
        /// 文档扫描用户(唯一标识)
        /// </summary>
        public string ScanUser { get; set; }
        /// <summary>
        /// 文档拥有用户(唯一标识)
        /// </summary>
        public string OwnUser { get; set; }
        /// <summary>
        /// 当前文档任务执行的最新流转ID
        /// </summary>
        public string CurrentTaskActionIdentity { get; set; }

        /// <summary>
        /// 文件类型, IMG(图片类型)/PFD/OFD
        /// </summary>
        public string FileType { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 是否删除, true: 已删除; false 未删除
        /// </summary>
        public bool IsDelete { get; set; }
        /// <summary>
        /// 最后修改人ID
        /// </summary>
        public string LastModifierUserId { get; set; }
        /// <summary>
        /// 最后修改人名称
        /// </summary>
        public string LastModifierUserName { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreatorBy { get; set; }
        /// <summary>
        /// 创建人名称
        /// </summary>
        public string CreatorByName { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 0=草稿,1=已发送消息 2. 完成 4.删除
        /// </summary>
        public int Status { get; set; }

        public string StepName { get; set; }
    }
}
