﻿using Docpark.HttpClientExtension;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    [JsonTable("Scanning")]
    /// <summary>
    /// 影像扫描业务对象
    /// </summary>
    public class BusinessDataDto:FormDataBase
    {
        
        public string BusinessType { get; set; }

        public string BillNo { get; set; }

        public string BoxNo { get; set; }

        public string BatchNo { get; set; }

        


    }
}
