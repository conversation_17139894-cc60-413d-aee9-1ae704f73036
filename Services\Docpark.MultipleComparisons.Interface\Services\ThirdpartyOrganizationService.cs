﻿using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Models.Entity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class ThirdpartyOrganizationService : IThirdpartyOrganizationService
    {
        private readonly ILogger logger;
        private readonly IComHttpClient httpClient;
        private readonly IConfiguration configuration;

        public ThirdpartyOrganizationService(ILogger<ThirdpartyOrganizationService> _logger, IComHttpClient _httpClient, IConfiguration _configuration)
        {
            this.logger = _logger;
            this.httpClient = _httpClient;
            this.configuration = _configuration;
        }

        /// <summary>
        /// 同步第三方组织机构数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <param name="errMsg"></param>
        /// <returns></returns>
        public bool SyncThirdpartyOrganization(Dictionary<string, object> requestData, out string errMsg)
        {
            bool isSuccess = false;
            errMsg = "";
            try
            {
                string url = this.configuration["thirdparty_org:org_api"];
                var resStr = this.httpClient.GetThirdpartyOrganization(url, requestData);
                if (!string.IsNullOrEmpty(resStr))
                {
                    var resJson = JsonConvert.DeserializeObject<JObject>(resStr);
                    #region 第三方组织机构信息
                    switch (resJson["returnState"].ToString())
                    {
                        case "0": // 0: 表示未操作。
                            errMsg = "调用第三方组织机构信息失败, 请重新操作。";
                            break;
                        case "1": // 1: 表示操作失败。
                            errMsg = $"调用第三方组织机构信息失败, 原因: {resJson["message"].ToString()}";
                            break;
                        case "2": // 2: 表示操作成功。
                            var orgInfos = JsonConvert.DeserializeObject<List<Model_ThirdpartyOrganization>>(resJson["message"].ToString());
                            if (orgInfos != null && orgInfos.Count > 0)
                            {
                                //处理一下组织架构的层级关系, 然后逐层同步数据
                                var orgData = GetToSaveOrgFirstData(orgInfos, "C");
                                for (int i = 0; i < orgData.Count; i++)
                                {
                                    var org = orgData[i];
                                    //同步第三方组织架构信息
                                    var strResult = this.httpClient.SyncThirdpartyOrganization(org);
                                    if (!string.IsNullOrEmpty(strResult))
                                    {
                                        var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                                        if (payload != null && Convert.ToBoolean(payload["success"]))
                                        {
                                            isSuccess = true;
                                            StringBuilder sb_ErrMsg = new StringBuilder();
                                            var errMsgResult = JsonConvert.DeserializeObject<Dictionary<string, string>>(payload["result"].ToString());
                                            foreach (var item in errMsgResult)
                                            {
                                                sb_ErrMsg.AppendLine($"{i++}. {item.Key}：{item.Value}; ");
                                            }
                                            errMsg = sb_ErrMsg.ToString();
                                            if (sb_ErrMsg.Length > 0)
                                                break;
                                        }
                                        else
                                        {
                                            errMsg = !string.IsNullOrEmpty(payload["error"].ToString()) ? ((JObject)((JObject)payload)["error"])["message"].ToString() : $"同步{(i + 1)}层级组织机构数据失败, 剩余{(orgData.Count - i)}级组织架构数据未同步！本次同步失败的机构标识如下: {string.Join("; ", org.Select(x => x.ThirdpartyId))}";
                                            break;
                                        }
                                    }
                                    else
                                    {
                                        errMsg = $"同步{(i + 1)}层级组织机构数据失败, 剩余{(orgData.Count - i)}级组织架构数据未同步！本次同步失败的机构标识如下: {string.Join("; ", org.Select(x => x.ThirdpartyId))}";
                                        break;
                                    }
                                }
                            }
                            break;
                    }
                    #endregion
                }
                else
                {
                    errMsg = $"获取第三方组织机构信息失败。";
                }
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                errMsg = $"同步第三方组织机构数据失败, errorCode: 1404";
            }

            return isSuccess;
        }

        /// <summary>
        /// 同步第三方组织用户数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <param name="errMsg"></param>
        /// <returns></returns>
        public bool SyncThirdpartyUser(Dictionary<string, object> requestData, out string errMsg)
        {
            bool isSuccess = false;
            errMsg = "";
            try
            {
                string authProvider = this.configuration["thirdparty_org:auth_provider"];
                if (string.IsNullOrEmpty(authProvider))
                {
                    errMsg = "获取第三方注册授权者[thirdparty_auth_provider]配置信息为空值！";
                    return isSuccess;
                }
                string url = this.configuration["thirdparty_org:org_api"];
                var resStr = this.httpClient.GetThirdpartyOrganization(url, requestData);
                if (!string.IsNullOrEmpty(resStr))
                {
                    var resJson = JsonConvert.DeserializeObject<JObject>(resStr);
                    #region 第三方组织机构部门信息
                    switch (resJson["returnState"].ToString())
                    {
                        case "0": // 0: 表示未操作。
                            errMsg = "调用第三方组织机构用户信息失败, 请重新操作。";
                            break;
                        case "1": // 1: 表示操作失败。
                            errMsg = $"调用第三方组织机构用户信息失败, 原因: {resJson["message"].ToString()}";
                            break;
                        case "2": // 2: 表示操作成功。
                            var userInfos = JsonConvert.DeserializeObject<List<Model_ThirdpartyUser>>(resJson["message"].ToString());
                            if (userInfos != null && userInfos.Count > 0)
                            {
                                //获取系统组织架构树结构对象
                                var orgItems = new List<OrganizationUnitDto>();
                                var orgTreeData = this.httpClient.GetOrganizationTreeData();
                                if (!string.IsNullOrEmpty(orgTreeData))
                                {
                                    var payload = JsonConvert.DeserializeObject<JObject>(orgTreeData);
                                    if (payload != null && Convert.ToBoolean(payload["success"]))
                                    {
                                        var orgResult = JsonConvert.DeserializeObject<Dictionary<string, object>>(payload["result"].ToString());

                                        if (orgResult != null && orgResult.ContainsKey("items"))
                                        {
                                            orgItems = JsonConvert.DeserializeObject<List<OrganizationUnitDto>>(orgResult["items"].ToString());
                                        }
                                        else
                                        {
                                            errMsg = "获取系统组织结构树结构数据失败！";
                                        }
                                    }
                                    else
                                    {
                                        errMsg = !string.IsNullOrEmpty(payload["error"].ToString()) ? ((JObject)((JObject)payload)["error"])["message"].ToString() : "获取系统组织结构树结构数据失败！";
                                    }
                                }
                                else
                                {
                                    errMsg = "获取系统组织结构树结构数据失败！";
                                }

                                if (orgItems != null && orgItems.Count > 0)
                                {
                                    var userItems = new List<UserDto>();
                                    var userStr = this.httpClient.GetUsers();
                                    if (!string.IsNullOrEmpty(userStr))
                                    {
                                        var payload = JsonConvert.DeserializeObject<JObject>(userStr);
                                        if (payload != null && Convert.ToBoolean(payload["success"]))
                                        {
                                            var usersResult = JsonConvert.DeserializeObject<Dictionary<string, object>>(payload["result"].ToString());

                                            if (usersResult != null && usersResult.ContainsKey("items"))
                                            {
                                                userItems = JsonConvert.DeserializeObject<List<UserDto>>(usersResult["items"].ToString());
                                            }
                                            else
                                            {
                                                errMsg = "获取系统组织结构树结构数据失败！";
                                            }
                                        }
                                        else
                                        {
                                            errMsg = !string.IsNullOrEmpty(payload["error"].ToString()) ? ((JObject)((JObject)payload)["error"])["message"].ToString() : "获取系统组织结构树结构数据失败！";
                                        }
                                    }
                                    else
                                    {
                                        errMsg = "获取系统组织结构树结构数据失败！";
                                    }
                                    List<Model_ThirdpartyUser> selectToBeCreateUsers = userInfos;
                                    if (userItems != null && userItems.Count > 0)
                                    {
                                        var selectUserNames = userItems.Select(x => x.userName);
                                        selectToBeCreateUsers = userInfos.Where(x => !selectUserNames.Contains(x.loginName)).ToList();
                                    }

                                    var rootOrg = orgItems.Where(x => x.parentId == null).FirstOrDefault();
                                    var list_SaveUser = new List<Model_ThirdpartyCreateUser>();
                                    foreach (var user in selectToBeCreateUsers)
                                    {
                                        var orgParent = rootOrg;
                                        if (!string.IsNullOrEmpty(user.parent))
                                        {
                                            orgParent = orgItems.Where(x => x.remarks == user.parent).FirstOrDefault();
                                        }
                                        list_SaveUser.Add(new Model_ThirdpartyCreateUser()
                                        {
                                            AssignedRoleNames = new string[] { },
                                            OrganizationUnits = new List<long>() {
                                                string.IsNullOrEmpty(user.parent)?rootOrg.id: orgParent != null ? orgParent.id : rootOrg.id
                                            },
                                            SendActivationEmail = false,
                                            SetRandomPassword = false,
                                            AuthProvider = authProvider, // 第三方平台提供者
                                            ProviderKey = user.id, // 第三方用户ID
                                            User = new Model_CreateUserDto()
                                            {
                                                UserName = user.loginName,
                                                Password = user.loginName,
                                                Name = user.name,
                                                EmailAddress = string.IsNullOrEmpty(user.email) ? $"{user.loginName}@test.com" : user.email,
                                                PhoneNumber = user.mobileNo,
                                                ShouldChangePasswordOnNextLogin = true,
                                                IsActive = true,
                                                IsLockoutEnabled = true,
                                                IsTwoFactorEnabled = true,
                                                Surname = "",
                                                Id = 0
                                            }
                                        });
                                    }
                                    var strResult = this.httpClient.SyncThirdpartyUser(list_SaveUser);
                                    if (!string.IsNullOrEmpty(strResult))
                                    {
                                        var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                                        if (payload != null && Convert.ToBoolean(payload["success"]))
                                        {
                                            isSuccess = true;
                                            StringBuilder sb_ErrMsg = new StringBuilder();
                                            var errMsgResult = JsonConvert.DeserializeObject<Dictionary<string, string>>(payload["result"].ToString());
                                            int i = 1;
                                            foreach (var item in errMsgResult)
                                            {
                                                sb_ErrMsg.AppendLine($"{i++}. {item.Key}：{item.Value}; ");
                                            }
                                            errMsg = sb_ErrMsg.ToString();
                                        }
                                        else
                                        {
                                            errMsg = !string.IsNullOrEmpty(payload["error"].ToString()) ? ((JObject)((JObject)payload)["error"])["message"].ToString() : "同步第三方组织机构用户信息失败！";
                                        }
                                    }
                                    else
                                    {
                                        errMsg = "同步第三方组织机构用户信息失败！";
                                    }
                                }
                            }
                            break;
                    }
                    #endregion
                }
                else
                {
                    errMsg = $"获取第三方组织机构用户信息失败。";
                }
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                errMsg = $"同步第三方组织机构用户数据失败, errorCode: 1404";
            }

            return isSuccess;
        }

        /// <summary>
        /// 获取待保存的组织机构数据
        /// </summary>
        private List<List<Model_ThirdpartyCreateOrgUnit>> GetToSaveOrgFirstData(List<Model_ThirdpartyOrganization> orgData, string orgType)
        {
            var result = new List<List<Model_ThirdpartyCreateOrgUnit>>();
            if (orgData != null && orgData.Count > 0)
            {
                var saveOrgData = orgData.Where(x => string.IsNullOrEmpty(x.parent)).ToArray();

                var orgLevelData = new List<Model_ThirdpartyCreateOrgUnit>();
                foreach (var item in saveOrgData)
                {
                    orgLevelData.Add(new Model_ThirdpartyCreateOrgUnit()
                    {
                        No = item.no,
                        ThirdpartyParentId = item.parent,
                        DisplayName = item.name,
                        ShortName = item.name,
                        ThirdpartyId = item.id,
                        Type = orgType,
                    });
                }

                result.Add(orgLevelData);

                var orgLevelDataIds = orgLevelData.Select(x => x.ThirdpartyId).ToArray();
                this.GetToSaveOrgChildrenData(orgData, orgLevelDataIds, orgType, result);

            }
            return result;
        }

        /// <summary>
        /// 获取待保存的组织机构子节点数据
        /// </summary>
        private void GetToSaveOrgChildrenData(List<Model_ThirdpartyOrganization> orgData, string[] parentIds, string orgType, List<List<Model_ThirdpartyCreateOrgUnit>> returnData)
        {
            if (parentIds != null && parentIds.Length > 0)
            {
                var orgLevelData = new List<Model_ThirdpartyCreateOrgUnit>();
                if (orgData != null && orgData.Count > 0)
                {
                    var saveOrgData = orgData.Where(x => parentIds.Contains(x.parent)).ToArray();

                    foreach (var item in saveOrgData)
                    {
                        orgLevelData.Add(new Model_ThirdpartyCreateOrgUnit()
                        {
                            No = item.no,
                            ThirdpartyParentId = item.parent,
                            DisplayName = item.name,
                            ShortName = item.name,
                            ThirdpartyId = item.id,
                            Type = orgType,
                        });
                    }
                    if (orgLevelData != null && orgLevelData.Count > 0)
                    {
                        returnData.Add(orgLevelData);

                        var orgLevelDataIds = orgLevelData.Select(x => x.ThirdpartyId).ToArray();
                        GetToSaveOrgChildrenData(orgData, orgLevelDataIds, orgType, returnData);
                    }
                }
            }
        }

    }
}
