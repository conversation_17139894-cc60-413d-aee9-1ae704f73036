﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    [JsonTable("MC_BILL_MASTER_DATA")]
    public class Request_MC03
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        [Required]
        public string bill_no { get; set; }
        /// <summary>
        /// 状态标识（01 发起人修改单据，02 发起人废弃单据，03 财务退回,04  业务领导终审通过）
        /// </summary>
        [Required]
        public string status { get; set; }
    }
}