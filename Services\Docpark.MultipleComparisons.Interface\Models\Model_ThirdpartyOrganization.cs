﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Model_ThirdpartyOrganization
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 标示该值可以作为数据存储的主健
        /// </summary>
        public string lunid { get; set; }
        /// <summary>
        /// 机构名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 组织架构类型,org(机构),dept(部门),group(群组),post(岗位),person(人员)
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string no { get; set; }
        /// <summary>
        /// 是否有效,该属性决定该组织架构是否删除
        /// </summary>
        public string isAvailable { get; set; }
        /// <summary>
        /// 父机构节点
        /// </summary>
        public string parent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isExternal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string alterTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hierarchyId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isBusiness { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string,object> langProps { get; set; }

    }
}
