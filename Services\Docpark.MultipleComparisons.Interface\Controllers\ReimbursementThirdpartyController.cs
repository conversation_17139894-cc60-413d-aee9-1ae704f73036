﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReimbursementThirdpartyController : ControllerBase
    {
        private readonly ILogger<ReimbursementThirdpartyController> _logger;
        private IDocparkMetaDataService _docparkMetaDataService { get; }
        private readonly IObjectQuery objectQuery;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IConfiguration configuration;
        private readonly IComHttpClient _httpClient;
        private readonly IDGrpc dGrpc;
        private readonly IReimbursementService _reimbursementService;
        private readonly IComHttpClient _comHttpClient;

        public ReimbursementThirdpartyController(IDocparkMetaDataService _docparkMetaDataService, ILogger<ReimbursementThirdpartyController> logger, IObjectQuery objectQuery, IThirdPartyMongoDbService callLogService, IConfiguration configuration, IDGrpc dGrpc, IReimbursementService reimbursementService, IComHttpClient httpClient, IComHttpClient comHttpClient)
        {
            _logger = logger;
            this._docparkMetaDataService = _docparkMetaDataService;
            this.objectQuery = objectQuery;
            this.callLogService = callLogService;
            this.configuration = configuration;
            this.dGrpc = dGrpc;
            _reimbursementService = reimbursementService;
            _httpClient = httpClient;
            _comHttpClient = comHttpClient;
        }

        /// <summary>
        /// 接收报销单主数据信息
        /// </summary>
        /// <param name="request_BillData"></param>
        /// <returns></returns>
        [EnableCors("FTPAPI")]
        [HttpPost]
        [Route("Bill_MC01")]
        public async Task<ResponseResult> Bill_MC01(Request_BillData request_BillData)
        {
            var responseResult = new ResponseResult();
            try
            {
                var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "tic_id",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { request_BillData.tic_id}
                });
                var selectReimList = await this.objectQuery.GetList<Reimbursement>(filter, sorters, 0, 50);

                if (selectReimList.totalCount > 0)
                {
                    responseResult.isSuccess = false;
                    responseResult.msg = $"票夹信息已提交，请勿重复提交。";
                }
                else
                {
                    var requestModel = await _comHttpClient.GetReimbursementConfig();
                    var thirdticketholderUrl = requestModel == null ? "" : requestModel.ThirdticketpriviewUrl;//_comHttpClient.GetApiHost("Reimbursement.ThirdticketpriviewUrl"); //this.configuration["thirdticketpriviewUrl"];

                    Random rd = new Random();
                    var data = rd.Next(10000, 99999);
                    Reimbursement reimbursement = new Reimbursement()
                    {
                        business_type = "日常报销",
                        tic_id = request_BillData.tic_id,
                        department = "销售部",
                        company = "上海公司",
                        totalamount = request_BillData.totalamount,
                        applicant = request_BillData.applicant,
                        application_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        state = "审批通过",
                        wallet_path = thirdticketholderUrl + $"#/img/invoice/detail?folderId={request_BillData.tic_id}&folderName={request_BillData.name}",
                        ArchivesKey = DateTime.Now.ToString("yyyyMMddHHmm") + data
                    };
                    Guid mstId = this.dGrpc.NewGuid();
                    //数据是否接收成功
                    var issucces = await objectQuery.CreateOrUpdate<Reimbursement>(mstId, reimbursement);
                    if (issucces)
                    {
                        //修改票夹状态Status   0：草稿   1：提交中   2：提交
                        //var requestModel = await _comHttpClient.GetReimbursementConfig();
                        var thirdticketrUrlSStatus = requestModel == null ? "" : requestModel.ThirdticketholderUrl; //_comHttpClient.GetApiHost("Reimbursement.ThirdticketholderUrl"); //this.configuration["thirdticketholderUrl"];
                        Dictionary<string, object> dic = new Dictionary<string, object>()
                        {
                            ["Id"] = request_BillData.tic_id,
                            ["Name"] = request_BillData.name,
                            ["Owner"] = 2,
                            ["Status"] = 1
                        };

                        var requestData = _httpClient.Post(thirdticketrUrlSStatus + $"invoice/folder/SetStatus", dic);

                        responseResult.isSuccess = true;
                        responseResult.msg = $"报销单接收成功。";
                    }
                    else
                    {
                        responseResult.msg = $"报销单接收失败, errorCode: 1401";
                    }
                }
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, "Bill_MC01:" + ex.Message);
                responseResult.msg = $"报销单接收失败, errorCode: 1404";
            }
            #region 添加调用日志
            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "Bill_MC01",
                ApiDesc = "报销单接收API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_BillData),
                Remark = ""
            });
            #endregion

            return responseResult;
        }

        /// <summary>
        /// 获取报销单数据
        /// </summary>
        /// <returns></returns>
        [EnableCors("FTPAPI")]
        [HttpGet]
        [Route("Get_BillData")]
        public async Task<ResponseResult> Get_BillData(int pageIndex, int pageSize)
        {
            var responseResult = new ResponseResult();
            //筛选条件     待处理
            var filter = new List<Filter>();
            //排序
            List<Sorter> sorters = new List<Sorter>();
            sorters.Add(new Sorter()
            {
                Field = "application_time",
                Order = "descend"
            });
            if (pageIndex < 0)
            {
                pageIndex = 0;
            }
            if (pageSize <= 0)
            {
                pageSize = 10;
            }


            //获取单据主数据信息
            var listmaster = await objectQuery.GetList<Reimbursement>(filter, sorters, pageIndex, pageSize);

            if (listmaster.data.Count > 0 && listmaster.data != null)
            {
                responseResult.isSuccess = true;
                responseResult.data = listmaster.data;
                responseResult.msg = "查询成功";
                responseResult.count = listmaster.totalCount;
            }
            else
            {
                responseResult.isSuccess = false;
                responseResult.data = null;
                responseResult.msg = "列表无数据";
                responseResult.count = listmaster.totalCount;
            }
            return responseResult;
        }

        /// <summary>
        /// 推送智能比对数据
        /// </summary>
        /// <param name="request_Bill_No"></param>
        /// <returns></returns>
        [EnableCors("FTPAPI")]
        [Route("SendDataTo_MC01")]
        [HttpPost]
        public async Task<ResponseResult> SendDataTo_MC01(Request_Bill_nos request_Bill_Nos)
        {
            ResponseResult responseResult = new ResponseResult();
            try
            {
                var list = await _reimbursementService.SendDataTo_MC01(request_Bill_Nos.bill_nos);
                if (list.result)
                {
                    responseResult.isSuccess = true;
                    responseResult.msg = list.msg;
                }
                else
                {
                    responseResult.isSuccess = false;
                    responseResult.msg = list.msg;
                }
            }
            catch (Exception ex)
            {
                responseResult.isSuccess = false;
                responseResult.msg = ex.Message;
                this._logger.LogError(ex, "SendDataTo_MC01:" + ex.StackTrace.ToString());
            }
            return responseResult;
        }

        /// <summary>
        /// 已签收后修改报销单状态
        /// </summary>
        /// <param name="request_Bill_Nos"></param>
        /// <returns></returns>
        [EnableCors("FTPAPI")]
        [Route("SuccessDataToReim")]
        [HttpPost]
        public async Task<ResponseResult> SuccessDataToReim(Request_Bill_nos request_Bill_Nos)
        {
            ResponseResult responseResult = new ResponseResult();
            try
            {
                var result = await this._reimbursementService.SuccessDataToReim(request_Bill_Nos.bill_nos);
                if (result)
                {
                    responseResult.isSuccess = true;
                    responseResult.msg = "设置报销单状态完成";
                }
                else
                {
                    responseResult.isSuccess = false;
                    responseResult.msg = "设置报销单状态失败";
                }
            }
            catch (Exception ex)
            {
                responseResult.isSuccess = false;
                responseResult.msg = ex.Message;
                this._logger.LogError(ex, "SuccessDataToReim:" + ex.StackTrace.ToString());
            }
            return responseResult;
        }
    }
}
