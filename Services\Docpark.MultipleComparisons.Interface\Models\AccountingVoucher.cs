﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Docpark.HttpClientExtension.Json;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 记账凭证
    /// </summary>
    [JsonTable("AccountingVoucher")]
    public class AccountingVoucher
    {
        public Guid MstId { get; set; }

        /// <summary>
        /// 编制单位
        /// </summary>
        public string PreparationUnit { get; set; }

        /// <summary>
        /// 核算单位
        /// </summary>
        public string AccountingUnit { get; set; }

        /// <summary>
        /// 公司编码
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 会计年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 会计期间
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 凭证号
        /// </summary>
        public string VoucherNo { get; set; }

        /// <summary>
        /// 记账
        /// </summary>
        public string Bookkeeping { get; set; }

        /// <summary>
        /// 复核
        /// </summary>
        public string Reviewer { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Maker { get; set; }
        /// <summary>
        /// 明细列表
        /// </summary>
        public List<VoucherDetail> VoucherDetail { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 唯一标识
        /// </summary>
        public string ArchivesKey { get; set; }

        /// <summary>
        /// 保管年限
        /// </summary>
        public string KeepYear { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDateTime { get; set; }

        /// <summary>
        /// 附件数
        /// </summary>
        public int AnnexNumber { get; set; }

        /// <summary>
        /// 公开
        /// </summary>
        public string SecretLevel { get; set; }
    }

    /// <summary>
    /// 凭证明细
    /// </summary>
    [JsonTable("VoucherDetail")]
    public class VoucherDetail
    {
        /// <summary>
        /// 摘要
        /// </summary>
        public string Summary { get; set; }

        /// <summary>
        /// 会计科目
        /// </summary>
        public string AccountingSubjects { get; set; }

        /// <summary>
        /// 借方
        /// </summary>
        public string Debit { get; set; }

        /// <summary>
        /// 贷方
        /// </summary>
        public string Lender { get; set; }
    }
}
