﻿using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.EventBus.continental.Dto
{
    public class CompanyInfo
    {
        public string TaxNo { get; set; }

        /// <summary>
        /// SAP系统编号
        /// </summary>
        public string SAP_BUKRS { get; set; }

        /// <summary>
        /// 公司编号
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 工厂编号
        /// </summary>
        public string PlantInvoice { get; set; }

        public List<PoInfo> PoList { get; set; }
    }

    public class PoInfo
    {
        public string Type { get; set; }

        public string PO { get; set; }

        public string POLength { get; set; }

    }
}
