﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Runtime.InteropServices;
using System.Text;
using SharpCompress.Common;
using SharpCompress.Readers;
using SharpCompress.Writers;

namespace DocPark.Commons
{
    public class FileHelper
    {
        #region 字节、字符串操作
        /// <summary>
        /// 将字符串压缩成Base64字符串
        /// </summary>
        /// <param name="input"></param> 
        /// <returns></returns>
        public static string Compress(string input)
        {
            byte[] inputBytes = Encoding.Default.GetBytes(input);
            byte[] result = Compress(inputBytes);
            return Convert.ToBase64String(result);
        }

        /// <summary>
        /// 将Base64字符串解压缩成字符串
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Decompress(string input)
        {
            byte[] inputBytes = Convert.FromBase64String(input);
            byte[] depressBytes = Decompress(inputBytes);
            return Encoding.Default.GetString(depressBytes);
        }

        /// <summary>
        /// 压缩字节数组
        /// </summary>
        /// <param name="inputBytes"></param>
        public static byte[] Compress(byte[] inputBytes)
        {
            using (MemoryStream outStream = new MemoryStream())
            {
                using (GZipStream zipStream = new GZipStream(outStream, CompressionMode.Compress, true))
                {
                    zipStream.Write(inputBytes, 0, inputBytes.Length);
                    zipStream.Close(); //很重要，必须关闭，否则无法正确解压
                    return outStream.ToArray();
                }
            }
        }

        /// <summary>
        /// 解压缩字节数组
        /// </summary>
        /// <param name="inputBytes"></param>
        public static byte[] Decompress(byte[] inputBytes)
        {
            using (MemoryStream inputStream = new MemoryStream(inputBytes))
            {
                using (MemoryStream outStream = new MemoryStream())
                {
                    using (GZipStream zipStream = new GZipStream(inputStream, CompressionMode.Decompress))
                    {
                        zipStream.CopyTo(outStream);
                        zipStream.Close();
                        return outStream.ToArray();
                    }
                }
            }
        }
        #endregion

        #region 文件操作
        /// <summary>
        /// 将byte数组转换为文件并保存到指定地址
        /// </summary>
        /// <param name="buff">byte数组</param>
        /// <param name="savePath">保存地址</param>
        public static void Bytes2File(byte[] buff, string savePath)
        {
            if (System.IO.File.Exists(savePath))
            {
                System.IO.File.Delete(savePath);
            }

            FileStream fs = new FileStream(savePath, FileMode.CreateNew);
            BinaryWriter bw = new BinaryWriter(fs);
            bw.Write(buff, 0, buff.Length);
            bw.Close();
            fs.Close();
        }

        /// <summary>
        /// 将文件转换为byte数组
        /// </summary>
        /// <param name="filePath">文件地址</param>
        /// <returns>转换后的byte数组</returns>
        public static byte[] File2Bytes(string filePath)
        {
            if (!System.IO.File.Exists(filePath))
            {
                return new byte[0];
            }

            FileInfo fi = new FileInfo(filePath);
            byte[] buff = new byte[fi.Length];


            FileStream fs = fi.OpenRead();
            fs.Read(buff, 0, Convert.ToInt32(fs.Length));
            fs.Close();


            return buff;
        }
        #endregion

        #region 文件夹操作
        /// <summary>  
        /// 获取所有文件  
        /// </summary>  
        /// <returns></returns>  
        public static Hashtable GetAllFiles(string dir)
        {
            Hashtable filesList = new Hashtable();
            DirectoryInfo fileDire = new DirectoryInfo(dir);
            if (fileDire.Exists)
            {
                FileInfo[] files = fileDire.GetFiles("*.*", SearchOption.AllDirectories);
                foreach (FileInfo file in files)
                {
                    filesList.Add(file.FullName, file.LastWriteTime);
                }
            }

            return filesList;
        }

        /// <summary>
        /// 删除所有文件
        /// </summary>
        /// <param name="dir"></param>
        public static void DelAllFiles(string dir)
        {
            DirectoryInfo fileDire = new DirectoryInfo(dir);
            if (fileDire.Exists)
            {
                FileInfo[] files = fileDire.GetFiles("*.*", SearchOption.AllDirectories);
                foreach (FileInfo file in files)
                {
                    File.Delete(file.FullName);
                }
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath"></param>
        public static void DelFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }
        #endregion

        #region 根据系统生成文件路径
        public static string GetRuntimeDirectory(string path)
        {
            if (IsLinuxRunTime())
                return GetLinuxDirectory(path);
            if (IsWindowRunTime())
                return GetWindowDirectory(path);
            return path;
        }

        public static bool IsWindowRunTime()
        {
            return System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
        }

        public static bool IsLinuxRunTime()
        {
            return System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Linux);
        }

        public static string GetLinuxDirectory(string path)
        {
            string pathTemp = Path.Combine(path);
            return pathTemp.Replace("\\", "/");
        }
        public static string GetWindowDirectory(string path)
        {
            string pathTemp = Path.Combine(path);
            return pathTemp.Replace("/", "\\");
        }
        #endregion

        #region 解压、压缩
        /// <summary>
        /// 压缩文件
        /// </summary>
        /// <param name="sourceFilePath">原文件的路径</param>
        /// <param name="zipPath">压缩后的文件路径</param>
        public static void Compress(string sourceFilePath, string zipPath)
        {
            using (Stream stream = File.OpenWrite(Path.GetExtension(zipPath).ToLower() == ".zip" ? zipPath : zipPath + ".zip"))
            {
                var writerOptions = new WriterOptions(CompressionType.BZip2);
                writerOptions.ArchiveEncoding.Default = Encoding.GetEncoding("GBK");

                using (var writer = WriterFactory.Open(stream, ArchiveType.Zip, writerOptions))
                {
                    writer.WriteAll(sourceFilePath, "*", SearchOption.AllDirectories);
                }
            }
        }

        /// <summary>
        /// 将一系列文件压缩到指定ZIP文件 
        /// </summary>
        /// <param name="folderOrFileList"></param>
        /// <param name="zipPath"></param>
        public static void Compress(List<string> folderOrFileList, string zipPath)
        {
            using (Stream stream = File.OpenWrite(Path.GetExtension(zipPath).ToLower() == ".zip" ? zipPath : zipPath + ".zip"))
            {
                var writerOptions = new WriterOptions(CompressionType.BZip2);
                writerOptions.ArchiveEncoding.Default = Encoding.GetEncoding("GBK");

                using (var writer = WriterFactory.Open(stream, ArchiveType.Zip, writerOptions))
                {
                    foreach (var filePath in folderOrFileList)
                    {
                        if (System.IO.Directory.Exists(filePath))
                        {
                            writer.WriteAll(filePath, "*", SearchOption.AllDirectories);
                        }
                        else if (System.IO.File.Exists(filePath))
                        {
                            writer.Write(Path.GetFileName(filePath), filePath);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 将一系列文件压缩到指定ZIP文件 
        /// </summary>
        /// <param name="filePaths"></param>
        public static MemoryStream Compress(List<string> filePaths)
        {
            var stream = new MemoryStream();
            var writerOptions = new WriterOptions(CompressionType.BZip2);
            writerOptions.ArchiveEncoding.Default = Encoding.GetEncoding("GBK");
            using (var writer = WriterFactory.Open(stream, ArchiveType.Zip, writerOptions))
            {
                foreach (var filePath in filePaths)
                {
                    if (System.IO.Directory.Exists(filePath))
                    {
                        writer.WriteAll(filePath, "*", SearchOption.AllDirectories);
                    }
                    else if (System.IO.File.Exists(filePath))
                    {
                        writer.Write(Path.GetFileName(filePath), filePath);
                    }
                }
            }
            return stream;
        }

        /// <summary>
        /// 解压文件(支持格式:None,GZip,BZip2,PPMd,Deflate,Rar,LZMA,BCJ,BCJ2,LZip,Xz,Unknown,Deflate64)
        /// </summary>
        /// <param name="zipPath">压缩文件的路径</param>
        /// <param name="extractPath">解压之后的文件路径</param>
        public static void UnCompress(string zipPath, string extractPath)
        {
            using (Stream stream = File.OpenRead(zipPath))
            {
                var readerOptions = new ReaderOptions();
                readerOptions.ArchiveEncoding.Default = Encoding.GetEncoding("GBK");
                var reader = ReaderFactory.Open(stream, readerOptions);
                while (reader.MoveToNextEntry())
                {
                    if (!reader.Entry.IsDirectory)
                    {
                        SharpCompress.Common.ExtractionOptions option = new ExtractionOptions() { Overwrite = true, ExtractFullPath = true };
                        reader.WriteEntryToDirectory(extractPath, option);
                    }
                }
            }
        }
        #endregion
    }
}
