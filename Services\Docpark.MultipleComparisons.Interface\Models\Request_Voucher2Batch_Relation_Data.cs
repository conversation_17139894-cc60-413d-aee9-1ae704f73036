﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_Voucher2Batch_Relation_Data
    {
        /// <summary>
        /// 凭证Id<br/>
        /// 用户系统凭证唯一编码。有则传入，无则不传入，数据以 业务实体名，凭证类型，凭证期间，和凭证号 四要素组合确定唯一
        /// </summary>
        public string voucherId { get; set; }

        /// <summary>
        /// 业务实体名称(公司名称)
        /// </summary>
        public string businessEntityName { get; set; }

        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucherType { get; set; }

        /// <summary>
        /// 凭证期间
        /// </summary>
        public string voucherPeriod { get; set; }

        /// <summary>
        /// 凭证号
        /// </summary>
        public string voucherNo { get; set; }

        /// <summary>
        /// 流水号列表<br/>
        /// 关联流水号数组，跟details二选一，该字段有值时details不生效
        /// </summary>
        public string[] transNos { get; set; }

        /// <summary>
        /// 关联流水要素列表
        /// </summary>
        public List<Voucher2Batch_Details> details { get; set; }
    }

    /// <summary>
    /// 发票
    /// </summary>
    public class Voucher2Batch_Details
    {
        /// <summary>
        /// 流水号
        /// </summary>
        public string transNo { get; set; }
        /// <summary>
        /// 我方银行账号
        /// </summary>
        public string accountNo { get; set; }
        /// <summary>
        /// 对方银行账号
        /// </summary>
        public string opAccountNo { get; set; }
        /// <summary>
        /// 交易日期
        /// </summary>
        public string transDate { get; set; }
        /// <summary>
        /// 借方金额
        /// </summary>
        public string transAmtD { get; set; }
        /// <summary>
        /// 贷方金额
        /// </summary>
        public string transAmtC { get; set; }

    }
}
