using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 数据映射服务实现
    /// </summary>
    public class DataMappingService : IDataMappingService
    {
        private readonly ILogger<DataMappingService> _logger;

        public DataMappingService(ILogger<DataMappingService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 映射响应数据
        /// </summary>
        public async Task<object> MapResponseDataAsync(object responseData, DataMappingConfig mappingConfig)
        {
            if (mappingConfig == null || !mappingConfig.IsEnabled)
            {
                return responseData;
            }

            try
            {
                var startTime = DateTime.UtcNow;
                _logger.LogInformation("开始数据映射处理");

                // 将响应数据转换为JToken
                JToken sourceData;
                if (responseData is string jsonString)
                {
                    sourceData = JToken.Parse(jsonString);
                }
                else
                {
                    var json = JsonConvert.SerializeObject(responseData);
                    sourceData = JToken.Parse(json);
                }

                // 如果指定了根路径，先提取根数据
                if (!string.IsNullOrEmpty(mappingConfig.RootPath))
                {
                    sourceData = await ExtractDataByPathAsync(sourceData, mappingConfig.RootPath);
                }

                // 应用映射规则
                var mappedData = await ApplyMappingRulesAsync(sourceData, mappingConfig.Rules);

                // 构建最终结果
                var result = new Dictionary<string, object>();

                if (mappingConfig.KeepOriginalData)
                {
                    result["original_data"] = responseData;
                }

                result[mappingConfig.MappedDataName] = mappedData;

                var endTime = DateTime.UtcNow;
                _logger.LogInformation($"数据映射处理完成，耗时: {(endTime - startTime).TotalMilliseconds}ms");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据映射处理失败");
                throw;
            }
        }

        /// <summary>
        /// 验证映射配置
        /// </summary>
        public async Task<ValidationResult> ValidateMappingConfigAsync(DataMappingConfig mappingConfig)
        {
            var result = new ValidationResult { IsValid = true };

            if (mappingConfig == null)
            {
                result.IsValid = false;
                result.Errors.Add("映射配置不能为空");
                return result;
            }

            // 验证映射规则
            if (mappingConfig.Rules == null || !mappingConfig.Rules.Any())
            {
                result.Warnings.Add("未配置映射规则");
            }
            else
            {
                foreach (var rule in mappingConfig.Rules)
                {
                    await ValidateMappingRule(rule, result);
                }
            }

            // 验证根路径
            if (!string.IsNullOrEmpty(mappingConfig.RootPath))
            {
                if (!IsValidJsonPath(mappingConfig.RootPath))
                {
                    result.IsValid = false;
                    result.Errors.Add($"根路径格式无效: {mappingConfig.RootPath}");
                }
            }

            return result;
        }

        /// <summary>
        /// 应用映射规则
        /// </summary>
        public async Task<Dictionary<string, object>> ApplyMappingRulesAsync(JToken sourceData, List<MappingRule> rules)
        {
            var result = new Dictionary<string, object>();

            if (rules == null || !rules.Any())
            {
                return result;
            }

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    await ApplySingleMappingRule(sourceData, rule, result);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"映射规则应用失败: {rule.SourcePath} -> {rule.TargetField}");
                    
                    // 如果是必需字段，使用默认值或抛出异常
                    if (rule.IsRequired)
                    {
                        if (!string.IsNullOrEmpty(rule.DefaultValue))
                        {
                            result[rule.TargetField] = rule.DefaultValue;
                        }
                        else
                        {
                            throw new InvalidOperationException($"必需字段 {rule.TargetField} 映射失败且无默认值");
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 转换数据类型
        /// </summary>
        public async Task<object> TransformDataAsync(object value, DataTransformType transformType, string parameter = null)
        {
            if (value == null)
            {
                return null;
            }

            try
            {
                switch (transformType)
                {
                    case DataTransformType.None:
                        return value;

                    case DataTransformType.ToString:
                        return value.ToString();

                    case DataTransformType.ToNumber:
                        return ConvertToNumber(value);

                    case DataTransformType.ToDateTime:
                        return ConvertToDateTime(value, parameter);

                    case DataTransformType.ToBoolean:
                        return ConvertToBoolean(value);

                    case DataTransformType.JsonToObject:
                        return JsonConvert.DeserializeObject(value.ToString());

                    case DataTransformType.ObjectToJson:
                        return JsonConvert.SerializeObject(value);

                    case DataTransformType.RegexExtract:
                        return ExtractByRegex(value.ToString(), parameter);

                    case DataTransformType.StringReplace:
                        return ReplaceString(value.ToString(), parameter);

                    case DataTransformType.StringSplit:
                        return SplitString(value.ToString(), parameter);

                    case DataTransformType.ArrayJoin:
                        return JoinArray(value, parameter);

                    case DataTransformType.Custom:
                        return await ExecuteCustomTransform(value, parameter);

                    default:
                        return value;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"数据转换失败: {transformType}, 值: {value}");
                return value;
            }
        }

        /// <summary>
        /// 提取嵌套数据
        /// </summary>
        public async Task<JToken> ExtractDataByPathAsync(JToken data, string path)
        {
            if (data == null || string.IsNullOrEmpty(path))
            {
                return data;
            }

            try
            {
                return data.SelectToken(path);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"路径提取失败: {path}");
                return null;
            }
        }

        /// <summary>
        /// 生成映射预览
        /// </summary>
        public async Task<MappingPreviewResult> GenerateMappingPreviewAsync(object sampleData, DataMappingConfig mappingConfig)
        {
            var startTime = DateTime.UtcNow;
            var result = new MappingPreviewResult
            {
                OriginalData = sampleData,
                Statistics = new MappingStatistics()
            };

            try
            {
                var mappedData = await MapResponseDataAsync(sampleData, mappingConfig);
                result.MappedData = mappedData;

                // 计算统计信息
                result.Statistics.TotalFields = mappingConfig.Rules?.Count ?? 0;
                result.Statistics.ProcessingTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

                // TODO: 计算更详细的统计信息
            }
            catch (Exception ex)
            {
                result.Errors.Add(new MappingError
                {
                    ErrorType = MappingErrorType.Unknown,
                    Message = ex.Message,
                    ExceptionDetails = ex.ToString()
                });
            }

            return result;
        }

        #region 私有方法

        private async Task ValidateMappingRule(MappingRule rule, ValidationResult result)
        {
            if (string.IsNullOrEmpty(rule.SourcePath))
            {
                result.IsValid = false;
                result.Errors.Add("源路径不能为空");
            }

            if (string.IsNullOrEmpty(rule.TargetField))
            {
                result.IsValid = false;
                result.Errors.Add("目标字段不能为空");
            }

            if (!string.IsNullOrEmpty(rule.ValidationPattern))
            {
                try
                {
                    new Regex(rule.ValidationPattern);
                }
                catch
                {
                    result.IsValid = false;
                    result.Errors.Add($"验证规则正则表达式无效: {rule.ValidationPattern}");
                }
            }
        }

        private async Task ApplySingleMappingRule(JToken sourceData, MappingRule rule, Dictionary<string, object> result)
        {
            // 提取源数据
            var extractedValue = await ExtractDataByPathAsync(sourceData, rule.SourcePath);
            
            object finalValue = null;

            if (extractedValue != null)
            {
                // 应用数据转换
                finalValue = await TransformDataAsync(extractedValue.ToObject<object>(), rule.TransformType, rule.TransformParameter);

                // 验证数据
                if (!string.IsNullOrEmpty(rule.ValidationPattern) && finalValue != null)
                {
                    if (!Regex.IsMatch(finalValue.ToString(), rule.ValidationPattern))
                    {
                        if (rule.IsRequired)
                        {
                            throw new InvalidOperationException($"字段 {rule.TargetField} 验证失败");
                        }
                        finalValue = null;
                    }
                }
            }

            // 如果值为空且有默认值，使用默认值
            if (finalValue == null && !string.IsNullOrEmpty(rule.DefaultValue))
            {
                finalValue = rule.DefaultValue;
            }

            // 如果是必需字段但值为空，抛出异常
            if (finalValue == null && rule.IsRequired)
            {
                throw new InvalidOperationException($"必需字段 {rule.TargetField} 值为空");
            }

            if (finalValue != null)
            {
                result[rule.TargetField] = finalValue;
            }
        }

        private bool IsValidJsonPath(string path)
        {
            try
            {
                // 简单的JSON路径验证
                return !string.IsNullOrEmpty(path) && (path.StartsWith("$") || path.StartsWith(".") || path.Contains("["));
            }
            catch
            {
                return false;
            }
        }

        private object ConvertToNumber(object value)
        {
            if (value is string str)
            {
                if (double.TryParse(str, out double doubleResult))
                {
                    return doubleResult;
                }
            }
            
            return Convert.ToDouble(value);
        }

        private DateTime ConvertToDateTime(object value, string format = null)
        {
            if (value is string str)
            {
                if (!string.IsNullOrEmpty(format))
                {
                    return DateTime.ParseExact(str, format, CultureInfo.InvariantCulture);
                }
                return DateTime.Parse(str);
            }
            
            return Convert.ToDateTime(value);
        }

        private bool ConvertToBoolean(object value)
        {
            if (value is string str)
            {
                return str.ToLower() == "true" || str == "1" || str.ToLower() == "yes";
            }
            
            return Convert.ToBoolean(value);
        }

        private string ExtractByRegex(string input, string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
            {
                return input;
            }

            var match = Regex.Match(input, pattern);
            return match.Success ? match.Value : input;
        }

        private string ReplaceString(string input, string parameter)
        {
            if (string.IsNullOrEmpty(parameter))
            {
                return input;
            }

            var parts = parameter.Split('|');
            if (parts.Length == 2)
            {
                return input.Replace(parts[0], parts[1]);
            }

            return input;
        }

        private string[] SplitString(string input, string delimiter)
        {
            if (string.IsNullOrEmpty(delimiter))
            {
                delimiter = ",";
            }

            return input.Split(new[] { delimiter }, StringSplitOptions.RemoveEmptyEntries);
        }

        private string JoinArray(object value, string delimiter)
        {
            if (string.IsNullOrEmpty(delimiter))
            {
                delimiter = ",";
            }

            if (value is JArray array)
            {
                return string.Join(delimiter, array.Select(x => x.ToString()));
            }

            if (value is IEnumerable<object> enumerable)
            {
                return string.Join(delimiter, enumerable.Select(x => x.ToString()));
            }

            return value.ToString();
        }

        private async Task<object> ExecuteCustomTransform(object value, string script)
        {
            // TODO: 实现自定义脚本执行（可以使用C# Script或其他脚本引擎）
            _logger.LogWarning("自定义转换功能尚未实现");
            return value;
        }

        #endregion
    }
}
