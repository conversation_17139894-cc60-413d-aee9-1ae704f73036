﻿using Docpark.ThirdMaster.Services.Models.EmailCollection;
using Docpark.ThirdMaster.Services.Models.MongoEntities;
using Docpark.HttpClientExtension.IServices.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.EmailCollection
{
    public interface IEmailCollectionService
    {
        /// <summary>
        /// 获取供应商邮箱
        /// </summary>
        /// <returns></returns>
        Task<List<string>> GetSupplierEmails();
        /// <summary>
        /// 获取邮箱配置
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<ReceiveEmailInfo> GetReceiveEmailInfo(Enum_Collect_Type type);
        /// <summary>
        /// 收取邮件
        /// </summary>
        /// <returns></returns>
        Task ReceiveEmails(Enum_Collect_Type type);
        /// <summary>
        /// 发票签收
        /// </summary>
        /// <returns></returns>
        Task<ResultDto> MailSignIn();
    }
}
