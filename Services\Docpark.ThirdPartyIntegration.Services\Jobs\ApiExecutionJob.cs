using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;

namespace Docpark.ThirdPartyIntegration.Services.Jobs
{
    /// <summary>
    /// API执行任务
    /// </summary>
    [DisallowConcurrentExecution] // 默认不允许并发执行
    public class ApiExecutionJob : IJob
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ApiExecutionJob> _logger;

        public ApiExecutionJob(IServiceProvider serviceProvider, ILogger<ApiExecutionJob> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var jobKey = context.JobDetail.Key.ToString();
            var apiConfigId = context.JobDetail.JobDataMap.GetString("ApiConfigId");
            var startTime = DateTime.UtcNow;

            _logger.LogInformation("开始执行定时任务，任务键: {JobKey}, API配置ID: {ApiConfigId}", jobKey, apiConfigId);

            try
            {
                // 创建服务作用域
                using var scope = _serviceProvider.CreateScope();
                var apiExecutionService = scope.ServiceProvider.GetRequiredService<IApiExecutionService>();
                var mongoRepository = scope.ServiceProvider.GetRequiredService<IMongoRepository>();

                // 记录任务执行开始
                var executionHistory = new JobExecutionHistory
                {
                    Id = Guid.NewGuid().ToString(),
                    JobKey = jobKey,
                    ApiConfigId = apiConfigId,
                    StartTime = startTime,
                    Status = JobExecutionStatus.Running,
                    TriggerType = context.Trigger.GetType().Name,
                    IsRetry = context.RefireCount > 0,
                    RetryCount = context.RefireCount
                };

                await SaveExecutionHistoryAsync(mongoRepository, executionHistory);

                // 执行API调用
                var result = await apiExecutionService.ExecuteApiAsync(apiConfigId);

                // 更新执行历史
                executionHistory.EndTime = DateTime.UtcNow;
                executionHistory.DurationMs = (long)(executionHistory.EndTime.Value - executionHistory.StartTime).TotalMilliseconds;
                executionHistory.Status = result.IsSuccess ? JobExecutionStatus.Success : JobExecutionStatus.Failed;
                executionHistory.Result = result.ResponseData;

                if (!result.IsSuccess)
                {
                    executionHistory.ErrorMessage = result.ErrorMessage;
                }

                await SaveExecutionHistoryAsync(mongoRepository, executionHistory);

                // 更新任务数据
                await UpdateJobDataAsync(context, result.IsSuccess);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("定时任务执行成功，任务键: {JobKey}, API配置ID: {ApiConfigId}, 耗时: {Duration}ms", 
                        jobKey, apiConfigId, executionHistory.DurationMs);
                }
                else
                {
                    _logger.LogError("定时任务执行失败，任务键: {JobKey}, API配置ID: {ApiConfigId}, 错误: {Error}", 
                        jobKey, apiConfigId, result.ErrorMessage);

                    // 如果配置了重试，抛出异常触发重试
                    var retryCount = context.JobDetail.JobDataMap.GetIntValue("RetryCount");
                    if (context.RefireCount < retryCount)
                    {
                        throw new JobExecutionException($"API执行失败: {result.ErrorMessage}") { RefireImmediately = true };
                    }
                }
            }
            catch (JobExecutionException)
            {
                // 重新抛出JobExecutionException以触发重试机制
                throw;
            }
            catch (Exception ex)
            {
                var endTime = DateTime.UtcNow;
                var duration = (long)(endTime - startTime).TotalMilliseconds;

                _logger.LogError(ex, "定时任务执行异常，任务键: {JobKey}, API配置ID: {ApiConfigId}, 耗时: {Duration}ms", 
                    jobKey, apiConfigId, duration);

                // 记录异常执行历史
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var mongoRepository = scope.ServiceProvider.GetRequiredService<IMongoRepository>();

                    var executionHistory = new JobExecutionHistory
                    {
                        Id = Guid.NewGuid().ToString(),
                        JobKey = jobKey,
                        ApiConfigId = apiConfigId,
                        StartTime = startTime,
                        EndTime = endTime,
                        DurationMs = duration,
                        Status = JobExecutionStatus.Failed,
                        ErrorMessage = ex.Message,
                        ExceptionDetails = ex.ToString(),
                        TriggerType = context.Trigger.GetType().Name,
                        IsRetry = context.RefireCount > 0,
                        RetryCount = context.RefireCount
                    };

                    await SaveExecutionHistoryAsync(mongoRepository, executionHistory);
                }
                catch (Exception saveEx)
                {
                    _logger.LogError(saveEx, "保存任务执行历史失败");
                }

                // 检查是否需要重试
                var retryCount = context.JobDetail.JobDataMap.GetIntValue("RetryCount");
                if (context.RefireCount < retryCount)
                {
                    throw new JobExecutionException($"任务执行异常: {ex.Message}", ex) { RefireImmediately = true };
                }

                throw;
            }
        }

        private async Task SaveExecutionHistoryAsync(IMongoRepository mongoRepository, JobExecutionHistory history)
        {
            try
            {
                var collection = mongoRepository.GetCollection<JobExecutionHistory>("job_execution_history");
                await collection.InsertOneAsync(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存任务执行历史失败，任务键: {JobKey}", history.JobKey);
            }
        }

        private async Task UpdateJobDataAsync(IJobExecutionContext context, bool isSuccess)
        {
            try
            {
                var jobDataMap = context.JobDetail.JobDataMap;
                var executionCount = jobDataMap.GetIntValue("ExecutionCount") + 1;
                jobDataMap.Put("ExecutionCount", executionCount);
                jobDataMap.Put("LastExecutionTime", DateTime.UtcNow);
                jobDataMap.Put("LastExecutionSuccess", isSuccess);

                if (isSuccess)
                {
                    var successCount = jobDataMap.GetIntValue("SuccessCount") + 1;
                    jobDataMap.Put("SuccessCount", successCount);
                }
                else
                {
                    var failureCount = jobDataMap.GetIntValue("FailureCount") + 1;
                    jobDataMap.Put("FailureCount", failureCount);
                }

                // 检查是否达到最大执行次数
                var maxExecutionCount = jobDataMap.GetIntValue("MaxExecutionCount");
                if (maxExecutionCount > 0 && executionCount >= maxExecutionCount)
                {
                    _logger.LogInformation("任务已达到最大执行次数，将被删除，任务键: {JobKey}", context.JobDetail.Key);
                    
                    // 标记任务为完成，调度器会自动删除
                    await context.Scheduler.DeleteJob(context.JobDetail.Key);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务数据失败，任务键: {JobKey}", context.JobDetail.Key);
            }
        }
    }

    /// <summary>
    /// 任务执行历史（用于MongoDB存储）
    /// </summary>
    public class JobExecutionHistory
    {
        public string Id { get; set; }
        public string JobKey { get; set; }
        public string ApiConfigId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public long DurationMs { get; set; }
        public JobExecutionStatus Status { get; set; }
        public string ErrorMessage { get; set; }
        public string ExceptionDetails { get; set; }
        public string Result { get; set; }
        public int RetryCount { get; set; }
        public bool IsRetry { get; set; }
        public string TriggerType { get; set; }
    }

    /// <summary>
    /// 任务执行状态
    /// </summary>
    public enum JobExecutionStatus
    {
        Running = 0,
        Success = 1,
        Failed = 2,
        Timeout = 3,
        Cancelled = 4,
        Retrying = 5
    }
}
