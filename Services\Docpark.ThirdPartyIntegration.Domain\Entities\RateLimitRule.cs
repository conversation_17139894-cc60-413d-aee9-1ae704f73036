using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 限流规则实体
    /// </summary>
    public class RateLimitRule
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// API配置ID（可选，为空表示全局规则）
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 限流类型
        /// </summary>
        [Required]
        public RateLimitType LimitType { get; set; }

        /// <summary>
        /// 限流维度（如：IP、用户、API等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LimitDimension { get; set; }

        /// <summary>
        /// 限流阈值
        /// </summary>
        public int LimitThreshold { get; set; }

        /// <summary>
        /// 时间窗口（秒）
        /// </summary>
        public int TimeWindowSeconds { get; set; }

        /// <summary>
        /// 令牌桶容量（仅TokenBucket类型使用）
        /// </summary>
        public int? BucketCapacity { get; set; }

        /// <summary>
        /// 令牌补充速率（每秒）
        /// </summary>
        public double? RefillRate { get; set; }

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 匹配条件（JSON格式）
        /// </summary>
        public string MatchConditions { get; set; }

        /// <summary>
        /// 限流响应配置（JSON格式）
        /// </summary>
        public string ResponseConfig { get; set; }

        /// <summary>
        /// 白名单（JSON数组格式）
        /// </summary>
        public string Whitelist { get; set; }

        /// <summary>
        /// 黑名单（JSON数组格式）
        /// </summary>
        public string Blacklist { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }
    }
}
