using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 告警服务接口
    /// </summary>
    public interface IAlertService
    {
        /// <summary>
        /// 创建告警规则
        /// </summary>
        Task<string> CreateAlertRuleAsync(AlertRule rule);

        /// <summary>
        /// 更新告警规则
        /// </summary>
        Task<bool> UpdateAlertRuleAsync(string id, AlertRule rule);

        /// <summary>
        /// 获取告警规则
        /// </summary>
        Task<AlertRule> GetAlertRuleAsync(string id);

        /// <summary>
        /// 获取所有告警规则
        /// </summary>
        Task<List<AlertRule>> GetAllAlertRulesAsync(bool enabledOnly = false);

        /// <summary>
        /// 删除告警规则
        /// </summary>
        Task<bool> DeleteAlertRuleAsync(string id);

        /// <summary>
        /// 触发告警
        /// </summary>
        Task<string> TriggerAlertAsync(Alert alert);

        /// <summary>
        /// 确认告警
        /// </summary>
        Task<bool> AcknowledgeAlertAsync(string alertId, string acknowledgedBy, string notes = null);

        /// <summary>
        /// 解决告警
        /// </summary>
        Task<bool> ResolveAlertAsync(string alertId, string resolvedBy, string resolutionNotes = null);

        /// <summary>
        /// 获取活跃告警
        /// </summary>
        Task<List<Alert>> GetActiveAlertsAsync(AlertSeverity? severity = null, string apiConfigId = null);

        /// <summary>
        /// 获取告警历史
        /// </summary>
        Task<List<Alert>> GetAlertHistoryAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null, int limit = 100);

        /// <summary>
        /// 评估告警规则
        /// </summary>
        Task EvaluateAlertRulesAsync();

        /// <summary>
        /// 评估特定API的告警规则
        /// </summary>
        Task EvaluateApiAlertRulesAsync(string apiConfigId);

        /// <summary>
        /// 发送告警通知
        /// </summary>
        Task<bool> SendAlertNotificationAsync(Alert alert, NotificationChannel channel);

        /// <summary>
        /// 获取告警统计信息
        /// </summary>
        Task<object> GetAlertStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 清理过期的告警数据
        /// </summary>
        Task<int> CleanupExpiredAlertsAsync(int olderThanDays = 90);

        /// <summary>
        /// 静默告警
        /// </summary>
        Task<bool> SilenceAlertAsync(string alertId, int silenceDurationMinutes, string silencedBy, string reason = null);

        /// <summary>
        /// 取消静默
        /// </summary>
        Task<bool> UnsilenceAlertAsync(string alertId, string unsilencedBy);
    }
}
