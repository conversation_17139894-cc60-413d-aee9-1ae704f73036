using System.ComponentModel.DataAnnotations;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// API参数实体
    /// </summary>
    public class ApiParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 参数位置：header, query, body
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Location { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        [Required]
        public ParameterType Type { get; set; }

        /// <summary>
        /// 参数值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 格式化字符串（用于时间等动态参数）
        /// </summary>
        [StringLength(100)]
        public string Format { get; set; }
    }
}
