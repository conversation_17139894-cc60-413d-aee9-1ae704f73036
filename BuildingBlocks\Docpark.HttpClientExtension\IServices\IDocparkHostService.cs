﻿using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IDocparkHostService
    {
        Task<ResultInfo<List<UserListDto>>> GetUsersByUserIds(long[] userIds);
        Task<ResultInfo<string>> GetOrganizationUnitByUserName(string userName);

        Task<ResultInfo<string>> GetOrganizationUnitByUserId(string userId);
        Task<ResultInfo<string>> GetShortSummary(string mstId);
        Task<Dictionary<string, object>> GetShortSummarys(List<Guid> mstId);

        Task<ResultInfo> BatchRemove(List<string> mstId);

        Task<ResultInfo> SaveFormData(List<SaveFormDocumentDto> inputs);



        Task<T> GetFormData<T>(string documentTypeId, string mstId);

        Task UpdateFormDataStatus(Guid mstId, int status);

        Task<T> GetFormData<T>(string mstId);

        Task<int> Count(string documentTypeId, List<Filter> filters, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10, bool enablePremission = false);
        Task<int> Count(string documentTypeId, List<List<Filter>> filterss, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10);


        Task<List<T>> GetFormDataList<T>(string documentTypeId, List<string> mstId, bool enablePremission = false);

        Task<ResultInfo<string>> GetFormDataList(string documentTypeId, List<Filter> filters, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10, bool enablePremission = false);

        Task<ResultInfo<string>> GetFormDataLists(string documentTypeId, List<List<Filter>> filterss, List<Sorter> sorters, List<string> mstIds, int pageIndex = 1, int pageSize = 10);
        Task<ResultInfo<string>> GetMstIds(string formId, List<Filter> filters, int pageIndex = 1, int pageSize = 10);
        Task<ResultInfo<string>> GetDocumentMstIds(string formId, List<Filter> filters, int pageIndex = 1, int pageSize = 10);

        Task<ResultInfo<List<Guid>>> GetPermissionBusinessType(string userId);

        Task<List<DocFormField>> GetFields(string documentTypeId);

        Task<bool> SaveFormData(Dictionary<string, object> data);

        Task<bool> SaveFormDataList(List<Dictionary<string, object>> dataList);

        Task<List<RelationshipsDto>> GetRelationships(string relationId, List<Guid> TargetObjInstanceIds);

        Task<Guid> GetTargetObjInstanceId(Guid ObjUniqueId);

        Task<List<Guid>> GetTargetObjInstanceIds(List<Guid> objUniqueIds);

        Task RemoveRelationshipsList(List<Guid> objUniqueIds);

        Task<bool> SaveRelationships(List<Dictionary<string, object>> data);

        Task<bool> RemoveRelationships(Guid objUnionId);

        Task<bool> RemoveAllRelationships(Guid ObjId, string relationId, Guid TargetObjInstanceId);
    }
}
