﻿using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{

    /// <summary>
    /// 单据主数据信息
    /// </summary>
    [JsonTable("MC_BILL_MASTER_DATA")]
    public class MC_BILL_MASTER_DATA
    {
        /// <summary>
        /// 单据来源
        /// </summary>
        public string third_bill_source { get; set; }
        /// <summary>
        /// 第三方单据状态
        /// </summary>
        public string third_bill_status { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string business_type { get; set; }
        /// <summary>
        /// Mstid
        /// </summary>
        public Guid mstId { get; set; }
        /// <summary>
        /// 单据id
        /// </summary>
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string bill_no { get; set; }
        /// <summary>
        /// 单据标题
        /// </summary>
        public string third_bill_title { get; set; }
        /// <summary>
        /// 提单人代码
        /// </summary>
        public string proposer_code { get; set; }
        /// <summary>
        /// 提单人名称
        /// </summary>
        public string proposer_name { get; set; }
        /// <summary>
        /// 提单时间
        /// </summary>
        public string bill_submit_time { get; set; }
        /// <summary>
        /// 提单人部门编码
        /// </summary>
        public string proposer_dept_code { get; set; }
        /// <summary>
        /// 提单人部门名称
        /// </summary>
        public string proposer_dept_name { get; set; }
        /// <summary>
        /// 成本中心编码
        /// </summary>
        public string cost_center_code { get; set; }
        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string cost_center_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string supplier_code { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string supplier_name { get; set; }
        /// <summary>
        /// 费用明细
        /// </summary>
        public List<MC_Expense_details> expense_details { get; set; }
        /// <summary>
        /// 票据凭证数量
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<int>))]
        public int invoice_qty { get; set; }
        /// <summary>
        /// 报销总金额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double total_amount { get; set; }
        /// <summary>
        /// 财务核定总金额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double total_approved_amount { get; set; }
        /// <summary>
        /// 费用凭证号
        /// </summary>
        public string expense_voucher_no { get; set; }
        /// <summary>
        /// 支付凭证号
        /// </summary>
        public string payment_voucher_no { get; set; }
        /// <summary>
        /// 影像条码
        /// </summary>
        public string image_barcode { get; set; }
        /// <summary>
        /// 业务状态
        /// </summary>
        public string business_status { get; set; }
        /// <summary>
        /// 系统状态
        /// </summary>
        public string sys_status { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 附件数据集合
        /// </summary>
        public List<Annex_items> annex_items { get; set; }
        /// <summary>
        /// 是否发送
        /// </summary>
        public int ispost { get; set; }
        /// <summary>
        /// 匹配模式, 00 手工匹配, 01 自动匹配
        /// </summary>
        public string match_mode { get; set; }
        /// <summary>
        /// 报销单文件
        /// </summary>
        public string reim_file_details { get; set; }
        /// <summary>
        /// 报销单文件-类型
        /// </summary>
        public string reim_filetype { get; set; }
    }
    /// <summary>
    /// 主表单费用明细
    /// </summary>
    public class MC_Expense_details
    {
        /// <summary>
        /// 费用明细id
        /// </summary>
        public string expense_detail_id { get; set; }
        /// <summary>
        /// 费用类型编码
        /// </summary>
        public string expense_type_code { get; set; }
        /// <summary>
        /// 费用类型名称
        /// </summary>
        public string expense_type_name { get; set; }
        /// <summary>
        /// 申请金额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double apply_amount { get; set; }
        /// <summary>
        /// 财务核定金额
        /// </summary>
        [JsonConverter(typeof(CustomerJsonConvert<double>))]
        public Double approved_amount { get; set; }
        /// <summary>
        /// 费用明细说明
        /// </summary>
        public string expense_remark { get; set; }
    }
    /// <summary>
    /// 单据附件数据集合
    /// </summary>
    public class Annex_items
    {
        /// <summary>
        /// 附件guid
        /// </summary>
        public string annex_guid { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        public string annex_name { get; set; }
        /// <summary>
        /// 附件大类
        /// </summary>
        public string annex_type { get; set; }
        /// <summary>
        /// 附件数据类型
        /// </summary>
        public string annex_data_type { get; set; }
        /// <summary>
        /// 附件数据详情
        /// </summary>
        public string annex_details { get; set; }
    }
}
