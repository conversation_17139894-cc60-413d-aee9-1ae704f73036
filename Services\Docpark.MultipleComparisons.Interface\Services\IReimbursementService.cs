﻿using Docpark.MultipleComparisons.Interface.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IReimbursementService
    {
        /// <summary>
        /// 推送智能比对数据
        /// </summary>
        /// <param name="bill_no"></param>
        /// <returns></returns>
        Task<(bool result, string msg)> SendDataTo_MC01(string[] bill_nos);
        /// <summary>
        /// 单据比对成功修改报销单状态
        /// </summary>
        Task<bool> SuccessDataToReim(string[] bill_nos);
    }
}