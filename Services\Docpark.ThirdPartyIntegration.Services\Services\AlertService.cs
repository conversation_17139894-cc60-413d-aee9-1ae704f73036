using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 告警服务实现
    /// </summary>
    public class AlertService : IAlertService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly IMonitoringService _monitoringService;
        private readonly ILogger<AlertService> _logger;

        private const string AlertRuleCollectionName = "alert_rules";
        private const string AlertCollectionName = "alerts";

        public AlertService(
            IMongoRepository mongoRepository,
            IMonitoringService monitoringService,
            ILogger<AlertService> logger)
        {
            _mongoRepository = mongoRepository;
            _monitoringService = monitoringService;
            _logger = logger;
        }

        /// <summary>
        /// 创建告警规则
        /// </summary>
        public async Task<string> CreateAlertRuleAsync(AlertRule rule)
        {
            try
            {
                rule.Id = null; // 确保创建新记录
                rule.CreatedAt = DateTime.UtcNow;
                rule.UpdatedAt = DateTime.UtcNow;

                await _mongoRepository.InsertOneAsync(AlertRuleCollectionName, rule);
                _logger.LogInformation("创建告警规则成功，规则名称: {RuleName}", rule.Name);
                return rule.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建告警规则失败，规则名称: {RuleName}", rule.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新告警规则
        /// </summary>
        public async Task<bool> UpdateAlertRuleAsync(string id, AlertRule rule)
        {
            try
            {
                rule.Id = id;
                rule.UpdatedAt = DateTime.UtcNow;

                var filter = Builders<AlertRule>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.ReplaceOneAsync(AlertRuleCollectionName, filter, rule);
                
                _logger.LogInformation("更新告警规则成功，ID: {Id}", id);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新告警规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取告警规则
        /// </summary>
        public async Task<AlertRule> GetAlertRuleAsync(string id)
        {
            try
            {
                var filter = Builders<AlertRule>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<AlertRule>(AlertRuleCollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取所有告警规则
        /// </summary>
        public async Task<List<AlertRule>> GetAllAlertRulesAsync(bool enabledOnly = false)
        {
            try
            {
                var filterBuilder = Builders<AlertRule>.Filter;
                var filter = enabledOnly ? filterBuilder.Eq(x => x.IsEnabled, true) : filterBuilder.Empty;

                var rules = await _mongoRepository.GetCollection<AlertRule>(AlertRuleCollectionName)
                    .Find(filter)
                    .ToListAsync();

                return rules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警规则列表失败");
                throw;
            }
        }

        /// <summary>
        /// 删除告警规则
        /// </summary>
        public async Task<bool> DeleteAlertRuleAsync(string id)
        {
            try
            {
                var filter = Builders<AlertRule>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.DeleteOneAsync<AlertRule>(AlertRuleCollectionName, filter);
                
                _logger.LogInformation("删除告警规则成功，ID: {Id}", id);
                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除告警规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 触发告警
        /// </summary>
        public async Task<string> TriggerAlertAsync(Alert alert)
        {
            try
            {
                // 检查是否已存在相同的活跃告警
                var existingAlert = await GetExistingActiveAlertAsync(alert.AlertId);
                
                if (existingAlert != null)
                {
                    // 更新现有告警
                    existingAlert.AlertCount++;
                    existingAlert.LastAlertTime = DateTime.UtcNow;
                    existingAlert.UpdatedAt = DateTime.UtcNow;
                    
                    var filter = Builders<Alert>.Filter.Eq(x => x.Id, existingAlert.Id);
                    await _mongoRepository.ReplaceOneAsync(AlertCollectionName, filter, existingAlert);
                    
                    _logger.LogInformation("更新现有告警，告警ID: {AlertId}, 告警次数: {AlertCount}", 
                        alert.AlertId, existingAlert.AlertCount);
                    
                    return existingAlert.Id;
                }
                else
                {
                    // 创建新告警
                    alert.Id = null;
                    alert.CreatedAt = DateTime.UtcNow;
                    alert.UpdatedAt = DateTime.UtcNow;
                    alert.FirstAlertTime = DateTime.UtcNow;
                    alert.LastAlertTime = DateTime.UtcNow;

                    await _mongoRepository.InsertOneAsync(AlertCollectionName, alert);
                    
                    _logger.LogInformation("创建新告警，告警ID: {AlertId}, 严重程度: {Severity}", 
                        alert.AlertId, alert.Severity);

                    // 发送通知
                    await SendAlertNotificationsAsync(alert);
                    
                    return alert.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发告警失败，告警ID: {AlertId}", alert.AlertId);
                throw;
            }
        }

        /// <summary>
        /// 确认告警
        /// </summary>
        public async Task<bool> AcknowledgeAlertAsync(string alertId, string acknowledgedBy, string notes = null)
        {
            try
            {
                var filter = Builders<Alert>.Filter.Eq(x => x.AlertId, alertId);
                var update = Builders<Alert>.Update
                    .Set(x => x.IsAcknowledged, true)
                    .Set(x => x.AcknowledgedAt, DateTime.UtcNow)
                    .Set(x => x.AcknowledgedBy, acknowledgedBy)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                if (!string.IsNullOrEmpty(notes))
                {
                    update = update.Set(x => x.ResolutionNotes, notes);
                }

                var result = await _mongoRepository.UpdateOneAsync(AlertCollectionName, filter, update);
                _logger.LogInformation("确认告警成功，告警ID: {AlertId}, 确认者: {AcknowledgedBy}", alertId, acknowledgedBy);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认告警失败，告警ID: {AlertId}", alertId);
                throw;
            }
        }

        /// <summary>
        /// 解决告警
        /// </summary>
        public async Task<bool> ResolveAlertAsync(string alertId, string resolvedBy, string resolutionNotes = null)
        {
            try
            {
                var filter = Builders<Alert>.Filter.Eq(x => x.AlertId, alertId);
                var update = Builders<Alert>.Update
                    .Set(x => x.IsResolved, true)
                    .Set(x => x.ResolvedAt, DateTime.UtcNow)
                    .Set(x => x.ResolvedBy, resolvedBy)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                if (!string.IsNullOrEmpty(resolutionNotes))
                {
                    update = update.Set(x => x.ResolutionNotes, resolutionNotes);
                }

                var result = await _mongoRepository.UpdateOneAsync(AlertCollectionName, filter, update);
                _logger.LogInformation("解决告警成功，告警ID: {AlertId}, 解决者: {ResolvedBy}", alertId, resolvedBy);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解决告警失败，告警ID: {AlertId}", alertId);
                throw;
            }
        }

        /// <summary>
        /// 获取活跃告警
        /// </summary>
        public async Task<List<Alert>> GetActiveAlertsAsync(AlertSeverity? severity = null, string apiConfigId = null)
        {
            try
            {
                var filterBuilder = Builders<Alert>.Filter;
                var filter = filterBuilder.Eq(x => x.IsResolved, false);

                if (severity.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.Severity, severity.Value));
                }

                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.ApiConfigId, apiConfigId));
                }

                var sort = Builders<Alert>.Sort.Descending(x => x.Severity).Descending(x => x.LastAlertTime);

                var alerts = await _mongoRepository.GetCollection<Alert>(AlertCollectionName)
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();

                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃告警失败");
                throw;
            }
        }

        /// <summary>
        /// 获取告警历史
        /// </summary>
        public async Task<List<Alert>> GetAlertHistoryAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null, int limit = 100)
        {
            try
            {
                var filterBuilder = Builders<Alert>.Filter;
                var filter = filterBuilder.Empty;

                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.ApiConfigId, apiConfigId));
                }

                if (startTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Gte(x => x.FirstAlertTime, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Lte(x => x.FirstAlertTime, endTime.Value));
                }

                var sort = Builders<Alert>.Sort.Descending(x => x.LastAlertTime);

                var alerts = await _mongoRepository.GetCollection<Alert>(AlertCollectionName)
                    .Find(filter)
                    .Sort(sort)
                    .Limit(limit)
                    .ToListAsync();

                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警历史失败");
                throw;
            }
        }

        /// <summary>
        /// 评估告警规则
        /// </summary>
        public async Task EvaluateAlertRulesAsync()
        {
            try
            {
                var rules = await GetAllAlertRulesAsync(true);
                
                foreach (var rule in rules)
                {
                    try
                    {
                        await EvaluateAlertRuleAsync(rule);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "评估告警规则失败，规则ID: {RuleId}", rule.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估告警规则失败");
                throw;
            }
        }

        /// <summary>
        /// 评估特定API的告警规则
        /// </summary>
        public async Task EvaluateApiAlertRulesAsync(string apiConfigId)
        {
            try
            {
                var filterBuilder = Builders<AlertRule>.Filter;
                var filter = filterBuilder.And(
                    filterBuilder.Eq(x => x.IsEnabled, true),
                    filterBuilder.Or(
                        filterBuilder.Eq(x => x.ApiConfigId, apiConfigId),
                        filterBuilder.Eq(x => x.ApiConfigId, null) // 全局规则
                    )
                );

                var rules = await _mongoRepository.GetCollection<AlertRule>(AlertRuleCollectionName)
                    .Find(filter)
                    .ToListAsync();

                foreach (var rule in rules)
                {
                    try
                    {
                        await EvaluateAlertRuleAsync(rule, apiConfigId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "评估API告警规则失败，规则ID: {RuleId}, API配置ID: {ApiConfigId}", 
                            rule.Id, apiConfigId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估API告警规则失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 发送告警通知
        /// </summary>
        public async Task<bool> SendAlertNotificationAsync(Alert alert, NotificationChannel channel)
        {
            try
            {
                // 这里实现具体的通知发送逻辑
                // 根据不同的通知渠道调用相应的服务

                switch (channel)
                {
                    case NotificationChannel.Email:
                        return await SendEmailNotificationAsync(alert);
                    case NotificationChannel.SMS:
                        return await SendSmsNotificationAsync(alert);
                    case NotificationChannel.WeChat:
                        return await SendWeChatNotificationAsync(alert);
                    case NotificationChannel.DingTalk:
                        return await SendDingTalkNotificationAsync(alert);
                    case NotificationChannel.Slack:
                        return await SendSlackNotificationAsync(alert);
                    case NotificationChannel.Webhook:
                        return await SendWebhookNotificationAsync(alert);
                    case NotificationChannel.InApp:
                        return await SendInAppNotificationAsync(alert);
                    default:
                        _logger.LogWarning("不支持的通知渠道: {Channel}", channel);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送告警通知失败，告警ID: {AlertId}, 通知渠道: {Channel}",
                    alert.AlertId, channel);
                return false;
            }
        }

        /// <summary>
        /// 获取告警统计信息
        /// </summary>
        public async Task<object> GetAlertStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var filterBuilder = Builders<Alert>.Filter;
                var filter = filterBuilder.Empty;

                if (startTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Gte(x => x.FirstAlertTime, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Lte(x => x.FirstAlertTime, endTime.Value));
                }

                var alerts = await _mongoRepository.GetCollection<Alert>(AlertCollectionName)
                    .Find(filter)
                    .ToListAsync();

                var statistics = new
                {
                    TotalAlerts = alerts.Count,
                    ActiveAlerts = alerts.Count(a => !a.IsResolved),
                    ResolvedAlerts = alerts.Count(a => a.IsResolved),
                    AcknowledgedAlerts = alerts.Count(a => a.IsAcknowledged),
                    CriticalAlerts = alerts.Count(a => a.Severity == AlertSeverity.Critical),
                    ErrorAlerts = alerts.Count(a => a.Severity == AlertSeverity.Error),
                    WarningAlerts = alerts.Count(a => a.Severity == AlertSeverity.Warning),
                    InfoAlerts = alerts.Count(a => a.Severity == AlertSeverity.Info),
                    AverageResolutionTimeMinutes = alerts.Where(a => a.IsResolved && a.ResolvedAt.HasValue)
                        .Average(a => (a.ResolvedAt.Value - a.FirstAlertTime).TotalMinutes),
                    TopAlertTypes = alerts.GroupBy(a => a.AlertType)
                        .Select(g => new { AlertType = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .Take(10)
                        .ToList()
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警统计信息失败");
                throw;
            }
        }

        /// <summary>
        /// 清理过期的告警数据
        /// </summary>
        public async Task<int> CleanupExpiredAlertsAsync(int olderThanDays = 90)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                var filter = Builders<Alert>.Filter.And(
                    Builders<Alert>.Filter.Lt(x => x.CreatedAt, cutoffDate),
                    Builders<Alert>.Filter.Eq(x => x.IsResolved, true)
                );

                var result = await _mongoRepository.DeleteManyAsync<Alert>(AlertCollectionName, filter);
                var deletedCount = (int)result.DeletedCount;

                _logger.LogInformation("清理过期告警数据完成，删除记录数: {DeletedCount}", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期告警数据失败");
                throw;
            }
        }

        /// <summary>
        /// 静默告警
        /// </summary>
        public async Task<bool> SilenceAlertAsync(string alertId, int silenceDurationMinutes, string silencedBy, string reason = null)
        {
            try
            {
                // 这里可以实现告警静默逻辑
                // 例如：在告警数据中添加静默标记和时间
                _logger.LogInformation("静默告警，告警ID: {AlertId}, 静默时长: {Duration}分钟, 操作者: {SilencedBy}",
                    alertId, silenceDurationMinutes, silencedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "静默告警失败，告警ID: {AlertId}", alertId);
                return false;
            }
        }

        /// <summary>
        /// 取消静默
        /// </summary>
        public async Task<bool> UnsilenceAlertAsync(string alertId, string unsilencedBy)
        {
            try
            {
                // 这里可以实现取消告警静默逻辑
                _logger.LogInformation("取消静默告警，告警ID: {AlertId}, 操作者: {UnsilencedBy}",
                    alertId, unsilencedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消静默告警失败，告警ID: {AlertId}", alertId);
                return false;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取现有的活跃告警
        /// </summary>
        private async Task<Alert> GetExistingActiveAlertAsync(string alertId)
        {
            var filter = Builders<Alert>.Filter.And(
                Builders<Alert>.Filter.Eq(x => x.AlertId, alertId),
                Builders<Alert>.Filter.Eq(x => x.IsResolved, false)
            );

            return await _mongoRepository.FindOneAsync<Alert>(AlertCollectionName, filter);
        }

        /// <summary>
        /// 评估单个告警规则
        /// </summary>
        private async Task EvaluateAlertRuleAsync(AlertRule rule, string specificApiConfigId = null)
        {
            try
            {
                // 这里实现具体的规则评估逻辑
                // 根据规则条件检查监控数据，判断是否需要触发告警

                var apiConfigId = specificApiConfigId ?? rule.ApiConfigId;
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    // 全局规则，需要检查所有API
                    return;
                }

                // 获取最近的健康检查数据
                var recentHealthChecks = await _monitoringService.GetHealthCheckHistoryAsync(
                    apiConfigId,
                    DateTime.UtcNow.AddMinutes(-rule.EvaluationIntervalMinutes),
                    DateTime.UtcNow,
                    100);

                // 根据规则条件评估是否需要触发告警
                var shouldTriggerAlert = await EvaluateRuleConditions(rule, recentHealthChecks);

                if (shouldTriggerAlert)
                {
                    var alert = new Alert
                    {
                        AlertId = $"{rule.Id}_{apiConfigId}_{DateTime.UtcNow:yyyyMMddHHmm}",
                        ApiConfigId = apiConfigId,
                        Title = $"告警规则触发: {rule.Name}",
                        Description = rule.Description,
                        Severity = rule.Severity,
                        AlertType = rule.AlertType,
                        Source = "AlertRule",
                        AlertData = new BsonDocument
                        {
                            { "ruleId", rule.Id },
                            { "ruleName", rule.Name },
                            { "evaluationTime", DateTime.UtcNow }
                        }
                    };

                    await TriggerAlertAsync(alert);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估告警规则失败，规则ID: {RuleId}", rule.Id);
            }
        }

        /// <summary>
        /// 评估规则条件
        /// </summary>
        private async Task<bool> EvaluateRuleConditions(AlertRule rule, List<HealthCheck> healthChecks)
        {
            try
            {
                // 解析规则条件
                var conditions = JsonConvert.DeserializeObject<dynamic>(rule.Conditions);

                // 这里实现具体的条件评估逻辑
                // 例如：检查响应时间、可用性、错误率等指标

                // 简化的示例逻辑
                if (rule.AlertType == "ResponseTime")
                {
                    var avgResponseTime = healthChecks.Any() ? healthChecks.Average(h => h.ResponseTimeMs) : 0;
                    var threshold = conditions.threshold ?? 5000; // 默认5秒
                    return avgResponseTime > threshold;
                }
                else if (rule.AlertType == "Availability")
                {
                    var healthyCount = healthChecks.Count(h => h.Status == HealthStatus.Healthy);
                    var totalCount = healthChecks.Count;
                    var availability = totalCount > 0 ? (double)healthyCount / totalCount : 1.0;
                    var threshold = conditions.threshold ?? 0.95; // 默认95%
                    return availability < threshold;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估规则条件失败，规则ID: {RuleId}", rule.Id);
                return false;
            }
        }

        /// <summary>
        /// 发送告警通知到所有配置的渠道
        /// </summary>
        private async Task SendAlertNotificationsAsync(Alert alert)
        {
            try
            {
                // 获取告警规则的通知配置
                var rule = await GetAlertRuleByAlertAsync(alert);
                if (rule?.NotificationChannels != null)
                {
                    foreach (var channel in rule.NotificationChannels)
                    {
                        await SendAlertNotificationAsync(alert, channel);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送告警通知失败，告警ID: {AlertId}", alert.AlertId);
            }
        }

        /// <summary>
        /// 根据告警获取对应的告警规则
        /// </summary>
        private async Task<AlertRule> GetAlertRuleByAlertAsync(Alert alert)
        {
            try
            {
                if (alert.AlertData?.Contains("ruleId") == true)
                {
                    var ruleId = alert.AlertData["ruleId"].AsString;
                    return await GetAlertRuleAsync(ruleId);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警对应的规则失败，告警ID: {AlertId}", alert.AlertId);
                return null;
            }
        }

        // 各种通知渠道的实现方法（简化版本）
        private async Task<bool> SendEmailNotificationAsync(Alert alert)
        {
            // 实现邮件通知
            _logger.LogInformation("发送邮件通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendSmsNotificationAsync(Alert alert)
        {
            // 实现短信通知
            _logger.LogInformation("发送短信通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendWeChatNotificationAsync(Alert alert)
        {
            // 实现微信通知
            _logger.LogInformation("发送微信通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendDingTalkNotificationAsync(Alert alert)
        {
            // 实现钉钉通知
            _logger.LogInformation("发送钉钉通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendSlackNotificationAsync(Alert alert)
        {
            // 实现Slack通知
            _logger.LogInformation("发送Slack通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendWebhookNotificationAsync(Alert alert)
        {
            // 实现Webhook通知
            _logger.LogInformation("发送Webhook通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        private async Task<bool> SendInAppNotificationAsync(Alert alert)
        {
            // 实现应用内通知
            _logger.LogInformation("发送应用内通知，告警ID: {AlertId}", alert.AlertId);
            return true;
        }

        #endregion
    }
}
