﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_Voucher2Invoice_Relation_Data
    {
        /// <summary>
        /// 凭证Id<br/>
        /// 用户系统凭证唯一编码。有则传入，无则不传入，数据以 业务实体名，凭证类型，凭证期间，和凭证号 四要素组合确定唯一
        /// </summary>
        public string voucherId { get; set; }

        /// <summary>
        /// 业务实体名称(公司名称)
        /// </summary>
        public string businessEntityName { get; set; }

        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucherType { get; set; }

        /// <summary>
        /// 凭证期间
        /// </summary>
        public string voucherPeriod { get; set; }

        /// <summary>
        /// 凭证号
        /// </summary>
        public string voucherNo { get; set; }

        /// <summary>
        /// 发票列表
        /// </summary>
        public List<Voucher2Invoice_Invoice> invoiceList { get; set; }
    }

    /// <summary>
    /// 发票
    /// </summary>
    public class Voucher2Invoice_Invoice
    {
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNumber { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 发票详情
        /// </summary>
        public Voucher2Invoice_InvoiceDetail invoiceDetail { get; set; }

    }

    /// <summary>
    /// 发票详情
    /// </summary>
    public class Voucher2Invoice_InvoiceDetail
    {
        /// <summary>
        /// 乘车时间
        /// </summary>
        public string departureTime { get; set; }
        /// <summary>
        /// 乘客姓名
        /// </summary>
        public string passengerName { get; set; }

    }
}
