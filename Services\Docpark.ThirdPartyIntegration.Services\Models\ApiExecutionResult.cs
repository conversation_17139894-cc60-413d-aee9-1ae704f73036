using System;

namespace Docpark.ThirdPartyIntegration.Services.Models
{
    /// <summary>
    /// API执行结果
    /// </summary>
    public class ApiExecutionResult
    {
        /// <summary>
        /// API配置ID
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 响应数据（处理后的）
        /// </summary>
        public string ResponseData { get; set; }

        /// <summary>
        /// 原始响应数据（未处理的）
        /// </summary>
        public string OriginalResponseData { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// 执行时间戳
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 请求数据
        /// </summary>
        public string RequestData { get; set; }

        /// <summary>
        /// 处理记录数
        /// </summary>
        public int RecordsProcessed { get; set; }

        /// <summary>
        /// 执行日志ID
        /// </summary>
        public string ExecutionLogId { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="responseData"></param>
        /// <param name="statusCode"></param>
        /// <param name="executionTime"></param>
        /// <returns></returns>
        public static ApiExecutionResult Success(string apiConfigId, string responseData, int statusCode, TimeSpan executionTime)
        {
            return new ApiExecutionResult
            {
                ApiConfigId = apiConfigId,
                IsSuccess = true,
                ResponseData = responseData,
                StatusCode = statusCode,
                ExecutionTime = executionTime
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="errorMessage"></param>
        /// <param name="statusCode"></param>
        /// <param name="executionTime"></param>
        /// <returns></returns>
        public static ApiExecutionResult Failure(string apiConfigId, string errorMessage, int statusCode = 0, TimeSpan? executionTime = null)
        {
            return new ApiExecutionResult
            {
                ApiConfigId = apiConfigId,
                IsSuccess = false,
                ErrorMessage = errorMessage,
                StatusCode = statusCode,
                ExecutionTime = executionTime ?? TimeSpan.Zero
            };
        }
    }
}
