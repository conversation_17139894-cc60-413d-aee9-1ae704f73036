﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class MC_SCAN_IMAGE_RESULT_Model
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime createDateTime { get; set; }
        /// <summary>
        /// 文档ID
        /// </summary>
        public string docId { get; set; }
        /// <summary>
        /// 文档类型ID
        /// </summary>
        public string docType { get; set; }
        /// <summary>
        /// 单据详情ID(bill_detail_id)
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 扫描影像简报数据
        /// </summary>
        public string metaData { get; set; }
        /// <summary>
        /// 扫描件的mstid
        /// </summary>
        public string mstId { get; set; }
        /// <summary>
        /// 扫描人
        /// </summary>
        public string scanner { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 文档类型标识, title_page(封面) attachment(附件) invoice(发票) Voucher(票据)
        /// </summary>
        public string docTypeCode { get; set; }
    }

    public class MC_SCAN_RESULT_Model
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime creatorDate { get; set; }
        /// <summary>
        /// 文档ID
        /// </summary>
        public string documentId { get; set; }
        /// <summary>
        /// 文档类型ID
        /// </summary>
        public string documentTypeId { get; set; }
        /// <summary>
        /// 单据详情ID(bill_detail_id)
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 扫描影像简报数据
        /// </summary>
        public string metaData { get; set; }
        /// <summary>
        /// 货币信息
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 扫描件的mstid
        /// </summary>
        public string mstId { get; set; }
        /// <summary>
        /// 扫描人
        /// </summary>
        public string scanner { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 文档类型标识, title_page(封面) attachment(附件) invoice(发票) Voucher(票据)
        /// </summary>
        public string docTypeCode { get; set; }
    }
    public class MC_SCAN_IMAGE_RESULT_MetaData_Model
    {
        /// <summary>
        /// 字段ID
        /// </summary>
        public string field { get; set; }
        /// <summary>
        /// 字段标识
        /// </summary>
        public string identity { get; set; }
        /// <summary>
        /// 字段名称
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 字段数值
        /// </summary>
        public string value { get; set; }
    }
    public class MC_CNY_Amount
    {
        public string Item1 { get; set; }
        public string Item2 { get; set; }
        public double Item3 { get; set; }
    }
}
