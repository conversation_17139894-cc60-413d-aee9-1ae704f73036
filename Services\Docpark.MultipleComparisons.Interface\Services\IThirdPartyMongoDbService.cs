﻿using Docpark.MultipleComparisons.Interface.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IThirdPartyMongoDbService
    {
        /// <summary>
        /// 获取调用日志
        /// </summary>
        /// <param name="apiName">调用的apiName</param>
        /// <param name="keywords">查询的关键字</param>
        /// <returns>返回查询结果</returns>
        Task<List<MONGODB_THIRDPARTY_CALL_LOG>> GetCallLog(string apiName, string keywords, DateTime beginTime, DateTime endTime);
        /// <summary>
        /// 添加调用日志
        /// </summary>
        /// <param name="saveData">添加的日志</param>
        /// <returns>返回执行结果</returns>
        Task<bool> AddCallLog(MONGODB_THIRDPARTY_CALL_LOG saveData);
        /// <summary>
        /// 删除调用日志
        /// </summary>
        /// <param name="id">删除的日志ID</param>
        /// <returns>返回执行结果</returns>
        Task<bool> DelCallLog(string id);
    }
}
