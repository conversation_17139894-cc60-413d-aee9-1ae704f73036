namespace Docpark.ThirdPartyIntegration.Domain.Enums
{
    /// <summary>
    /// 授权类型枚举
    /// </summary>
    public enum AuthenticationType
    {
        /// <summary>
        /// 基础认证（用户名密码）
        /// </summary>
        BasicAuth = 1,

        /// <summary>
        /// OAuth2授权
        /// </summary>
        OAuth2 = 2,

        /// <summary>
        /// API密钥
        /// </summary>
        ApiKey = 3,

        /// <summary>
        /// Bearer令牌
        /// </summary>
        BearerToken = 4,

        /// <summary>
        /// 自定义授权（可扩展）
        /// </summary>
        Custom = 99
    }
}
