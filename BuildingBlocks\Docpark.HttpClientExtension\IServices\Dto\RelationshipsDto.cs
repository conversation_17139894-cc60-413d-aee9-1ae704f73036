﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class RelationshipsDto
    {
        /// <summary>
        /// 子对象的对象标识
        /// </summary>
        public Guid ObjId { get; set; }

        /// <summary>
        /// 子对象实例的唯一标识
        /// </summary>
        public Guid ObjUniqueId { get; set; }

        /// <summary>
        /// 子对象内关系字段定义的标识,入InvoiceItem,OrderItem等标识
        /// </summary>
        public string RelationId { get; set; }

        /// <summary>
        /// 父对象实例的唯一标识
        /// </summary>
        public Guid TargetObjInstanceId { get; set; }
    }
}
