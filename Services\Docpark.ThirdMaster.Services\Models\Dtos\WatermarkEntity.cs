﻿namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class WatermarkEntity
    {
        /// <summary>
        /// 水印参数
        /// </summary>
        public Watermark Watermark { get; set; }

        /// <summary>
        /// 业务操作
        /// </summary>
        public Business Business { get; set; }
    }

    public class Business
    {
        /// <summary>
        /// 操作类型(Add:添加;Preview:预览;View:查看;Create:创建;)
        /// </summary>
        public string OperationType { get; set; }

        /// <summary>
        /// 文档Id
        /// </summary>
        public string DocumentId { get; set; }

        /// <summary>
        /// 开启水印(true:启用;false:禁用;)
        /// </summary>
        public bool IsOpenWatermark { get; set; } = false;
    }

    public class Watermark
    {
        /// <summary>
        /// 水印类型(SystemText:系统文本;CustomizeText:自定义文本;Img:图片;)
        /// </summary>
        public string WatermarkType { get; set; }

        /// <summary>
        /// 旋转角度(取值范围[-360~360])
        /// </summary>
        public float Angle { get; set; } = 45;

        /// <summary>
        /// 水印尺寸
        /// </summary>
        public WatermarkSize WatermarkSize { get; set; } = new WatermarkSize();

        /// <summary>
        /// 水印透明度(取值范围[0~100],数值越小透明度越高)
        /// </summary>
        public int Transparency { get; set; } = 50;

        /// <summary>
        /// 水印位置(LT:左上;T:上;RT:右上;LC:左中;C:中;RC:右中;LB:左下;B:下;RB:右下;PP:平铺)
        /// </summary>
        public string Position { get; set; } = "RT";

        /// <summary>
        /// 水平距离
        /// </summary>
        public int X { get; set; } = 40;

        /// <summary>
        /// 垂直距离
        /// </summary>
        public int Y { get; set; } = 40;

        /// <summary>
        /// 文本
        /// </summary>
        public Text Text { get; set; } = new Text();

        /// <summary>
        /// 画布尺寸
        /// </summary>
        public CanvasSize CanvasSize { get; set; } = new CanvasSize();
    }

    public class WatermarkSize
    {
        /// <summary>
        /// 水印宽度
        /// </summary>
        public int WatermarkWidth { get; set; }

        /// <summary>
        /// 水印高度
        /// </summary>
        public int WatermarkHeight { get; set; }
    }

    public class Text
    {
        /// <summary>
        /// 文本类型(UserName:用户名;PhoneNumber:手机号;DateTime:当前时间;Customize:自定义;)
        /// </summary>
        public string TextType { get; set; }

        /// <summary>
        /// 文本内容
        /// </summary>
        public string TextContent { get; set; }

        /// <summary>
        /// 字体
        /// </summary>
        public string FontFamily { get; set; } = "宋体";

        /// <summary>
        /// 字号
        /// </summary>
        public float FontSize { get; set; } = 12;

        /// <summary>
        /// 字体颜色
        /// </summary>
        public string FontColor { get; set; } = "#000000";
    }

    public class CanvasSize
    {
        /// <summary>
        /// 画布宽度
        /// </summary>
        public int CanvasWidth { get; set; }

        /// <summary>
        /// 画布高度
        /// </summary>
        public int CanvasHeight { get; set; }
    }
}
