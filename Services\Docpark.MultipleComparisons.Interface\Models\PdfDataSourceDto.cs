﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class PdfDataSourceDto
    {
        public List<SourceDto> Sources { get; set; }
        public List<RepeaterSourceDto> RepeaterSources { get; set; }
    }

    public class SourceDto
    {
        public string Name { get; set; }
        public object Source { get; set; }
    }

    public class RepeaterSourceDto
    {
        public string Name { get; set; }
        public List<RepeaterSourceDetailDto> RepeaterSource { get; set; }
    }

    public class RepeaterSourceDetailDto
    {
        public string Abstract { get; set; }
        public string Subject { get; set; }
        public string Debit { get; set; }
        public string Credit { get; set; }
    }
}
