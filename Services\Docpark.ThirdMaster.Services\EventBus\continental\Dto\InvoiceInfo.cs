﻿using Docpark.HttpClientExtension.Json;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Org.BouncyCastle.Pqc.Crypto.Crystals.Kyber;
using System;

namespace Docpark.ThirdMaster.Services.EventBus.continental.Dto
{
    public class InvoiceInfo
    {
        public string MstId { get; set; }


        public string number { get; set; }

        public string code { get; set; }

        public string date { get; set; }

        public string buyer_tax_id { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public string type { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string tax_rate { get; set; }

        public string remark { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string PO { get; set; }

        /// <summary>
        /// 税前金额
        /// </summary>
        public string pretax_amount { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string tax { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public string total { get; set; }

        public string DN { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 入账日期
        /// </summary>
        public string dlrzrq { get; set; }

        /// <summary>
        /// 凭证号码
        /// </summary>
        public string dlpzhm { get; set; }

        /// <summary>
        /// 利率中心
        /// </summary>
        public string dllrzx { get; set; }

        /// <summary>
        /// 入账金额
        /// </summary>
        public string dlrzje { get; set; }

        public string StepName { get; set; }

        /// <summary>
        /// 返填时间
        /// </summary>
        public string BackfillDate { get; set; }
        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        public string seller_tax_id { get; set; }
        /// <summary>
        /// 业务类型ID
        /// </summary>
        public string businesstypeid { get; set; }

        /// <summary>
        /// 外币金额
        /// </summary>
        [JsonProperty("sp-je"), JsonConverter(typeof(CustomerJsonConvert<decimal>))]
        public decimal foreign_currency_amount { get; set; }

        /// <summary>
        /// 索赔金额
        /// </summary>
        [JsonProperty("SPJE"), JsonConverter(typeof(CustomerJsonConvert<decimal>))]
        public decimal SPJE { get; set; }


        public string pz_remark { get; set; }
    }
}
