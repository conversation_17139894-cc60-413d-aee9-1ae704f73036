﻿using Docpark.HttpClientExtension.Services;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IStorageService
    {
        Task<byte[]> GetDocument(string documentId);

        Task<DocumentInfo> GetDocumentInfo(string docId);

        Task<byte[]> DownloadOfdFirst(string docId);

        Task<byte[]> DownloadZip(string docId,int pageIndex);
    }

}
