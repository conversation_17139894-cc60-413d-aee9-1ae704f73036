using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 数据同步控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DataSyncController : ControllerBase
    {
        private readonly IDataSyncService _dataSyncService;
        private readonly ILogger<DataSyncController> _logger;

        public DataSyncController(
            IDataSyncService dataSyncService,
            ILogger<DataSyncController> logger)
        {
            _dataSyncService = dataSyncService;
            _logger = logger;
        }

        /// <summary>
        /// 创建同步配置
        /// </summary>
        /// <param name="config">同步配置</param>
        /// <returns>配置ID</returns>
        [HttpPost("configurations")]
        public async Task<IActionResult> CreateSyncConfiguration([FromBody] SyncConfiguration config)
        {
            try
            {
                var configId = await _dataSyncService.CreateSyncConfigurationAsync(config);
                return Ok(new { id = configId, message = "同步配置创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建同步配置失败");
                return StatusCode(500, new { message = "创建同步配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新同步配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <param name="config">同步配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("configurations/{id}")]
        public async Task<IActionResult> UpdateSyncConfiguration(string id, [FromBody] SyncConfiguration config)
        {
            try
            {
                var success = await _dataSyncService.UpdateSyncConfigurationAsync(id, config);
                if (success)
                {
                    return Ok(new { message = "同步配置更新成功" });
                }
                return NotFound(new { message = "同步配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新同步配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "更新同步配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取同步配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>同步配置</returns>
        [HttpGet("configurations/{id}")]
        public async Task<IActionResult> GetSyncConfiguration(string id)
        {
            try
            {
                var config = await _dataSyncService.GetSyncConfigurationAsync(id);
                if (config != null)
                {
                    return Ok(config);
                }
                return NotFound(new { message = "同步配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同步配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取同步配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据API配置ID获取同步配置
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>同步配置</returns>
        [HttpGet("configurations/by-api/{apiConfigId}")]
        public async Task<IActionResult> GetSyncConfigurationByApiConfigId(string apiConfigId)
        {
            try
            {
                var config = await _dataSyncService.GetSyncConfigurationByApiConfigIdAsync(apiConfigId);
                if (config != null)
                {
                    return Ok(config);
                }
                return NotFound(new { message = "同步配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据API配置ID获取同步配置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取同步配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除同步配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("configurations/{id}")]
        public async Task<IActionResult> DeleteSyncConfiguration(string id)
        {
            try
            {
                var success = await _dataSyncService.DeleteSyncConfigurationAsync(id);
                if (success)
                {
                    return Ok(new { message = "同步配置删除成功" });
                }
                return NotFound(new { message = "同步配置不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除同步配置失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除同步配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 执行增量同步
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="lastSyncTime">上次同步时间</param>
        /// <returns>同步结果</returns>
        [HttpPost("execute/incremental/{apiConfigId}")]
        public async Task<IActionResult> ExecuteIncrementalSync(string apiConfigId, [FromQuery] DateTime? lastSyncTime = null)
        {
            try
            {
                var session = await _dataSyncService.ExecuteIncrementalSyncAsync(apiConfigId, lastSyncTime);
                return Ok(new { 
                    sessionId = session.SessionId,
                    status = session.Status.ToString(),
                    message = "增量同步执行完成",
                    session = session
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行增量同步失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "执行增量同步失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 执行全量同步
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>同步结果</returns>
        [HttpPost("execute/full/{apiConfigId}")]
        public async Task<IActionResult> ExecuteFullSync(string apiConfigId)
        {
            try
            {
                var session = await _dataSyncService.ExecuteFullSyncAsync(apiConfigId);
                return Ok(new { 
                    sessionId = session.SessionId,
                    status = session.Status.ToString(),
                    message = "全量同步执行完成",
                    session = session
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行全量同步失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "执行全量同步失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取同步会话
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>同步会话</returns>
        [HttpGet("sessions/{sessionId}")]
        public async Task<IActionResult> GetSyncSession(string sessionId)
        {
            try
            {
                var session = await _dataSyncService.GetSyncSessionAsync(sessionId);
                if (session != null)
                {
                    return Ok(session);
                }
                return NotFound(new { message = "同步会话不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同步会话失败，会话ID: {SessionId}", sessionId);
                return StatusCode(500, new { message = "获取同步会话失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取同步会话历史
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="limit">限制数量</param>
        /// <returns>同步会话历史</returns>
        [HttpGet("sessions/history/{apiConfigId}")]
        public async Task<IActionResult> GetSyncSessionHistory(string apiConfigId, [FromQuery] int limit = 50)
        {
            try
            {
                var sessions = await _dataSyncService.GetSyncSessionHistoryAsync(apiConfigId, limit);
                return Ok(sessions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同步会话历史失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取同步会话历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取未解决的冲突
        /// </summary>
        /// <param name="apiConfigId">API配置ID（可选）</param>
        /// <returns>未解决的冲突列表</returns>
        [HttpGet("conflicts/unresolved")]
        public async Task<IActionResult> GetUnresolvedConflicts([FromQuery] string apiConfigId = null)
        {
            try
            {
                var conflicts = await _dataSyncService.GetUnresolvedConflictsAsync(apiConfigId);
                return Ok(conflicts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未解决冲突失败");
                return StatusCode(500, new { message = "获取未解决冲突失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 解决数据冲突
        /// </summary>
        /// <param name="conflictId">冲突ID</param>
        /// <param name="request">解决请求</param>
        /// <returns>解决结果</returns>
        [HttpPost("conflicts/{conflictId}/resolve")]
        public async Task<IActionResult> ResolveDataConflict(string conflictId, [FromBody] ConflictResolutionRequest request)
        {
            try
            {
                var success = await _dataSyncService.ResolveDataConflictAsync(conflictId, request.Strategy, request.ResolvedData);
                if (success)
                {
                    return Ok(new { message = "数据冲突解决成功" });
                }
                return NotFound(new { message = "数据冲突不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解决数据冲突失败，冲突ID: {ConflictId}", conflictId);
                return StatusCode(500, new { message = "解决数据冲突失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取同步统计信息
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>同步统计信息</returns>
        [HttpGet("statistics/{apiConfigId}")]
        public async Task<IActionResult> GetSyncStatistics(string apiConfigId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var statistics = await _dataSyncService.GetSyncStatisticsAsync(apiConfigId, startDate, endDate);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同步统计信息失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取同步统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重置同步状态
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>重置结果</returns>
        [HttpPost("reset/{apiConfigId}")]
        public async Task<IActionResult> ResetSyncState(string apiConfigId)
        {
            try
            {
                var success = await _dataSyncService.ResetSyncStateAsync(apiConfigId);
                if (success)
                {
                    return Ok(new { message = "同步状态重置成功" });
                }
                return BadRequest(new { message = "同步状态重置失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置同步状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "重置同步状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的同步数据
        /// </summary>
        /// <param name="olderThanDays">保留天数</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup")]
        public async Task<IActionResult> CleanupExpiredSyncData([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var deletedCount = await _dataSyncService.CleanupExpiredSyncDataAsync(olderThanDays);
                return Ok(new { 
                    message = "清理过期同步数据完成",
                    deletedCount = deletedCount,
                    olderThanDays = olderThanDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期同步数据失败");
                return StatusCode(500, new { message = "清理过期同步数据失败", error = ex.Message });
            }
        }
    }

    #region 请求模型

    /// <summary>
    /// 冲突解决请求
    /// </summary>
    public class ConflictResolutionRequest
    {
        public ConflictResolutionStrategy Strategy { get; set; }
        public object ResolvedData { get; set; }
    }

    #endregion
}
