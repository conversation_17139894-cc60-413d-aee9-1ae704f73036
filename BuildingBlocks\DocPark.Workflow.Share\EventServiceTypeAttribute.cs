﻿using System;
using System.Collections.Generic;
using System.Text;

namespace DocPark.Workflow.Share
{
    public class ServiceTypeAttribute : Attribute
    {
        /// <summary>
        /// 动作编码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 动作描述
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="code">服务编码</param>
        /// <param name="name">服务名称</param>
        public ServiceTypeAttribute(string code, string name)
        {
            Code = code;
            Name = name;
        }
    }

    public static class ServiceAttributeExtions
    {
        /// <summary>
        /// 获取服务的类型
        /// </summary>
        /// <param name="check"></param>
        /// <returns></returns>
        public static ServiceTypeAttribute GetServiceType(this IEventService check)
        {
            var type = check.GetType();
            return (ServiceTypeAttribute)type.GetCustomAttributes(typeof(ServiceTypeAttribute),false)[0];
        }
    }
}
