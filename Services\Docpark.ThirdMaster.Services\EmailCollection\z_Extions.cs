﻿using Docpark.ThirdMaster.Services.EmailCollection.BackgroundJob;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.EmailCollection
{
    public static class EmailCollectionExtions
    {
        public static IServiceCollection UseEmailCollection(this IServiceCollection services)
        {
            services.AddTransient<ICaseNoService, CaseNoService>();
            services.AddTransient<IEmailCollectionService, EmailCollectionService>();

            return services;
        }

        public static string GetValue(this Dictionary<string, object> item, string key)
        {
            if (item != null && item.ContainsKey(key) && item[key] != null)
                return item[key].ToString().Trim();
            return string.Empty;
        }
    }
}
