namespace Docpark.ThirdPartyIntegration.Domain.Enums
{
    /// <summary>
    /// 数据同步策略枚举
    /// </summary>
    public enum SyncStrategy
    {
        /// <summary>
        /// 全量同步 - 每次获取所有数据
        /// </summary>
        FullSync = 1,

        /// <summary>
        /// 增量同步 - 基于时间戳的增量更新
        /// </summary>
        IncrementalSync = 2,

        /// <summary>
        /// 差异同步 - 基于数据版本的差异更新
        /// </summary>
        DifferentialSync = 3,

        /// <summary>
        /// 检查点同步 - 基于检查点的断点续传
        /// </summary>
        CheckpointSync = 4,

        /// <summary>
        /// 混合同步 - 结合多种策略的智能同步
        /// </summary>
        HybridSync = 5
    }
}
