﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 摘要详情
    /// </summary>
    public class Summary_details
    {
        /// <summary>
        /// 摘要
        /// </summary>
        public string summary { get; set; }
        /// <summary>
        /// 科目名称
        /// </summary>
        public string accounting_title { get; set; }
        /// <summary>
        /// 会计科目代码
        /// </summary>
        public string accounting_code { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string dept_name { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public string dept_code { get; set; }
        /// <summary>
        /// 人员名称
        /// </summary>
        public string user_name { get; set; }
        /// <summary>
        /// 人员编码
        /// </summary>
        public string user_code { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string item_supplier_code { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string item_supplier_name { get; set; }
        /// <summary>
        /// 原币金额
        /// </summary>
        public string original_currency_amount { get; set; }
        /// <summary>
        /// 借方金额
        /// </summary>
        public string debit_amount { get; set; }
        /// <summary>
        /// 贷方金额
        /// </summary>
        public string credit_amount { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string item_company_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string item_company_code { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string item_product_name { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string item_product_code { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string item_project_name { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string item_project_code { get; set; }
        /// <summary>
        /// 内部往来名称
        /// </summary>
        public string item_current_name { get; set; }
        /// <summary>
        /// 内部往来编码
        /// </summary>
        public string item_current_code { get; set; }
    }
}
