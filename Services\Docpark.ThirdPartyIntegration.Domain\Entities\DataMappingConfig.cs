using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 数据映射配置
    /// </summary>
    public class DataMappingConfig
    {
        /// <summary>
        /// 是否启用数据映射
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 映射规则列表
        /// </summary>
        public List<MappingRule> Rules { get; set; } = new List<MappingRule>();

        /// <summary>
        /// 根路径（用于提取嵌套数据）
        /// </summary>
        [StringLength(500)]
        public string RootPath { get; set; }

        /// <summary>
        /// 是否保留原始数据
        /// </summary>
        public bool KeepOriginalData { get; set; } = true;

        /// <summary>
        /// 映射后的数据结构名称
        /// </summary>
        [StringLength(100)]
        public string MappedDataName { get; set; } = "mapped_data";
    }

    /// <summary>
    /// 映射规则
    /// </summary>
    public class MappingRule
    {
        /// <summary>
        /// 源字段路径（支持JSON Path）
        /// </summary>
        [Required]
        [StringLength(500)]
        public string SourcePath { get; set; }

        /// <summary>
        /// 目标字段名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string TargetField { get; set; }

        /// <summary>
        /// 数据类型转换
        /// </summary>
        public DataTransformType TransformType { get; set; } = DataTransformType.None;

        /// <summary>
        /// 转换参数（如日期格式、正则表达式等）
        /// </summary>
        [StringLength(500)]
        public string TransformParameter { get; set; }

        /// <summary>
        /// 默认值（当源字段不存在或为空时使用）
        /// </summary>
        [StringLength(500)]
        public string DefaultValue { get; set; }

        /// <summary>
        /// 是否必需字段
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// 字段描述
        /// </summary>
        [StringLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// 验证规则（正则表达式）
        /// </summary>
        [StringLength(500)]
        public string ValidationPattern { get; set; }

        /// <summary>
        /// 是否启用此规则
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// 数据转换类型
    /// </summary>
    public enum DataTransformType
    {
        /// <summary>
        /// 无转换
        /// </summary>
        None = 0,

        /// <summary>
        /// 字符串转换
        /// </summary>
        ToString = 1,

        /// <summary>
        /// 数字转换
        /// </summary>
        ToNumber = 2,

        /// <summary>
        /// 日期转换
        /// </summary>
        ToDateTime = 3,

        /// <summary>
        /// 布尔转换
        /// </summary>
        ToBoolean = 4,

        /// <summary>
        /// JSON字符串转对象
        /// </summary>
        JsonToObject = 5,

        /// <summary>
        /// 对象转JSON字符串
        /// </summary>
        ObjectToJson = 6,

        /// <summary>
        /// 正则表达式提取
        /// </summary>
        RegexExtract = 7,

        /// <summary>
        /// 字符串替换
        /// </summary>
        StringReplace = 8,

        /// <summary>
        /// 字符串分割
        /// </summary>
        StringSplit = 9,

        /// <summary>
        /// 数组连接
        /// </summary>
        ArrayJoin = 10,

        /// <summary>
        /// 自定义转换（通过脚本）
        /// </summary>
        Custom = 99
    }
}
