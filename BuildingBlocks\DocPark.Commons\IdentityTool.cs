﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace DocPark.Commons
{

    public interface IIdentityTool
    {
        string Token { get; }
        Task<string> Authenticate();
    }

    public class IdentityTool : IIdentityTool
    {
        public string Token => throw new System.NotImplementedException();

        public async Task<string> Authenticate()
        {
            try
            {
                var data = new Dictionary<string, string>();
                data["UserNameOrEmailAddress"] = ConfigHelper.Configuration["Authentication:UserName"];
                data["Password"] = ConfigHelper.Configuration["Authentication:Password"];
                data["RememberClient"] = "false";
                var client = new HttpClient();
                //var client = _clientFactory.CreateClient();
                string identityUrl = ConfigHelper.Configuration["IdentityUrl"];

                var context = new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");

                var responseMessage = await client.PostAsync((identityUrl.EndsWith("/") ? identityUrl : identityUrl + "/") +
                    "api/TokenAuth/Authenticate", context);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var str = await responseMessage.Content.ReadAsStringAsync();
                    var payload = JsonConvert.DeserializeObject<JObject>(str);
                    var result = payload["result"];
                    string token = result["accessToken"].ToString();
                    string userId = result["userId"].ToString();
                    return token;
                }
                return null;
            }
            catch (System.Exception ex)
            {

                return ex.Message;
            }
        }
    }
}
