using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// API执行日志服务接口
    /// </summary>
    public interface IApiExecutionLogService
    {
        /// <summary>
        /// 创建执行日志
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        Task<string> CreateAsync(ApiExecutionLog log);

        /// <summary>
        /// 更新执行日志
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        Task<bool> UpdateAsync(ApiExecutionLog log);

        /// <summary>
        /// 根据ID获取执行日志
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<ApiExecutionLog> GetByIdAsync(string id);

        /// <summary>
        /// 获取API配置的执行日志
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        Task<List<ApiExecutionLog>> GetByApiConfigIdAsync(string apiConfigId, int limit = 100);

        /// <summary>
        /// 根据条件查询执行日志
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="status"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        Task<List<ApiExecutionLog>> QueryAsync(
            string apiConfigId = null,
            ExecutionStatus? status = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            int limit = 100);

        /// <summary>
        /// 删除过期的执行日志
        /// </summary>
        /// <param name="olderThan"></param>
        /// <returns></returns>
        Task<long> DeleteOldLogsAsync(DateTime olderThan);
    }
}
