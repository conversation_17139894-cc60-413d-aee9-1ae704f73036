#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM registry.cn-shanghai.aliyuncs.com/gnsass/netcoresdk-nodejs-gdip:3.1 AS base
WORKDIR /app

EXPOSE 80
EXPOSE 443

FROM registry.cn-shanghai.aliyuncs.com/gnsass/netcoresdk-nodejs:3.1 AS build
WORKDIR /src
COPY ["Services/Docpark.MultipleComparisons.Interface/Docpark.MultipleComparisons.Interface.csproj", "Services/Docpark.MultipleComparisons.Interface/"]
RUN dotnet restore "Services/Docpark.MultipleComparisons.Interface/Docpark.MultipleComparisons.Interface.csproj"
COPY . .
WORKDIR "/src/Services/Docpark.MultipleComparisons.Interface"
RUN dotnet build "Docpark.MultipleComparisons.Interface.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Docpark.MultipleComparisons.Interface.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Docpark.MultipleComparisons.Interface.dll"]