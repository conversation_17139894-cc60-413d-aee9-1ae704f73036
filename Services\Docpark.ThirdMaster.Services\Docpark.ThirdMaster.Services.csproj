<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>ded3ac57-bd9d-40d1-bee5-c5ba527eceba</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.1.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.4" />
    <PackageReference Include="NPOI" Version="2.6.2" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.7.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.7.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.7.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.7.*" />
    <PackageReference Include="SharpCompress" Version="0.28.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.Commons\DocPark.Commons.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.CustomExtension\DocPark.CustomExtension.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.EmailService\DocPark.EmailService.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\Docpark.HttpClientExtension\Docpark.HttpClientExtension.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.Workflow.Share\DocPark.Workflow.Share.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ConfigXml\continental\Archive.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\continental\Deduction.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\continental\ExportInvoice.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\continental\VoucherRefilling.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\continental\供应商发票采集流程.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\continental\供应商发票采集流程_普票.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\PaperlessOffice\exportFileToFolder.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ConfigXml\PaperlessOffice\paperlessOfficeTestJob.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>


</Project>
