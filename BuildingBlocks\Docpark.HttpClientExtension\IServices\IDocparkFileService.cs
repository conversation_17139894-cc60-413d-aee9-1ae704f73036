﻿using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IDocparkFileService
    {
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        Task<ResultInfo<string>> UploadFile(KeyValuePair<string, byte[]> file);
        /// <summary>
        /// 获取文档信息
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        Task<DocumentFileDto> GetDocument(string docId);

        /// <summary>
        /// 批量获取文档信息
        /// </summary>
        /// <param name="documentIds"></param>
        /// <returns></returns>
        Task<List<DocumentFileDto>> GetDocuments(List<string> documentIds);

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        Task<(byte[], MediaTypeHeaderValue, string)> DowLoadFile(string docId);
        /// <summary>
        /// 下载带水印文件
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        Task<(byte[], MediaTypeHeaderValue, string)> DowLoadWatermarkFile(string docId);
        /// <summary>
        /// 获取ofd第一张图片
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        Task<byte[]> DownloadOfdFirst(string docId);
        /// <summary>
        /// 获取zip第一张图片
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        Task<byte[]> DownloadZipFirst(string docId);
    }
}
