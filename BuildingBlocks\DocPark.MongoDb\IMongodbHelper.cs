﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using MongoDB.Driver;

namespace DocPark.MongoDb
{
    public partial interface IMongodbManager
    {
        /// <summary>
        /// 获取延迟加载结果集Queryable
        /// </summary>
        /// <returns></returns>
        IQueryable<TEntity> GetQueryable<TEntity>();

        /// <summary>
        /// 根据条件查询获取第一条数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        TEntity GetEntity<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null);

        /// <summary>
        /// 根据条件查询数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        List<TEntity> GetList<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null);

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="orderBy"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        PageInfo<TEntity> GetPageList<TEntity>(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="queryable"></param>
        /// <param name="orderBy"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        PageInfo<TEntity> GetPageListByQueryable<TEntity>(IQueryable<TEntity> queryable, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 根据条件获取记录数
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<int> Count<TEntity>(Expression<Func<TEntity, bool>> filter = null);

        /// <summary>
        /// 添加数据
        /// </summary>
        /// <param name="entity"></param>
        Task<bool> Insert<TEntity>(TEntity entity);

        /// <summary>
        /// 批量添加数据
        /// </summary>
        /// <param name="entitys"></param>
        Task<bool> BulkCopy<TEntity>(IEnumerable<TEntity> entitys);

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="entity"></param>
        Task<bool> Update<TEntity>(Expression<Func<TEntity, bool>> filter, TEntity entity);

        /// <summary>
        /// 删除文档   
        /// </summary>
        /// <param name="filter"></param>
        Task<bool> Detele<TEntity>(Expression<Func<TEntity, bool>> filter = null);
    }
}
