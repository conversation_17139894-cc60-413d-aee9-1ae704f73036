﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models.Entity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NuonuoSDK;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class IdentiFicationService : IIdentiFicationService
    {
        private readonly ILogger logger;
        private readonly IConfiguration configuration;
        private readonly IObjectQuery objectQuery;
        private readonly IComHttpClient httpClient;
        private readonly IDocparkHostService docparkHostService;
        private readonly IProductService productService;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IDGrpc _dGrpc;

        public IdentiFicationService(ILogger<IdentiFicationService> _logger, IDGrpc dGrpc, IConfiguration _configuration, IObjectQuery _objectQuery, IComHttpClient _httpClient, IDocparkHostService _docparkHostService, IProductService _productService, IThirdPartyMongoDbService callLogService)
        {
            this.logger = _logger;
            this.configuration = _configuration;
            this.objectQuery = _objectQuery;
            this.httpClient = _httpClient;
            this.docparkHostService = _docparkHostService;
            this.productService = _productService;
            this.callLogService = callLogService;
            this._dGrpc = dGrpc;
        }

        //public NNInvoiceInspection(string _appKey, string _appSecret)
        //{
        //    var Identification_url = configuration.GetValue<string>("Identification_url");
        //    var inspectionSettings = new Model_NuonuoInvoiceInspectionSettings()
        //    {
        //        url = Identification_url,
        //        appkey = _appKey,
        //        appSecret = _appSecret
        //    };
        //}

        public Model_NuonuoInvoiceInspectionSettings GetInvoiceVerifySettingAsync(string _appKey, string _appSecret)
        {
            var Identification_url = configuration.GetValue<string>("Identification_url");
            Model_NuonuoInvoiceInspectionSettings setting = new Model_NuonuoInvoiceInspectionSettings
            {
                appkey = _appKey,
                appSecret = _appSecret,
                url = Identification_url
            };
            return setting;
        }


        /// <summary>
        /// 调用诺诺开放平台API
        /// </summary>
        /// <param name="taxnum">授权企业税号</param>
        /// <param name="inspContent">发票验真内容</param>
        /// <returns></returns>
        public string sendRequest(string _appKey, string _appSecret, string taxnum, Model_NuonuoInvoiceInspectionContent inspContent, out string errmsg)
        {

            errmsg = "";
            string token = "";
            logger.LogError("sendRequest");
            logger.LogError("sendRequest请求参数值：_appKey:" + _appKey + "_appSecret:" + _appSecret + "taxnum:" + taxnum + "_method:" + "inspContent:" + JsonConvert.SerializeObject(inspContent));
            string Identification_url = configuration.GetValue<string>("Identification_url");
            string senid = Guid.NewGuid().ToString("N").ToUpper(); // 唯一标识，由企业自己生成32位随机码
            string method = configuration.GetValue<string>("method_url");
            string content = JsonConvert.SerializeObject(inspContent);
            try
            {
                //获取token的接口地址
                var ocr_url = this.configuration.GetValue<string>("getToken_url");
                //获取token接口参数
                string company_code = taxnum;

                //获取token结果
                var toekn_Result = this.httpClient.PostByText(ocr_url, company_code);
                logger.LogError("获取token结果：" + toekn_Result);
                JObject jsonResult = JsonConvert.DeserializeObject<JObject>(toekn_Result);
                token = jsonResult.getString("msg");
                logger.LogError("token的值为：" + token);
            }
            catch (Exception e)
            {
                throw e;
            }
            logger.LogError("sendRequest请求参数值(2)：_appKey:" + _appKey + "_appSecret:" + _appSecret + "token:" + token + "taxnum:" + taxnum + "_method:" + method + "inspContent:" + content + "Identification_url:" + Identification_url + "senid:" + senid);
            string sendPost = NNOpenSDK.sendPostSyncRequest(Identification_url, senid, _appKey, _appSecret, token, taxnum, method, content);
            logger.LogError("sendRequest发送结果：" + sendPost);
            return sendPost;
        }

        /// <summary>
        /// 发票验真
        /// </summary>
        /// <param name="request_FileData"></param>
        /// <returns></returns>
        public async Task<(InvoiceOcrResultDtos model, string message)> VerifiyData(Request_FileData request_FileData)
        {
            string msg = "";
            var verify_DataModel = new InvoiceOcrResultDtos();
            verify_DataModel.verify_result = false;
            //诺诺接口地址
            //var Identification_url = configuration.GetValue<string>("Identification_url");
            //var method_url = configuration.GetValue<string>("method_url");
            //OCR的接口地址
            var ocr_url = this._dGrpc.GetStringAsync(ConstSettings.DocumentServiceApi).Result;
            //请求OCR接口参数
            var dic_Data = new Dictionary<string, object>();
            dic_Data["fileType"] = request_FileData.fileType;
            dic_Data["fileData"] = request_FileData.fileData;
            //获取ocr的识别结果
            var ocr_Result = this.httpClient.Post(ocr_url + "api/OCR/InvoiceOcr", dic_Data);
            if (!string.IsNullOrEmpty(ocr_Result))
            {
                JObject jsonResult = JsonConvert.DeserializeObject<JObject>(ocr_Result);
                var results = (JObject)jsonResult.getJObject("data").getJArray("identify_results")?.FirstOrDefault();
                var data_result = results.getJObject("details");
                var ocr_isSuccess = jsonResult.getString("isSuccess");
                if (ocr_isSuccess.ToLower() == "true")
                {
                    //获取公司名称
                    var companyName = data_result == null ? "" : data_result.getString("buyer");
                    //获取发票代码
                    var invoice_code = data_result == null ? "" : data_result.getString("code");
                    //获取获取发票编号
                    var invoice_no = data_result == null ? "" : data_result.getString("number");
                    //获取发票日期
                    var invoice_Date = data_result == null ? "" : data_result.getString("date");
                    //获取发票类别
                    var invoice_type = results.getString("type");
                    //获取价格合计
                    var total = data_result == null ? "" : data_result.getString("total");
                    //获取发票检验码
                    var check_code = data_result == null ? "" : data_result.getString("check_code");
                    var filterYd = new List<Filter>();
                    var sorters = new List<Sorter>();

                    filterYd.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "CompanyName",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { companyName }
                    });
                    //获取远东企业信息数据
                    var selectYdDataList = await this.objectQuery.GetList<YDCompanyFormData>(filterYd, sorters, 0, 1);
                    if (selectYdDataList.totalCount > 0 && selectYdDataList.data != null)
                    {
                        var invoiceDate = Convert.ToDateTime(invoice_Date).ToString("yyyy-MM-dd");
                        //判断发票类型    若为二手车发票，传参为车价合计  否则为校验码的后6位
                        var optionField = invoice_type == "10105" ? total : check_code.Length > 6 ? check_code.Substring(check_code.Length - 6) : check_code;
                        var verify_result = VerifyResult(selectYdDataList.data[0].CompanyCode, invoice_code, invoice_no, invoiceDate, optionField);

                        if (verify_result != null)
                        {
                            var data_ocr = JsonConvert.DeserializeObject<ResponseResult_Ocr>(ocr_Result);
                            var result = JsonConvert.DeserializeObject<Response_NuonuoInvoiceInspectionResult>(verify_result);
                            verify_DataModel.identify_results = data_ocr.Data == null ? null : data_ocr.Data.identify_results;
                            verify_DataModel.signature_results = data_ocr.Data == null ? null : data_ocr.Data.signature_results;
                            verify_DataModel.verify_result = result.code == "0000" ? true : false;
                            msg = result.describe;
                        }
                        else
                        {
                            msg = "验真流程失败";
                        }
                    }
                    else
                    {
                        msg = "公司信息错误或该发票不是增值税发票，请确认数据";
                    }
                }
                else
                {
                    msg = "识别服务错误，请查证发票信息";
                }
            }
            else
            {
                msg = "识别服务错误，请查证发票信息";
            }
            return (verify_DataModel, msg);
        }

        /// <summary>
        /// 诺诺验真
        /// </summary>
        /// <param name="taxnum"></param>
        /// <param name="invoiceCode"></param>
        /// <param name="invoiceNo"></param>
        /// <param name="invoiceDate"></param>
        /// <param name="optionfield"></param>
        /// <returns></returns>
        public string VerifyResult(string taxnum, string invoiceCode, string invoiceNo, string invoiceDate, string optionfield)
        {
            var dic = new Dictionary<string, object>();
            dic["taxnum"] = taxnum;
            dic["invoiceCode"] = invoiceCode;
            dic["invoiceNo"] = invoiceNo;
            dic["invoiceDate"] = invoiceDate;
            dic["optionField"] = optionfield;

            var verify_url = configuration.GetValue<string>("fk_verify_url");
            logger.LogError("验真接口调用地址为：" + verify_url);
            var res = this.httpClient.Post(verify_url, dic);
            logger.LogError("验真调用返回结果为：" + res);

            return res;
        }



    }
}