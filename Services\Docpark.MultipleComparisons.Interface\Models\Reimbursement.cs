﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 报销单数据信息
    /// </summary>
    [JsonTable("Reimbursement")]
    public class Reimbursement
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string business_type { get; set; }
        public Guid MstId { get; set; }
        /// <summary>
        /// 票夹Id
        /// </summary>
        public string tic_id { get; set; }
        /// <summary>
        /// 所属部门
        /// </summary>
        public string department { get; set; }
        /// <summary>
        /// 所属公司
        /// </summary>
        public string company { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>
        public string totalamount { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string applicant { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public string application_time { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string state { get; set; }
        /// <summary>
        /// 票夹预览地址
        /// </summary>
        public string wallet_path { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string ArchivesKey { get; set; }
        /// <summary>
        /// 凭证号
        /// </summary>
        public string VoucherNo { get; set; }
    }
}
