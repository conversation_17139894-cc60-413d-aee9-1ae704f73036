﻿using Docpark.ThirdMaster.Services.EmailCollection;
using Docpark.ThirdMaster.Services.Models.EmailCollection;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using DocPark.Commons;
using DocPark.EmailService;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("RefundMail", "发票退票")]
    public class RefundMailService : IEventService
    {

        private readonly ILogger<RefundMailService> logger;
        private readonly IObjectQuery objectQuery;
        private readonly IEventBus eventBus;
        private readonly IDGrpc grpc;
        private readonly IEmailCollectionService collectionService;
        private readonly IHttpClientFactory httpClientFactory;

        public RefundMailService(ILogger<RefundMailService> logger, IObjectQuery objectQuery, IEventBus eventBus, IDGrpc grpc, IEmailCollectionService collectionService, IHttpClientFactory httpClientFactory)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.eventBus = eventBus;
            this.grpc = grpc;
            this.collectionService = collectionService;
            this.httpClientFactory = httpClientFactory;
        }

        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            var resMessage = new StringBuilder();
            mqContent.TriggerStatus = true;
            logger.LogInformation("-----------------------------------开始发票退票------------------------------");
            try
            {
                var sBusinessType = await grpc.GetStringAsync("App.SupplierCollect.BusinessType");
                var cBusinessType = await grpc.GetStringAsync("App.CustomerClaim.BusinessType");

                var formData = await objectQuery.Get<Dictionary<string, object>>(new Guid(mqContent.MstId));
                if (IsNotNull(formData, "collect_type"))
                {
                    var collectType = formData["collect_type"].ToString();
                    //非邮箱收集
                    if (collectType != Enum_Collect_Type.SUPPLIER.ToString() && collectType != Enum_Collect_Type.CUSTOMER_CLAIM.ToString())
                    {
                        eventBus.Publish(new DocumentServiceMQEventData(JsonConvert.SerializeObject(mqContent)));
                        return string.Empty;
                    }
                }
                if (IsNotNull(formData, "businesstypeid"))
                {
                    if (IsNotNull(formData, "dlsjyx"))
                    {
                        var businessType = formData["businesstypeid"].ToString();
                        var collType = Enum_Collect_Type.SUPPLIER;
                        if (string.Compare(businessType, CommonUtil.ConvertObjectIdToGuid(cBusinessType).ToString(), true) == 0)
                        {
                            collType = Enum_Collect_Type.CUSTOMER_CLAIM;
                        }

                        var info = await collectionService.GetReceiveEmailInfo(collType);
                        var mkService = new MailKitService(logger, httpClientFactory)
                        {
                            Account = info.Account,
                            PassWord = info.PassWord,
                            ImapIdComond = info?.ImapIdComond?.ToLower() == "true",
                            ImapHost = info.ImapHost,
                            ImapPort = info.ImapPort,
                            SmtpHost = info.SmtpHost,
                            SmtpPort = info.SmtpPort
                        };

                        var email = formData["dlsjyx"].ToString();
                        var number = IsNotNull(formData, "number") ? formData["number"].ToString() : string.Empty;
                        var buyer = IsNotNull(formData, "buyer") ? formData["buyer"].ToString() : string.Empty;
                        var buyer_tax_id = IsNotNull(formData, "buyer_tax_id") ? formData["buyer_tax_id"].ToString() : string.Empty;
                        var reason = IsNotNull(formData, "dlthyy") ? formData["dlthyy"].ToString() : string.Empty;

                        var body = new StringBuilder();
                        body.AppendLine("您的发票被退回！发票信息如下：<br/>");
                        body.AppendLine($"发票号码:{number}<br/>");
                        body.AppendLine($"购买方名称:{buyer}<br/>");
                        body.AppendLine($"购买方税号:{buyer_tax_id} <br/>");
                        body.AppendLine($"退回原因:{reason} <br/>");

                        var log = new StringBuilder();
                        mkService.SendMail(email, "退票提醒", body.ToString(), ref log);
                        logger.LogError(log.ToString());
                    }
                    else
                    {
                        mqContent.TriggerStatus = false;
                        mqContent.TriggerMessage = "收件邮箱为空";
                    }
                }
                else
                {
                    mqContent.TriggerStatus = false;
                    mqContent.TriggerMessage = "业务类型为空";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "退票邮件异常");
                resMessage.AppendLine(ex.Message);

                mqContent.TriggerStatus = false;
                mqContent.TriggerMessage = "退票邮件异常";
            }

            var msg = JsonConvert.SerializeObject(mqContent);
            var publishEventData = new DocumentServiceMQEventData(msg);
            eventBus.Publish(publishEventData);

            logger.LogInformation("-----------------------------------结束发票退票------------------------------");

            return resMessage.ToString();
        }
        private bool IsNotNull(Dictionary<string, object> w, string key)
        {
            if (w.ContainsKey(key) && w[key] != null && !string.IsNullOrWhiteSpace(w[key].ToString()))
            {
                if (w[key].ToString() == "NO_AUTH")
                    return false;
                return true;
            }
            return false;
        }
    }
}
