﻿using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace DocPark.Commons
{
    public class CommonUtil
    {
        /// <summary>
        /// 判断User-Agent 是不是来自于手机
        /// </summary>
        /// <param name="ua"></param>
        /// <returns></returns>
        public static bool CheckAgentIsMobile(String ua)
        {
            String[] deviceArray = new String[] { "android", "iphone", "ipod", "ipad", "blackberry", "ucweb", "windows phone" };
            if (ua == null)
            {
                return false;
            }
            ua = ua.ToLower();
            foreach (var item in deviceArray)
            {
                if (ua.Contains(item))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 将ObjectId转成Guid
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static Guid ConvertObjectIdToGuid(string str)
        {
            using (MD5 md5Hash = MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(str));
                //转换成字符串，并取9到25位
                string sBuilder = BitConverter.ToString(data, 4, 8);
                //BitConverter转换出来的字符串会在每个字符中间产生一个分隔符，需要去除掉
                sBuilder = sBuilder.Replace("-", "").ToLower();

                return new Guid(Encoding.Default.GetBytes(sBuilder));
            }
        }
    }
}
