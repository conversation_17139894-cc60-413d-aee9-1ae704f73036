﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class ReimbursementService : IReimbursementService
    {
        private readonly ILogger logger;
        private readonly IConfiguration configuration;
        private readonly IObjectQuery objectQuery;
        private readonly IComHttpClient httpClient;
        private readonly IDocparkHostService docparkHostService;
        private readonly IProductService productService;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IDGrpc dGrpc;
        private readonly IMultipComparisonsService _multipComparisons;
        private readonly IComHttpClient _comHttpClient;

        private readonly string[] fileTypes = new string[] { "png", "jpg", "jpeg", "pdf", "ofd" };

        public ReimbursementService(ILogger<ReimbursementService> _logger, IConfiguration _configuration, IObjectQuery _objectQuery, IComHttpClient _httpClient, IDocparkHostService _docparkHostService, IProductService _productService, IThirdPartyMongoDbService callLogService, IDGrpc dGrpc, IMultipComparisonsService multipComparisons, IComHttpClient comHttpClient)
        {
            this.logger = _logger;
            this.configuration = _configuration;
            this.objectQuery = _objectQuery;
            this.httpClient = _httpClient;
            this.docparkHostService = _docparkHostService;
            this.productService = _productService;
            this.callLogService = callLogService;
            this.dGrpc = dGrpc;
            this._multipComparisons = multipComparisons;
            this._comHttpClient = comHttpClient;
        }

        /// <summary>
        /// 推送智能比对数据
        /// </summary>
        /// <param name="bill_nos"></param>
        /// <returns></returns>
        public async Task<(bool result, string msg)> SendDataTo_MC01(string[] bill_nos)
        {
            bool isSuccess = false;
            string request_data = "";
            List<bool> results = new List<bool>();
            string msg = "";
            string base64data = "";
            var filter = new List<Filter>();
            if (bill_nos.Length > 0 && bill_nos != null)
            {
                filter.Add(new Filter()
                {
                    DisplayType = "文本",
                    Field = "ArchivesKey",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = bill_nos
                });
                filter.Add(new Filter()
                {
                    DisplayType = "文本",
                    Field = "state",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "审批通过" }
                });

                //排序
                List<Sorter> sorters = new List<Sorter>();

                //获取单据主数据信息
                var listmaster = await objectQuery.GetList<Reimbursement>(filter, sorters, 0, 20);

                if (listmaster.totalCount > 0 && listmaster.data != null)
                {
                    var requestModel = await _comHttpClient.GetReimbursementConfig();
                    var thirdticketholderUrl = requestModel == null ? "" : requestModel.ThirdticketholderUrl; //_comHttpClient.GetApiHost("Reimbursement.ThirdticketholderUrl"); //this.configuration["thirdticketholderUrl"];
                    for (int i = 0; i < listmaster.totalCount; i++)
                    {
                        Dictionary<string, object> dic = new Dictionary<string, object>()
                        {
                            ["endDate"] = "",
                            ["folderId"] = listmaster.data[i].tic_id,
                            ["fphm"] = "",
                            ["pageIndex"] = 1,
                            ["pageSize"] = 10,
                            ["startDate"] = "",
                            ["status"] = new string[] { }
                        };
                        var request = httpClient.Post(thirdticketholderUrl + $"invoice/document/Search", dic);
                        if (request != null)
                        {
                            List<AnnexItems> annexList = new List<AnnexItems>();
                            //主数据信息的单据id
                            var masterid = Guid.NewGuid().ToString();
                            #region    发票信息
                            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(request);
                            var code = jsonResult.getString("code");
                            var result = jsonResult.getJObject("result");
                            var data = result.getJArray("data").ToString();
                            if (code == "1")//接口调用成功
                            {
                                List<Invoice_details> invoicelist = new List<Invoice_details>();
                                var resultdata = JsonConvert.DeserializeObject<List<MC_SCAN_RESULT_Model>>(data);
                                foreach (var item in resultdata)
                                {
                                    //该发票的guid
                                    var invoiceGuid = Guid.NewGuid().ToString();
                                    //传入参数documentid获取base64文件
                                    var imagedata = httpClient.DowLoadFile(item.documentId).Item1;
                                    //获取文件信息
                                    var FileType = httpClient.GetDocumentURL(item.documentId);
                                    //获取文件信息的格式
                                    JObject jsonResultType = JsonConvert.DeserializeObject<JObject>(FileType);
                                    var type = jsonResultType.getString("fileType").ToLower();
                                    if (string.IsNullOrEmpty(type))
                                    {
                                        var name = jsonResultType.getString("fileName").ToLower();
                                        type = name.Split('.')[1];
                                    }
                                    base64data = Convert.ToBase64String(imagedata);
                                    annexList.Add(new AnnexItems()
                                    {
                                        third_bill_guid = masterid,
                                        third_invoice_guid = invoiceGuid,
                                        annex_guid = Guid.NewGuid().ToString(),
                                        annex_details = base64data,
                                        img_data_filetype = type,
                                    });
                                    var dic_invoice = new Dictionary<string, object>()
                                    {
                                        ["mstIds"] = new string[] { item.id },
                                        ["FolderMstIds"] = new string[] { listmaster.data[i].tic_id }
                                    };

                                    var request_invoice = httpClient.Post(thirdticketholderUrl + $"invoice/document/GetInvoiceAttributes", dic_invoice);
                                    
                                    JObject jsonResultType_invoice = JsonConvert.DeserializeObject<JObject>(request_invoice);
                                    
                                    if (jsonResultType_invoice != null)
                                    {
                                        request_data = jsonResultType_invoice[item.id].ToString();
                                    }
                                    //获取meta数据
                                    var metaData = JsonConvert.DeserializeObject<List<MC_SCAN_IMAGE_RESULT_MetaData_Model>>(request_data);
                                    //发票代码
                                    var metaData_Code = metaData.Where(s => s.identity == "code").FirstOrDefault();
                                    //发票价格小写
                                    var metaData_Total = metaData.Where(s => s.identity == "total").FirstOrDefault();
                                    //发票类型
                                    var metaData_Type = metaData.Where(s => s.identity == "type").FirstOrDefault();
                                    //开票日期
                                    var metaData_Date = metaData.Where(s => s.identity == "date").FirstOrDefault();
                                    //发票号码
                                    var metaData_Number = metaData.Where(s => s.identity == "number").FirstOrDefault();


                                    //请求参数赋值
                                    var invoicmodel = new Invoice_details()
                                    {
                                        third_bill_guid = masterid,
                                        bill_no = listmaster.data[i].ArchivesKey,
                                        third_invoice_guid = invoiceGuid,
                                        third_type = metaData_Type == null ? "" : metaData_Type.value,
                                        code = metaData_Code == null ? "" : metaData_Code.value,
                                        number = metaData_Number == null ? "" : metaData_Number.value,
                                        //date = Convert.ToDateTime(metaData_Date == null ? DateTime.MinValue.ToString() : metaData_Date.value),
                                        date = metaData_Date == null ? "" : metaData_Date.value, 
                                        pretax_amount = metaData_Total == null ? 0.00 : Convert.ToDouble(metaData_Total.value),
                                        tax = 0,
                                        line_items = new List<MC_Invoice_Item>(),
                                        travel_details = new List<MC_Travel_Detail>(),
                                        total = metaData_Total == null ? 0.00 : Convert.ToDouble(metaData_Total.value),
                                        check_code = "",
                                        buyer = "",
                                        buyer_tax_id = "",
                                        seller = "",
                                        seller_tax_id = "",
                                        is_required_match = "01",
                                        image_mstid = item.mstId
                                    };

                                    if (string.IsNullOrEmpty(invoicmodel.date))
                                    {
                                        // 日期没有的, 默认显示当天时间
                                        invoicmodel.date = DateTime.Now.ToString("yyyy-MM-dd");
                                    }

                                    // 设置发票状态为: 报销中
                                    var setResult = await this._multipComparisons.SetImageInvoiceStatus(item.mstId, "2");
                                    if (!setResult)
                                    {
                                        return (false, $"更新影像发票状态为报销中失败, 发票号码({invoicmodel.number})影像标识:{item.mstId}");
                                    }

                                    invoicelist.Add(invoicmodel);
                                }
                                #endregion
                                //发票表须有数据
                                if (invoicelist.Count > 0)
                                {
                                    Request_MC01 request_MC01 = new Request_MC01()
                                    {
                                        third_bill_source = "报销单方传入",
                                        business_type = listmaster.data[i].business_type,
                                        third_bill_guid = masterid,
                                        bill_no = listmaster.data[i].ArchivesKey,
                                        third_bill_title = listmaster.data[i].applicant + "-" + listmaster.data[i].business_type,
                                        bill_submit_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        proposer_name = listmaster.data[i].applicant,
                                        proposer_code = "",
                                        proposer_dept_name = listmaster.data[i].department,
                                        proposer_dept_code = "",
                                        cost_center_name = "",
                                        cost_center_code = "",
                                        company_name = listmaster.data[i].company,
                                        company_code = "",
                                        supplier_name = "",
                                        supplier_code = "",
                                        business_status = listmaster.data[i].state,
                                        task_type = 1,
                                        total_amount = Convert.ToDouble(listmaster.data[i].totalamount),
                                        total_approved_amount = Convert.ToDouble(listmaster.data[i].totalamount),
                                        invoice_details = invoicelist,
                                        expense_details = new List<Expense_details>(),
                                        other_annex_items = new List<OtherAnnexItems>(),
                                        annex_items = annexList
                                    };

                                    var responseData = MC01(request_MC01);
                                    var isSuccessResult = JsonConvert.SerializeObject(responseData);

                                    var requestResult = JsonConvert.DeserializeObject<ResponseResult>(isSuccessResult);
                                    isSuccess = requestResult.isSuccess;
                                    results.Add(isSuccess);
                                    if (!isSuccess)
                                    {
                                        msg += $"{listmaster.data[i].ArchivesKey}：推送智能比对数据失败。";
                                    }
                                }
                                else
                                {
                                    results.Add(false);
                                    msg = "该票夹下无发票附件信息，请核对";
                                }
                            }
                            else
                            {
                                results.Add(false);
                                msg = "该票夹下信息错误，请核对";
                            }
                        }
                        else
                        {
                            results.Add(false);
                            msg = "该票夹下无发票附件信息，请核对";
                        }
                    }
                }
                else
                {
                    results.Add(false);
                    msg = "单据编号不存在或单据状态不为审批通过，请核对";
                }

                //判断结果是否存在false
                var isResults = results.Exists(s => s == false);
                if (isResults)
                {
                    return (false, msg);
                }
                else
                {
                    return (true, "推送智能比对数据成功");
                }
            }
            else
            {
                return (false, "单据号不正确，请核实数据");
            }
        }

        /// <summary>
        /// 单据比对成功修改报销单状态
        /// </summary>
        public async Task<bool> SuccessDataToReim(string[] bill_nos)
        {
            bool result = false;
            try
            {
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = bill_nos
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "business_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "审批通过" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "03" }
                });
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);
                //筛选出bill_no
                var bill_no_Array = selectMasterList.data.Select(s => s.bill_no).ToArray();

                var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "ArchivesKey",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = bill_no_Array
                });
                var selectReimList = await this.objectQuery.GetList<Reimbursement>(filter, sorters, 0, 50);

                if (selectReimList.totalCount > 0 && selectReimList.data != null)
                {
                    for (int i = 0; i < selectReimList.totalCount; i++)
                    {
                        selectReimList.data[i].state = "比对完成"; // 更改凭证是否发送的属性为: 已发送
                    }
                    result = await this.objectQuery.BulkCreateOrUpdate<Reimbursement>(selectReimList.data);
                }
                await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                {
                    ApiName = "报销单",
                    ApiDesc = "单据比对成功通知第三方业务系统API",
                    CallTime = DateTime.Now,
                    CallResult = result,
                    CallResultData = "返回结果result:" + result,
                    CallResultMsg = result ? "成功" : "失败",
                    RequestData = "单据编号:" + selectReimList.data[0].ArchivesKey + "单据类别：(0为通过，1为驳回)" + 0,
                    Remark = ""
                });
                return result;
            }
            catch (Exception ex)
            {
                logger.LogError("单据比对成功通知第三方业务系统API，执行异常，原因：" + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 推送二次比对的方法
        /// </summary>
        /// <param name="request_MC01"></param>
        /// <returns></returns>
        public ResponseResult MC01(Request_MC01 request_MC01)
        {
            var prevDate = DateTime.Now;
            TimeSpan diff = TimeSpan.Zero;
            StringBuilder stringBuilder = new StringBuilder();
            var responseResult = new ResponseResult();

            var issuccess = false;
            //主数据表单模型接收参数
            MC_BILL_MASTER_DATA mC_BILL_MASTER_DATA = new MC_BILL_MASTER_DATA()
            {
                bill_no = request_MC01.bill_no,
                third_bill_guid = request_MC01.third_bill_guid,
                third_bill_status = request_MC01.third_bill_status,
                third_bill_source = request_MC01.third_bill_source,
                business_type = request_MC01.business_type,
                third_bill_title = request_MC01.third_bill_title,
                proposer_code = request_MC01.proposer_code,
                proposer_name = request_MC01.proposer_name,
                bill_submit_time = request_MC01.bill_submit_time,
                proposer_dept_code = request_MC01.proposer_dept_code,
                proposer_dept_name = request_MC01.proposer_dept_name,
                cost_center_code = request_MC01.cost_center_code,
                cost_center_name = request_MC01.cost_center_name,
                company_code = request_MC01.company_code,
                company_name = request_MC01.company_name,
                expense_details = new List<MC_Expense_details>(),
                annex_items = new List<Annex_items>(),
                supplier_code = request_MC01.supplier_code,
                supplier_name = request_MC01.supplier_name,
                total_amount = request_MC01.total_amount,
                total_approved_amount = request_MC01.total_approved_amount,
                expense_voucher_no = request_MC01.expense_voucher_no,
                payment_voucher_no = request_MC01.payment_voucher_no,
                invoice_qty = request_MC01.invoice_details != null ? request_MC01.invoice_details.Count : 0,
                remark = request_MC01.remark,
                business_status = request_MC01.business_status,
                sys_status = configuration.GetValue<string>("bill_init_status") // 系统状态, 默认: 00 待处理
            };
            //主数据表单费用明细模型
            MC_Expense_details expense_Details = null;
            //主数据表单附件数据模型
            Annex_items annex_Items = null;
            //将MC01参数中的费用明细集合存入到主表单的费用明细模型中
            foreach (var item in request_MC01.expense_details)
            {
                if (item.third_bill_guid.ToString() == mC_BILL_MASTER_DATA.third_bill_guid)
                {
                    expense_Details = new MC_Expense_details();
                    expense_Details.expense_detail_id = item.expense_details_guid;
                    expense_Details.expense_type_code = item.expense_type_code;
                    expense_Details.expense_type_name = item.expense_type_name;
                    expense_Details.apply_amount = item.apply_amount;
                    expense_Details.approved_amount = item.approved_amount;
                    expense_Details.expense_remark = item.expense_remark;

                    //将费用明细模型存入到主表单费用明细集合中
                    mC_BILL_MASTER_DATA.expense_details.Add(expense_Details);
                }
            }
            //将MC01参数中的单据附件集合存入到主表单的附件数据集合模型中
            foreach (var item in request_MC01.other_annex_items)
            {
                var imgdata = string.Empty;
                if (!string.IsNullOrEmpty(item.annex_details))
                {
                    if (!string.IsNullOrEmpty(item.img_data_filetype))
                    {
                        // 2021-8-16 沟通单据其他附件, 不做类型限制
                        //if (this.fileTypes.Contains(item.img_data_filetype))
                        //{
                        var filetype = item.img_data_filetype;
                        imgdata = this._multipComparisons.UploadImage(item.annex_details, item.annex_guid, filetype).Result;
                        //}
                        //else
                        //{
                        //    imgdata = $"[其他附件({item.annex_guid})]传输的文件类型不符合要求";
                        //}
                    }
                    else
                    {
                        imgdata = "文件类型不能为空";
                    }
                }

                annex_Items = new Annex_items();
                annex_Items.annex_guid = item.annex_guid;
                annex_Items.annex_name = item.annex_name;
                annex_Items.annex_data_type = item.img_data_filetype;
                annex_Items.annex_details = imgdata;
                //将发票附件模型存入到主表单附件数据集合中
                mC_BILL_MASTER_DATA.annex_items.Add(annex_Items);
            }
            Guid bill_mstId = Guid.Empty;
            #region 验真 bill_no 是否已存在
            //查找单据信息
            var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
            filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
            {
                DisplayType = "文本",
                Field = "bill_no",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { request_MC01.bill_no }
            });
            var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();


            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("1. 主表单基础数据准备结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            var select_BillList = this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 1).Result;

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("2. 主表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            int ispost = 0;
            if (select_BillList.data != null && select_BillList.totalCount > 0)
            {
                bill_mstId = select_BillList.data[0].mstId;
                ispost = select_BillList.data[0].ispost;
            }
            else
            {
                bill_mstId = this.dGrpc.NewGuid();
            }
            mC_BILL_MASTER_DATA.ispost = request_MC01.task_type == 1 ? 0 : ispost;

            if (request_MC01.task_type != 2)
                mC_BILL_MASTER_DATA.match_mode = "01"; // 刚开始进入OA单据时, 设置为系统匹配

            //报销文件-附件
            var reim_file_details = string.Empty;
            if (!string.IsNullOrEmpty(request_MC01.reim_file_base64))
            {
                var filetype = request_MC01.reim_filetype;
                reim_file_details = this._multipComparisons.UploadImage(request_MC01.reim_file_base64, this.dGrpc.NewGuid().ToString(), filetype).Result;
            }
            mC_BILL_MASTER_DATA.reim_file_details = reim_file_details;
            mC_BILL_MASTER_DATA.reim_filetype = request_MC01.reim_filetype;
            #endregion

            //主数据是否接收成功
            var issucces = objectQuery.CreateOrUpdate<MC_BILL_MASTER_DATA>(bill_mstId, mC_BILL_MASTER_DATA).Result;

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("3. 主表单-数据保存结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            if (issucces)
            {
                List<MC_BILL_INVOICE_DATA> selectSavedInvoiceList = null;
                List<MC_BILL_MATCH_RESULT_DATA> selectSavedMatcList = null;
                if (select_BillList.data != null && select_BillList.totalCount > 0)
                {
                    #region 验真 bill_no 下是否已经保存了发票明细数据

                    //查找单据信息
                    filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                    filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "bill_no",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { request_MC01.bill_no }
                    });

                    sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();

                    diff = DateTime.Now - prevDate;
                    prevDate = DateTime.Now;
                    stringBuilder.Append("3.1. 发票明细表单-单据号查询前的监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                    var selectSavedInvoiceResult = this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filters, sorters, 0, 999).Result;
                    if (selectSavedInvoiceResult.data != null)
                    {
                        selectSavedInvoiceList = selectSavedInvoiceResult.data;
                    }

                    diff = DateTime.Now - prevDate;
                    prevDate = DateTime.Now;
                    stringBuilder.Append("3.2. 发票明细表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                    #endregion

                    #region 查找 bill_no 对应的发票比对结果数据, 应用于无需匹配的发票数据存储
                    var selectSavedMatchResult = this.objectQuery.GetList<MC_BILL_MATCH_RESULT_DATA>(filters, sorters, 0, 999).Result;
                    if (selectSavedMatchResult.data != null)
                    {
                        selectSavedMatcList = selectSavedMatchResult.data;
                    }

                    diff = DateTime.Now - prevDate;
                    prevDate = DateTime.Now;
                    stringBuilder.Append("3.3. 单据比对表单-单据号查询结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                    #endregion
                }

                List<Guid> toBeSaveInvoiceMstIds = new List<Guid>();
                List<MC_BILL_INVOICE_DATA> saveInvoiceDatas = new List<MC_BILL_INVOICE_DATA>();
                //单据发票凭证详情数据
                MC_BILL_INVOICE_DATA mC_BILL_INVOICE_DATA = null;
                //获取到发票明细集合中的数据
                foreach (var item in request_MC01.invoice_details)
                {
                    mC_BILL_INVOICE_DATA = new MC_BILL_INVOICE_DATA();
                    mC_BILL_INVOICE_DATA.bill_no = item.bill_no;
                    mC_BILL_INVOICE_DATA.third_bill_guid = item.third_bill_guid;
                    mC_BILL_INVOICE_DATA.bill_mstid = bill_mstId;
                    mC_BILL_INVOICE_DATA.third_invoice_guid = item.third_invoice_guid;
                    mC_BILL_INVOICE_DATA.third_type = item.third_type;
                    mC_BILL_INVOICE_DATA.type = item.third_type;
                    mC_BILL_INVOICE_DATA.code = item.code;
                    mC_BILL_INVOICE_DATA.number = item.number;
                    mC_BILL_INVOICE_DATA.date = item.date.ToString();
                    mC_BILL_INVOICE_DATA.pretax_amount = item.pretax_amount;
                    mC_BILL_INVOICE_DATA.tax = item.tax;
                    mC_BILL_INVOICE_DATA.items = item.line_items;
                    mC_BILL_INVOICE_DATA.total = item.total;
                    mC_BILL_INVOICE_DATA.annex_items = new List<Annex_items>();
                    mC_BILL_INVOICE_DATA.buyer = item.buyer;
                    mC_BILL_INVOICE_DATA.buyer_tax_id = item.buyer_tax_id;
                    mC_BILL_INVOICE_DATA.seller = item.seller;
                    mC_BILL_INVOICE_DATA.seller_tax_id = item.seller_tax_id;
                    mC_BILL_INVOICE_DATA.check_code = item.check_code;
                    mC_BILL_INVOICE_DATA.travel_details = item.travel_details;
                    mC_BILL_INVOICE_DATA.is_required_match = string.IsNullOrEmpty(item.is_required_match) ? "01" : item.is_required_match; // 是否必须匹配, 00 无需匹配, 01 需要匹配(默认值)
                    var selectAnnexItems = request_MC01.annex_items.Where(w => w.third_invoice_guid == item.third_invoice_guid).ToList();
                    if (selectAnnexItems != null)
                    {
                        foreach (var annexItem in selectAnnexItems)
                        {
                            var imgdata = string.Empty;
                            if (!string.IsNullOrEmpty(annexItem.annex_details))
                            {
                                if (!string.IsNullOrEmpty(annexItem.img_data_filetype))
                                {
                                    if (this.fileTypes.Contains(annexItem.img_data_filetype))
                                    {
                                        var filetype = annexItem.img_data_filetype;
                                        imgdata = this._multipComparisons.UploadImage(annexItem.annex_details, annexItem.annex_guid, filetype).Result;
                                    }
                                    else
                                    {
                                        imgdata = $"[发票附件({item.third_invoice_guid})]传输的文件类型不符合要求, 无法解析附件文件";
                                        return responseResult;
                                    }
                                }
                                else
                                {
                                    imgdata = "推送数据中文件类型为空值, 无法解析附件文件";
                                }
                            }

                            annex_Items = new Annex_items();
                            annex_Items.annex_guid = annexItem.annex_guid;
                            annex_Items.annex_data_type = annexItem.img_data_filetype;
                            annex_Items.annex_details = imgdata;
                            //将发票附件存到发票凭证详情表中
                            mC_BILL_INVOICE_DATA.annex_items.Add(annex_Items);
                        }
                    }

                    Guid invoice_mstId = this.dGrpc.NewGuid();
                    if (selectSavedInvoiceList != null && selectSavedInvoiceList.Count > 0)
                    {
                        var selectSavedInvoiceEntity = selectSavedInvoiceList.Where(w => w.third_invoice_guid == item.third_invoice_guid).FirstOrDefault();
                        if (selectSavedInvoiceEntity != null)
                        {
                            invoice_mstId = selectSavedInvoiceEntity.MstId;
                        }
                    }
                    mC_BILL_INVOICE_DATA.MstId = invoice_mstId;
                    toBeSaveInvoiceMstIds.Add(invoice_mstId);
                    saveInvoiceDatas.Add(mC_BILL_INVOICE_DATA);
                }

                diff = DateTime.Now - prevDate;
                prevDate = DateTime.Now;
                stringBuilder.Append("4. 发票明细表单-数据保存前的监控(发票数量: " + request_MC01.invoice_details.Count + "), 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

                #region 批量保存发票明细数据
                //单据发票凭证详情数据是否接收成功
                issuccess = this.objectQuery.BulkCreateOrUpdate<MC_BILL_INVOICE_DATA>(saveInvoiceDatas).Result;
                if (issuccess)
                {
                    #region 发票无需匹配设置
                    var saveInvoiceNotRequiredMatchList = saveInvoiceDatas.Where(w => w.is_required_match == "00").ToList();
                    if (saveInvoiceNotRequiredMatchList != null && saveInvoiceNotRequiredMatchList.Count > 0)
                    {
                        var saveInvoiceMatchResultDatas = new List<MC_BILL_MATCH_RESULT_DATA>();
                        foreach (var item in saveInvoiceNotRequiredMatchList)
                        {
                            Guid match_mstid = this.dGrpc.NewGuid();
                            if (selectSavedMatcList != null && selectSavedMatcList.Count > 0)
                            {
                                var noSavedMatchMstId = selectSavedMatcList.Where(w => w.inv_mstid == item.MstId.ToString()).Select(s => s.MstId).FirstOrDefault();
                                if (noSavedMatchMstId != null)
                                {
                                    match_mstid = Guid.Parse(noSavedMatchMstId);
                                }
                            }
                            MC_BILL_MATCH_RESULT_DATA mC_BILL_MATCH_RESULT_DATA = new MC_BILL_MATCH_RESULT_DATA();
                            mC_BILL_MATCH_RESULT_DATA.MstId = match_mstid.ToString();
                            mC_BILL_MATCH_RESULT_DATA.bill_no = item.bill_no;
                            mC_BILL_MATCH_RESULT_DATA.bill_mstid = bill_mstId.ToString();
                            mC_BILL_MATCH_RESULT_DATA.scan_mstid = "";
                            mC_BILL_MATCH_RESULT_DATA.inv_mstid = item.MstId.ToString();
                            mC_BILL_MATCH_RESULT_DATA.match_result = "01";
                            mC_BILL_MATCH_RESULT_DATA.is_auto_match = "01";
                            mC_BILL_MATCH_RESULT_DATA.match_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            mC_BILL_MATCH_RESULT_DATA.remark = "";

                            saveInvoiceMatchResultDatas.Add(mC_BILL_MATCH_RESULT_DATA);
                        }

                        issuccess = this.objectQuery.BulkCreateOrUpdate<MC_BILL_MATCH_RESULT_DATA>(saveInvoiceMatchResultDatas).Result;
                        if (!issuccess)
                        {
                            logger.LogError($"主数据(单据号: {request_MC01.bill_no})接收失败, 原因: 发票无需匹配设置。");
                        }

                        diff = DateTime.Now - prevDate;
                        prevDate = DateTime.Now;
                        stringBuilder.Append("4.1. [MC01]发票明细无需匹配比对设置保存结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                    }
                    #endregion

                    #region 删除不存在的发票明细数据
                    if (selectSavedInvoiceList != null && selectSavedInvoiceList.Count > 0)
                    {
                        var noSavedInvoiceMstIds = selectSavedInvoiceList.Where(w => !toBeSaveInvoiceMstIds.Contains(w.MstId)).Select(s => s.MstId).ToList();
                        if (noSavedInvoiceMstIds != null)
                        {
                            if (!this.objectQuery.RemoveAll(noSavedInvoiceMstIds).Result)
                            {
                                this.logger.LogError($"[MC01]删除单据({request_MC01.bill_no})下已保存的发票明细失败, 未能成功删除的发票明细[MSTID]: {JsonConvert.SerializeObject(noSavedInvoiceMstIds)}。");
                            }

                            diff = DateTime.Now - prevDate;
                            prevDate = DateTime.Now;
                            stringBuilder.Append("4.2. [MC01]删除单据({request_MC01.bill_no})下已保存的发票明细结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());
                        }
                    }
                    #endregion

                    responseResult.isSuccess = true;
                    responseResult.msg = "主数据接收成功";
                }
                else
                {
                    responseResult.msg = "主数据接收失败, errorCode: 1401";
                }
                #endregion

                diff = DateTime.Now - prevDate;
                prevDate = DateTime.Now;
                stringBuilder.Append("5. 发票明细表单-数据保存结束监控(发票数量: " + request_MC01.invoice_details.Count + "), 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            }
            else
            {
                responseResult.msg = "主数据接收失败, errorCode: 1402";
            }


            #region 添加调用日志
            this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
            {
                ApiName = "MC01",
                ApiDesc = "单据、发票及票据附件数据接收API",
                CallTime = DateTime.Now,
                CallResult = responseResult.isSuccess,
                CallResultData = responseResult.data != null ? JsonConvert.SerializeObject(responseResult.data) : "--",
                CallResultMsg = responseResult.msg,
                RequestData = JsonConvert.SerializeObject(request_MC01),
                Remark = ""
            });
            #endregion

            diff = DateTime.Now - prevDate;
            prevDate = DateTime.Now;
            stringBuilder.Append("6. [MC01]写入调用日志结束监控, 耗时(" + diff.ToString() + "): " + diff.TotalSeconds.ToString());

            this.logger.LogError($"[MC01]执行效率监控: {stringBuilder.ToString()}");

            return responseResult;
        }
    }
}
