﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>b60b7244-16f4-47a0-bb08-e9db87af02c5</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Temp\vhr.json" />
    <Content Remove="Temp\vhr_reimbursement.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Temp\vhr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Temp\vhr_reimbursement.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.14" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.Commons\DocPark.Commons.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.CustomExtension\DocPark.CustomExtension.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\Docpark.HttpClientExtension\Docpark.HttpClientExtension.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.MongoDb\DocPark.MongoDb.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Lib\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="NuonuoSDK">
      <HintPath>Lib\NuonuoSDK.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>


</Project>
