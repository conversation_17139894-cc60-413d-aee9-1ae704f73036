﻿using System;
using System.Collections.Generic;

namespace Docpark.ThirdMaster.Services.Models.EmailCollection
{
    public class EmailScanDataDto
    {
        /// <summary>
        /// 单据MstId
        /// </summary>
        public Guid MstId { get; set; }
        /// <summary>
        /// 单据号
        /// </summary>
        public string BillNo { get; set; }

        public List<EmailFileDocumentDto> Data { get; set; }
    }
    public class EmailFileDocumentDto
    {
        /// <summary>
        /// 单据MstId
        /// </summary>
        public Guid MstId { get; set; }
        /// <summary>
        /// 文档ID
        /// </summary>
        public string DocId { get; set; }
        /// <summary>
        /// 发件人邮箱地址
        /// </summary>
        public string SendAddress { get; set; }
        /// <summary>
        /// 邮件主题
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<FormFileDto> Files { get; set; }
    }
    public class FormFileDto
    {
        public string documentID { get; set; }
        public string name { get; set; }
        public int size { get; set; }
        public string type { get; set; }
        public string thumbUrl { get; set; }
    }

    public class AttachmentsTree
    {
        public AttachmentsTree() { }
        public AttachmentsTree(string fileName, string filePath)
        {
            FileName = fileName;
            FilePath = filePath;
        }
        public AttachmentsTree(string fphm, string fileName, string filePath)
        {
            FPHM = fphm;
            FileName = fileName;
            FilePath = filePath;
        }
        public string FPHM { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public List<AttachmentsTree> Children { get; set; } = new List<AttachmentsTree>();
    }
}
