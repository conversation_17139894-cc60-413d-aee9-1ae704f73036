﻿using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.HttpClientExtension.Services;
using DocPark.Commons;
using DocPark.V2.Web.Host.Grpc;
using GrpcDocpark;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Docpark.HttpClientExtension
{
    public static class CommExtensions
    {

        public static IServiceCollection AddDocparkHttpClient(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddGrpcClient<UserApi.UserApiClient>(o =>
            {
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true);
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2Support", true);
                o.Address = new Uri(configuration["Grpc_Host"]);
            });
            services.AddGrpcClient<UuidRPC.UuidRPCClient>(o =>
            {
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true);
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2Support", true);
                o.Address = new Uri(configuration["Grpc_Host"]);
            });
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<IDocparkHttpClient, DocparkHttpClient>();
            services.AddTransient<IDocparkHostService, DocparkHostService>();
            services.AddTransient<IDocparkMetaDataService, DocparkMetaDataService>();
            services.AddTransient<IDGrpc, DGrpc>();
            services.AddTransient<IObjectQuery, ObjectQuery>();
            services.AddTransient<IStorageService, StorageService>();
            services.AddTransient<IDocumentWorkflowService, DocumentWorkflowService>();
            services.AddTransient<IDocparkFileService, DocparkFileService>();
            services.AddTransient<IComHttpClient, ComHttpClient>();
            return services;
        }

        public static string ToUrl(this string api, string baseAddress)
        {
            return String.Format("{0}{1}", baseAddress.EndsWith('/') ? baseAddress : baseAddress + "/", api);
        }


        public static Dictionary<string, object> ConvertoFormData(this Dictionary<string, object> values, List<DocFormField> fields)
        {
            var fData = new Dictionary<string, object>();
            var formData = new Dictionary<string, object>();
            //默认币种
            foreach (var item in values)
            {
                var field = fields.Where(w => w.Identity == item.Key || w.CustomType == item.Key).FirstOrDefault();
                if (field != null)
                {
                    if (item.Value != null)
                    {
                        fData[field.Id] = item.Value;
                    }
                }
                else
                {
                    //子行项目
                    var parent = fields.Where(w => w.PIdentity == item.Key).FirstOrDefault();
                    if (parent != null)
                    {
                        if (item.Value != null)
                        {
                            if (item.Value.GetType().Name == typeof(List<Dictionary<string, object>>).GetType().Name
                                || item.Value.GetType().Name == typeof(JArray).Name
                                )
                            {
                                List<Dictionary<string, object>> _details = new List<Dictionary<string, object>>();
                                List<Dictionary<string, object>> details = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(item.Value.ToString());
                                foreach (var detail in details)
                                {
                                    var rowData = detail.ConvertoFormData(fields);
                                    if (rowData.ContainsKey("data")) 
                                    _details.Add((Dictionary<string, object>)rowData["data"]);
                                }
                                formData[parent.Parent] = _details;
                            }
                        }
                    }
                }
            }
            formData["data"] = fData;
            return formData;
        }
    }
}
