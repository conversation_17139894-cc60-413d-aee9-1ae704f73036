﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.Json
{
    public class CustomerJsonConvert<T> : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return true;
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                return serializer.Deserialize<T>(reader);
            }
            catch { }
            return default(T);
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
        {
            serializer.Serialize(writer, value);
        }
    }
}
