﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Model_ThirdpartyUser
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 标示该值可以作为数据存储的主健
        /// </summary>
        public string lunid { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 组织架构类型,org(机构),dept(部门),group(群组),post(岗位),person(人员)
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string no { get; set; }
        /// <summary>
        /// 手机号,仅当type为person时,有此信息
        /// </summary>
        public string mobileNo { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string sex { get; set; }
        /// <summary>
        /// 登录名,仅当type为person时,有此信息
        /// </summary>
        public string loginName { get; set; }
        /// <summary>
        /// 邮件地址,仅当type为person时,有此信息
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 能否登录
        /// </summary>
        public bool canLogin { get; set; }
        /// <summary>
        /// 密码,base64加密后的信息,仅当type为person时,有此信息
        /// </summary>
        public string password { get; set; }
        /// <summary>
        /// 登录账号小写
        /// </summary>
        public string loginNameLower { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string order { get; set; }
        /// <summary>
        /// 是否有效,该属性决定该组织架构是否删除
        /// </summary>
        public bool isAvailable { get; set; }
        /// <summary>
        /// 自定义属性，仅当type为person时,有此信息
        /// </summary>
        public Dictionary<string, object> customProps { get; set; }
        /// <summary>
        /// 父节点ID
        /// </summary>
        public string parent { get; set; }
        /// <summary>
        /// 岗位
        /// </summary>
        public List<string> posts { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string docCreator { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool isExternal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string alterTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hierarchyId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool isBusiness { get; set; }
    }
}
