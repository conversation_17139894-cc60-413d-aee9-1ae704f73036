﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 发票明细
    /// </summary>
    public class Invoice_details
    {
        /// <summary>
        /// 单据id
        /// </summary>
        [Required(ErrorMessage = "单据ID必填")]
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 所属单据id
        /// </summary>
        [Required(ErrorMessage = "单据编号必填")]
        public string bill_no { get; set; }
        /// <summary>
        /// 发票明细id
        /// </summary>
        [Required(ErrorMessage = "发票ID必填")]
        public string third_invoice_guid { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        [Required(ErrorMessage = "发票类型必填")]
        public string third_type { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        [Required(ErrorMessage = "发票号码必填")]
        public string number { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string date { get; set; }
        /// <summary>
        /// 税前金额
        /// </summary>
        public Double pretax_amount { get; set; }
        /// <summary>
        /// 发票税额
        /// </summary>
        public Double tax { get; set; }
        /// <summary>
        /// 发票行明细
        /// </summary>
        public List<MC_Invoice_Item> line_items { get; set; }
        /// <summary>
        /// 发票差旅详情
        /// </summary>
        public List<MC_Travel_Detail> travel_details { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        [Required(ErrorMessage = "价税合计必填")]
        public Double total { get; set; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string check_code { get; set; }
        /// <summary>
        /// 购方名称
        /// </summary>
        public string buyer { get; set; }
        /// <summary>
        /// 购方税号
        /// </summary>
        public string buyer_tax_id { get; set; }
        /// <summary>
        /// 销方名称
        /// </summary>
        public string seller { get; set; }
        /// <summary>
        /// 销方税号
        /// </summary>
        public string seller_tax_id { get; set; }
        /// <summary>
        /// 是否必须匹配, 00 无需匹配, 01 需要匹配
        /// </summary>
        public string is_required_match { get; set; }
        /// <summary>
        /// 发票影像标识
        /// </summary>
        /// <remarks>
        /// 发票报销时从票池中选择的发票标识
        /// </remarks>
        [Required(ErrorMessage = "发票影像标识必填")]
        public string image_mstid { get; set; }
    }

}
