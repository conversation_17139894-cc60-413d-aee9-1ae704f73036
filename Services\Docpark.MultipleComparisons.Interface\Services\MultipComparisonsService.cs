﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class MultipComparisonsService : IMultipComparisonsService
    {
        private readonly ILogger logger;
        private readonly IConfiguration configuration;
        private readonly IObjectQuery objectQuery;
        private readonly IComHttpClient httpClient;
        private readonly IDocparkHostService docparkHostService;
        private readonly IProductService productService;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IDGrpc dGrpc;

        public MultipComparisonsService(ILogger<MultipComparisonsService> _logger, IConfiguration _configuration, IObjectQuery _objectQuery, IComHttpClient _httpClient, IDocparkHostService _docparkHostService, IProductService _productService, IThirdPartyMongoDbService callLogService, IDGrpc _dGrpc)
        {
            this.logger = _logger;
            this.configuration = _configuration;
            this.objectQuery = _objectQuery;
            this.httpClient = _httpClient;
            this.docparkHostService = _docparkHostService;
            this.productService = _productService;
            this.callLogService = callLogService;
            this.dGrpc = _dGrpc;
        }

        /// <summary>
        /// 获取单据影像预览HTML页面地址
        /// </summary>
        /// <param name="bill_no">单据编号</param>
        /// <param name="mark">预览数据标识, 1 单据发票附件预览, 2 单据扫描影像预览</param>
        /// <returns></returns>
        public string GetBillPreviewHtml(string bill_no, int mark)
        {
            string url = "";
            try
            {
                var thirdparty_img_preview_host = configuration.GetValue<string>("thirdparty_img_preview_host");
                url = $"{thirdparty_img_preview_host.TrimEnd('/')}/#/img/imagePreview?bill_no={bill_no}&mark={mark}";
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return url;
        }


        /// <summary>
        /// 删除单据下的表单数据
        /// </summary>
        /// <param name="delMark">0 删除扫描影像数据, 1 删除单据下所有的数据</param>
        /// <param name="bill_no">单据编号</param>
        /// <returns>返回执行结果</returns>
        public async Task<(bool result, string errMsg)> RemoveBillDataAsync(int delMark, string bill_no)
        {
            bool result = false;
            string errMsg = "";
            try
            {
                //查找单据信息
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { bill_no.Trim() }
                });
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var select_BillList = this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 1).Result;
                if (select_BillList.data != null && select_BillList.totalCount > 0)
                {
                    // 存储所有需要修改发票状态的影像发票标识
                    List<string> image_mstids = new List<string>();
                    List<Guid> delMstIds = new List<Guid>();
                    var bill_mstid = select_BillList.data[0].mstId;
                    if (delMark == 1)
                    {
                        // 删除单据mstid
                        delMstIds.Add(bill_mstid);

                        //查找单据发票明细数据
                        filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                        filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "bill_mstid",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { bill_mstid.ToString() }
                        });
                        sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                        var billInvoiceList = this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filters, sorters, 0, 999).Result;
                        if (billInvoiceList.data != null && billInvoiceList.totalCount > 0)
                        {
                            delMstIds.AddRange(billInvoiceList.data.Select(x => x.MstId));
                            image_mstids.AddRange(billInvoiceList.data.Select(x => x.image_mstid));
                        }

                        //查询单据发票比对结果数据
                        var billMatchResultList = this.objectQuery.GetList<MC_BILL_MATCH_RESULT_DATA>(filters, sorters, 0, 999).Result;

                        //查询单据凭证数据
                        filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                        filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "oa_flow_code",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { bill_no.Trim() }
                        });
                        sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                        var billVoucherList = this.objectQuery.GetList<Dictionary<string, object>>("MC_BILL_VOUCHER_DATA", filters, sorters, 0, 999).Result;
                        if (billVoucherList.data != null && billVoucherList.totalCount > 0)
                        {
                            delMstIds.AddRange(billVoucherList.data.Select(x => Guid.Parse(x["MstId"].ToString())));
                        }

                        //查询单据电子回单数据
                        filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                        filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "oa_flow_code",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { bill_no.Trim() }
                        });
                        sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                        var billEleReceiptList = this.objectQuery.GetList<Dictionary<string, object>>("MC_BILL_ELE_RECEIPT_DATA", filters, sorters, 0, 999).Result;
                        if (billEleReceiptList.data != null && billEleReceiptList.totalCount > 0)
                        {
                            delMstIds.AddRange(billEleReceiptList.data.Select(x => Guid.Parse(x["MstId"].ToString())));
                        }
                    }

                    //查找单据发票匹配结果数据, 并删除
                    filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                    filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "bill_mstid",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { bill_mstid.ToString() }
                    });
                    sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                    var billMatchList = this.objectQuery.GetList<MC_BILL_MATCH_RESULT_DATA>(filters, sorters, 0, 999).Result;
                    if (billMatchList.data != null && billMatchList.totalCount > 0)
                    {
                        delMstIds.AddRange(billMatchList.data.Select(x => Guid.Parse(x.MstId)));
                    }

                    // 删除单据扫描影像数据
                    var billScanImageResults = this.GetBillScanImageResults(bill_no, false, out errMsg);
                    if (billScanImageResults.billListResult != null && billScanImageResults.billListResult.Count > 0 && string.IsNullOrEmpty(errMsg))
                    {
                        var ids = billScanImageResults.billListResult.Select(s => s.MstId).ToList();
                        var delResult = await this.DelBillDetailAsync(ids);

                        if (delResult)
                        {
                            if (delMark != 1)
                            {
                                //将主表中的条码编码置空
                                var saveData = new Dictionary<string, string>();
                                saveData["image_barcode"] = "";
                                saveData["match_mode"] = "01"; // 设置默认值: 系统匹配
                                await this.objectQuery.CreateOrUpdate<Dictionary<string, string>>("MC_BILL_MASTER_DATA", bill_mstid, saveData);
                            }
                        }
                    }

                    //开始删除
                    result = this.objectQuery.RemoveAll(delMstIds).Result;

                    if (!result)
                    {
                        errMsg = (delMark == 1 ? "删除单据下所有的数据" : "删除扫描影像数据") + "失败";
                    }
                    else
                    {
                        if (delMark == 1 && image_mstids != null && image_mstids.Count > 0)
                        {
                            StringBuilder sb_FailedMstId = new StringBuilder();
                            foreach (var mstid in image_mstids)
                            {
                                // 设置发票状态为: 未报销
                                var setResult = await this.SetImageInvoiceStatus(mstid, "1");
                                if (!setResult)
                                {
                                    sb_FailedMstId.Append($"{mstid}; ");
                                }
                            }
                            if (sb_FailedMstId.Length > 0)
                            {
                                logger.LogError($"发起人废弃单据(编号: {bill_no})时, 存在设置发票状态失败的影像发票, 如下: {sb_FailedMstId.ToString()}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //errMsg = (delMark == 1 ? "删除单据下所有的数据" : "删除扫描影像数据") + "失败, 原因: " + ex.Message;

                throw ex;
            }

            return (result, errMsg);
        }

        /// <summary>
        /// 获取单据扫描的影像信息
        /// </summary>
        /// <param name="bill_no">单据编号</param>
        /// <param name="isQueryDocTypeCode">是否查询二次比对业务类型的文档类型标识</param>
        /// <param name="errMsg">输出返回的错误信息</param>
        /// <returns>返回(扫描单据列表数据, 扫描单据影像列表数据)</returns>
        public (List<ScanFolderItemDto> billListResult, List<MC_SCAN_IMAGE_RESULT_Model> billScanListResult) GetBillScanImageResults(string bill_no, bool isQueryDocTypeCode, out string errMsg)
        {
            List<ScanFolderItemDto> billListResult = new List<ScanFolderItemDto>();
            List<MC_SCAN_IMAGE_RESULT_Model> billScanListResult = new List<MC_SCAN_IMAGE_RESULT_Model>();

            try
            {
                errMsg = "";
                var multCompSettings = this.productService.MultCompSettings;
                var mc_ScanInvoiceBusinessTypeId = multCompSettings["mc_ScanInvoiceBusinessTypeId"].ToString();  // 二次比对-影像扫描业务类型ID

                var billDocTypeList = this.GetMCScanDocumentTypeList(mc_ScanInvoiceBusinessTypeId, out errMsg);

                var mc_ScanAttachmentDocumentTypeId = ""; // 二次比对-附件扫描文档类型ID
                var mc_ScanInvoiceDocumentTypeId = ""; // 二次比对-发票扫描文档类型ID
                if (billDocTypeList != null && string.IsNullOrEmpty(errMsg))
                {
                    var invoiceDocTypeEntity = billDocTypeList.Where(x => x.identity.ToLower() == "invoice").FirstOrDefault();
                    if (invoiceDocTypeEntity != null)
                    {
                        mc_ScanInvoiceDocumentTypeId = invoiceDocTypeEntity.id;
                    }

                    var attachmentDocTypeEntity = billDocTypeList.Where(x => x.identity.ToLower() == "attachment").FirstOrDefault();
                    if (attachmentDocTypeEntity != null)
                    {
                        mc_ScanAttachmentDocumentTypeId = attachmentDocTypeEntity.id;
                    }
                }
                var selectItemTypeIds = new string[] { mc_ScanInvoiceDocumentTypeId, mc_ScanAttachmentDocumentTypeId };

                var scanDocuments = this.productService.GetScanFolderItems(mc_ScanInvoiceBusinessTypeId, new List<string>() { bill_no }, selectItemTypeIds).Result;

                billListResult.AddRange(scanDocuments);

                List<Guid> documentIds = new List<Guid>();
                foreach (var item in scanDocuments)
                {
                    documentIds.AddRange(item.DocumentIds);
                }

                //获取文档的元素据
                var relationships_metadata = this.docparkHostService.GetRelationships(ConstObject.DocumentAndScanMetaDataRelationships, documentIds).Result;

                Dictionary<Guid, string> itemTypeObjIds = new Dictionary<Guid, string>();
                for (int i = 0; i < selectItemTypeIds.Length; i++)
                {
                    itemTypeObjIds.Add(this.ConvertObjectIdToGuid(selectItemTypeIds[i]), selectItemTypeIds[i]);
                }

                relationships_metadata.AsParallel().ForAll(async item =>
                {
                    var billDetail = new MC_SCAN_IMAGE_RESULT_Model();
                    billDetail.id = item.ObjUniqueId.ToString();

                    var documentTypeId = itemTypeObjIds.ContainsKey(item.ObjId) ? itemTypeObjIds[item.ObjId] : string.Empty;

                    if (!string.IsNullOrEmpty(documentTypeId))
                    {
                        // 获取单据中所有的文档元数据
                        var formData = await this.docparkHostService.GetFormData<FormDataDto>(documentTypeId, item.ObjUniqueId.ToString());

                        billDetail.docId = item.TargetObjInstanceId.ToString();
                        billDetail.docType = documentTypeId;
                        billDetail.scanner = formData.CreatorUserName;
                        billDetail.createDateTime = formData.CreationTime;
                        billDetail.status = string.Empty;
                        var resultShortSummary = await this.docparkHostService.GetShortSummary(item.ObjUniqueId.ToString());
                        //调用获取元数据简报信息
                        billDetail.metaData = resultShortSummary.Code == ResultCode.Success ? resultShortSummary.Result.Data : string.Empty;
                        billDetail.mstId = item.ObjUniqueId.ToString();
                        lock (billScanListResult)
                        {
                            billScanListResult.Add(billDetail);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                //errMsg = ex.Message;
                //billListResult = null;
                //billScanListResult = null;

                throw ex;
            }

            return (billListResult, billScanListResult);
        }

        /// <summary>
        /// 上传附件Base64文件, 返回附件文件预览地址
        /// </summary>
        /// <param name="inputBase64"></param>
        /// <param name="annexMstId"></param>
        /// <returns></returns>
        public async Task<string> UploadImage(string inputBase64, string annexMstId, string filetype)
        {
            var imgFileUrl = "";
            try
            {
                if (!string.IsNullOrEmpty(inputBase64))
                {
                    if (inputBase64.IndexOf(',') >= 0)
                        inputBase64 = inputBase64.Split(',')[1];
                    byte[] bytes = Convert.FromBase64String(inputBase64);

                    if (string.IsNullOrEmpty(annexMstId))
                    {
                        annexMstId = Guid.NewGuid().ToString();
                    }

                    //上传图片
                    var result = await this.httpClient.UploadFile(new KeyValuePair<string, byte[]>($"{annexMstId}." + filetype.TrimStart('.'), bytes));

                    if (!string.IsNullOrEmpty(result.fileDownloadUrl) && string.IsNullOrEmpty(result.errMsg))
                    {
                        imgFileUrl = result.fileDownloadUrl;
                    }
                }
            }
            catch
            {
            }

            return imgFileUrl;
        }

        /// <summary>
        /// 获取二次比对文档类型列表信息
        /// </summary>
        /// <param name="businessTypeId">业务类型ID</param>
        /// <param name="errMsg">输出返回的错误信息</param>
        /// <returns>返回二次比对文档类型列表</returns>
        private List<MC_SCAN_DOCUMENT_TYPES_RESULT_Model> GetMCScanDocumentTypeList(string businessTypeId, out string errMsg)
        {
            List<MC_SCAN_DOCUMENT_TYPES_RESULT_Model> result = null;

            try
            {
                errMsg = "";
                var documentServiceHost = this.httpClient.GetApiHost("App.Api.DocumentService.Host");

                //获取二次比对下的文档类型标识
                var url_GetDocumentTypeList = string.Format("{0}/api/Business/GetDocumentTypeList?id={1}", documentServiceHost.TrimEnd('/'), businessTypeId);

                var result_GetDocumentTypeList = this.httpClient.Get(url_GetDocumentTypeList);
                if (string.IsNullOrWhiteSpace(result_GetDocumentTypeList))
                {
                    errMsg = $"获取二次比对的文档类型列表数据失败。";
                }
                else
                {
                    result = JsonConvert.DeserializeObject<List<MC_SCAN_DOCUMENT_TYPES_RESULT_Model>>(result_GetDocumentTypeList);
                }
            }
            catch (Exception ex)
            {
                //errMsg = ex.Message;
                //result = null;

                throw ex;
            }

            return result;
        }


        /// <summary>
        /// 删除扫描单据详情数据
        /// </summary>
        /// <param name="ids">单据id</param>
        /// <returns></returns>
        private async Task<bool> DelBillDetailAsync(List<string> ids)
        {
            try
            {
                if (ids != null && ids.Count > 0)
                {
                    var documentIds = await this.docparkHostService.GetTargetObjInstanceIds(ids.Select(s => new Guid(s)).ToList());
                    foreach (var documentId in documentIds)
                    {
                        await this.docparkHostService.RemoveRelationships(documentId);
                    }

                    foreach (var id in ids)
                    {
                        await this.docparkHostService.RemoveRelationships(new Guid(id));
                    }

                    //删除文档元数据
                    await this.docparkHostService.BatchRemove(ids);

                    return true;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return false;
        }


        /// <summary>
        /// 将ObjectId转成Guid
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private Guid ConvertObjectIdToGuid(string str)
        {
            using (MD5 md5Hash = MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(str));
                //转换成字符串，并取9到25位
                string sBuilder = BitConverter.ToString(data, 4, 8);
                //BitConverter转换出来的字符串会在每个字符中间产生一个分隔符，需要去除掉
                sBuilder = sBuilder.Replace("-", "").ToLower();

                return new Guid(Encoding.Default.GetBytes(sBuilder));
            }
        }

        /// <summary>
        /// 获取远东-电子档案的单点登录Token
        /// </summary>
        /// <returns></returns>
        public string GetEleArchivesLoginToken()
        {
            string token = "";

            try
            {
                string apiUrl = this.configuration["formal_ele_archives_settings:formal_token:formal_api"]; //openapi
                string appCode = this.configuration["formal_ele_archives_settings:formal_token:formal_appCode"]; //openapi账号
                string appSecret = this.configuration["formal_ele_archives_settings:formal_token:formal_appSecret"]; //openapi密码

                TimeSpan diff = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
                var timestamp = Math.Round(diff.TotalMilliseconds);
                var sha = System.Security.Cryptography.SHA256.Create();
                var hash = sha.ComputeHash(Encoding.Default.GetBytes(appSecret + ":" + appCode + ":" + timestamp));
                string secret = null;
                for (int i = 0; i < hash.Length; i++)
                {
                    secret += hash[i].ToString("x2");
                }

                var inputData = new Dictionary<string, object>() { ["userCode"] = appCode, ["secretToken"] = secret, ["timestamp"] = timestamp };

                string resData = this.httpClient.PostJson(apiUrl, inputData);
                if (!string.IsNullOrEmpty(resData))
                {
                    var resJsonData = JsonConvert.DeserializeObject<JObject>(resData);

                    if (resJsonData["code"].ToString() == "200")
                    {
                        token = ((JObject)resJsonData["data"])["token"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return token;
        }

        /// <summary>
        /// 设置影像发票状态
        /// </summary>
        /// <param name="mstId">影像MstId</param>
        /// <param name="status">影像发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销</param>
        /// <returns></returns>
        public async Task<bool> SetImageInvoiceStatus(string mstId, string status)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(mstId) && !string.IsNullOrEmpty(status))
            {
                //发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销
                result = await objectQuery.CreateOrUpdate<Dictionary<string, object>>("Receipt", Guid.Parse(mstId), new Dictionary<string, object>()
                {
                    ["invoice_status"] = status
                });
            }
            return result;
        }

        #region 归档接口推送逻辑
        /// <summary>
        /// 推送0A单据归档数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_OA_Bill_Archives_Data(string eleLoginToken)
        {
            var result = false;
            try
            {
                //获取请求的api地址
                string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_oa_bill_archive_url"]; // 0A单据归档数据推送API
                logger.LogError("推送0A单据归档数据的URL：" + thirdparty_api);

                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "10" }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);

                if (selectMasterList.totalCount > 0 && selectMasterList.data != null)
                {
                    var selectBillNoArray = selectMasterList.data.Select(s => s.bill_no).ToArray();

                    #region 查询所有OA单据的凭证详情
                    filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                    filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "oa_flow_code",
                        Method = "in",
                        Mode = "in",
                        Type = "Text",
                        Values = selectBillNoArray
                    });

                    sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                    var selectBillVoucherList = await this.objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 999);
                    #endregion

                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";
                    var saveMasterDatas = new List<MC_BILL_MASTER_DATA>();
                    if (selectBillVoucherList.totalCount > 0 && selectBillVoucherList.data != null)
                    {
                        foreach (var item in selectMasterList.data)
                        {
                            apiResResult = false;

                            #region 准备-0A单据归档数据推送的实体
                            var pushData_10 = new Request_OA_Bill_Archive_Data()
                            {
                                fd_apply_date = item.bill_submit_time,
                                fd_company_name = item.company_name,
                                fd_company_code = item.company_code,
                                fd_subject = item.third_bill_title,
                                fd_claimant_no = item.proposer_code,
                                fd_claimant_name = item.proposer_name,
                                fk_ewm = item.bill_no,
                                fd_supplier_code = item.supplier_code,
                                fd_supplier_name = item.supplier_name,
                                fd_claimant_dept_name = item.proposer_dept_name,
                                fd_claimant_dept_no = item.proposer_dept_code,
                                fd_status = item.third_bill_status,
                                fd_catagory = item.business_type,
                                fd_number = item.bill_no,
                                fd_id = item.third_bill_guid,
                                fd_sys_flag = item.third_bill_source,
                                fd_total_pay_money = item.total_approved_amount,
                                fd_total_approved_money = item.total_amount,
                                fd_content = item.remark,
                                fdDetailList = null,
                                fdInvoiceList = null,
                                fdinvoiceFileList = null,
                                fdOtherFileList = new List<FdOtherFileList>(),
                                field_3 = new List<Field_3>(),
                                voucherNo = selectBillVoucherList.data != null && selectBillVoucherList.totalCount > 0 ? selectBillVoucherList.data.Select(s => s.voucher_no).ToList() : new List<string>(),
                                fd_attribute_one = "",
                                fd_attribute_two = "",
                            };

                            if (item.expense_details != null)
                            {
                                // 添加费用明细 
                                foreach (var item_expense in item.expense_details)
                                {
                                    pushData_10.field_3.Add(new Field_3()
                                    {
                                        fd_id = item_expense.expense_detail_id,
                                        fd_apply_money = item_expense.apply_amount,
                                        fd_approved_money = item_expense.approved_amount,
                                        fd_expense_item_code = item_expense.expense_type_code,
                                        fd_expense_item_name = item_expense.expense_type_name,
                                        fd_over_standar_reason = item_expense.expense_remark,
                                        fd_attribute_one = "",
                                        fd_attribute_two = ""
                                    });
                                }
                            }

                            if (item.annex_items != null)
                            {
                                // 添加单据附件
                                foreach (var item_annex in item.annex_items)
                                {
                                    pushData_10.fdOtherFileList.Add(new FdOtherFileList()
                                    {
                                        type = item_annex.annex_data_type,
                                        name = item_annex.annex_name,
                                        file = GetBase(item_annex.annex_details)
                                    });
                                }
                            }

                            #endregion
                            apiRequestData = JsonConvert.SerializeObject(pushData_10);
                            logger.LogError("推送0A单据归档数据请求参数：" + apiRequestData);
                            apiResData = this.httpClient.PostJson(thirdparty_api, pushData_10, "Authorization", $"Bearer {eleLoginToken}");
                            logger.LogError("推送0A单据归档数据返回结果：" + apiResData);
                            if (!string.IsNullOrEmpty(apiResData))
                            {
                                var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                                apiResResult = eleArchivePushResult.data.ToString().ToLower() == "true" ? true : false;
                                result = apiResResult;
                                apiResMsg = eleArchivePushResult.errMsg;
                                if (eleArchivePushResult.code == 0)
                                {
                                    item.sys_status = "11"; // 归档,OA单据已发送, OA单据已经发送给电子档案后的系统状态
                                    saveMasterDatas.Add(item);
                                }

                            }

                            //写入调用日志
                            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                            {
                                ApiName = "OA_BILL_PUSH",
                                ApiDesc = "0A单据归档数据推送API",
                                CallTime = DateTime.Now,
                                CallResult = apiResResult,
                                CallResultData = apiResData,
                                CallResultMsg = apiResMsg,
                                RequestData = apiRequestData,
                                Remark = "调用API: " + thirdparty_api
                            });

                        }
                    }

                    if (saveMasterDatas != null && saveMasterDatas.Count > 0)
                    {
                        await this.objectQuery.BulkCreateOrUpdate<MC_BILL_MASTER_DATA>(saveMasterDatas);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("推送0A单据归档数据的Push_OA_Bill_Archives_Data错误：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 推送0A单据发票明细归档数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_OA_Invoice_Archives_Data(string eleLoginToken)
        {
            var result = false;
            try
            {
                //获取请求的api地址
                string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_oa_invoice_archive_url"]; // 0A单据发票归档数据推送API
                logger.LogError("推送0A单据发票明细归档数据URL：" + thirdparty_api);
                //获取扫描影像预览API
                var preview_url = this.dGrpc.GetStringAsync(ConstSettings.DocumentServiceApi).Result;
                var mark = 2;
                //string url = $"{preview_url}" + "api/MultipleComparisons/GetPreviewTreeData" + $"?bill_no={bills}" + $"&&mark={mark}";
                //var apipreview_Data = this.httpClient.Get(url);

                //JObject jsonResult = JsonConvert.DeserializeObject<JObject>(apipreview_Data);
                //var scanData = jsonResult.getJObject("data");
                //var children_list = scanData.getString("children");
                //var scanlist = JsonConvert.DeserializeObject<List<MC_PREVIEW_TREE_DATA_Model>>(children_list);
                //List<string> a = new List<string>();
                //a.AddRange(scanlist.Select(s => s.imageUrlOrBase64).ToList());

                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "business_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "审批通过" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "11" }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);

                if (selectMasterList.totalCount > 0 && selectMasterList.data != null)
                {
                    var selectBillNoArray = selectMasterList.data.Select(s => s.bill_no).ToArray();

                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";
                    var saveMasterDatas = new List<MC_BILL_MASTER_DATA>();
                    foreach (var item in selectMasterList.data)
                    {
                        apiResResult = false;

                        #region 准备-单据发票归档数据推送的实体
                        //1. 查询单据下的所有发票详情数据
                        var filterinvoice = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                        filterinvoice.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "bill_mstid",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { item.mstId.ToString() }
                        });
                        filterinvoice.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                        {
                            DisplayType = "文本",
                            Field = "is_required_match",
                            Method = "eq",
                            Mode = "eq",
                            Type = "Text",
                            Values = new string[] { "00" }
                        });

                        sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                        var selectBillInvoiceList = await this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filterinvoice, sorters, 0, 50);

                        var pushData_11 = new List<Request_OA_Bill_Invoice_Archive_Data>();
                        if (selectBillInvoiceList.data != null && selectBillInvoiceList.totalCount > 0)
                        {
                            foreach (var item_inv in selectBillInvoiceList.data)
                            {
                                var basedata = new List<string>();
                                var baseinvoice = item_inv.annex_items == null ? null : GetListBase(item_inv.annex_items.Select(s => $"url@{s.annex_details}").ToList());
                                basedata.AddRange(baseinvoice);
                                string url = $"{preview_url}" + "api/MultipleComparisons/GetPreviewTreeData" + $"?bill_no={item_inv.bill_no}" + $"&&mark={mark}";
                                var apipreview_Data = this.httpClient.Get(url);

                                JObject jsonResult = JsonConvert.DeserializeObject<JObject>(apipreview_Data);
                                var scanData = jsonResult.getJObject("data");
                                var children_list = scanData.getString("children");
                                var scanlist = JsonConvert.DeserializeObject<List<MC_PREVIEW_TREE_DATA_Model>>(children_list);
                                var scan_preview_list = GetListBase(scanlist.Select(s => $"url@{s.imageUrlOrBase64}").ToList());
                                if (scan_preview_list != null)
                                {
                                    basedata.AddRange(scan_preview_list);
                                }

                                pushData_11.Add(new Request_OA_Bill_Invoice_Archive_Data()
                                {
                                    type = this.GetEleArchiveInvoiceType(item_inv.type),
                                    invoiceCode = item_inv.code,
                                    invoiceNumber = item_inv.number,
                                    issueDate = Convert.ToDateTime(item_inv.date).ToString("yyyy-MM-dd"),
                                    totalPriceAmount = item_inv.pretax_amount.ToString(),
                                    totalTaxAmount = item_inv.tax.ToString(),
                                    lineItems = item_inv.items,
                                    totalPriceAndTax = item_inv.total.ToString(),
                                    checkCode = item_inv.check_code,
                                    buyerName = item_inv.buyer,
                                    buyerTaxNumber = item_inv.buyer_tax_id,
                                    supplierName = item_inv.seller,
                                    supplierTaxNumber = item_inv.seller_tax_id,
                                    invoiceDetail = item_inv.travel_details != null && item_inv.travel_details.Count > 0 ? item_inv.travel_details[0] : new MC_Travel_Detail(),
                                    //base64 = item_inv.annex_items.Select(s => $"url@{s.annex_details}").ToList(),
                                    //base64 = item_inv.annex_items == null ? null: item_inv.annex_items.Select(s => $"url@{s.annex_details}").ToList(),//GetListBase
                                    base64 = basedata,
                                    voucherNos = null,
                                });
                            }
                        }
                        #endregion
                        apiRequestData = JsonConvert.SerializeObject(pushData_11);
                        logger.LogError("推送0A单据发票明细归档数据请求参数：" + apiRequestData);
                        apiResData = this.httpClient.PostJson(thirdparty_api, pushData_11, "Authorization", $"Bearer {eleLoginToken}");
                        logger.LogError("推送0A单据发票明细归档数据返回数据：" + apiResData);
                        if (!string.IsNullOrEmpty(apiResData))
                        {
                            var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                            apiResResult = eleArchivePushResult.code == 0 ? true : false;
                            result = apiResResult;
                            apiResMsg = eleArchivePushResult.errMsg;
                            if (eleArchivePushResult.code == 0)
                            {
                                var pushDetails = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(eleArchivePushResult.data.ToString());
                                if (pushDetails != null)
                                {
                                    bool isUpdate = true;
                                    for (int i = 0; i < pushDetails.Count; i++)
                                    {
                                        if (pushDetails[i].ContainsKey("success") && !Convert.ToBoolean(pushDetails[i]["success"]))
                                        {
                                            isUpdate = false;
                                            break;
                                        }
                                    }
                                    if (isUpdate)
                                    {
                                        item.sys_status = "12"; // 归档,OA单据发票已发送, OA单据发票已经发送给电子档案后的系统状态
                                        saveMasterDatas.Add(item);
                                    }
                                }
                            }

                        }

                        //写入调用日志
                        await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                        {
                            ApiName = "OA_INVOICE_PUSH",
                            ApiDesc = "0A单据发票归档数据推送API",
                            CallTime = DateTime.Now,
                            CallResult = apiResResult,
                            CallResultData = apiResData,
                            CallResultMsg = apiResMsg,
                            RequestData = apiRequestData,
                            Remark = "调用API: " + thirdparty_api
                        });

                    }

                    if (saveMasterDatas != null && saveMasterDatas.Count > 0)
                    {
                        await this.objectQuery.BulkCreateOrUpdate<MC_BILL_MASTER_DATA>(saveMasterDatas);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("推送0A单据发票明细归档数据的错误为Push_OA_Invoice_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 推送凭证归档数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_Voucher_Archives_Data(string eleLoginToken)
        {
            var result = false;
            try
            {
                //获取请求的api地址
                string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_voucher_archive_url"]; // 凭证归档数据推送API

                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "ne",
                    Mode = "ne",
                    Type = "number",
                    Values = new string[] { "1" }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectVoucherList = await this.objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 50);

                if (selectVoucherList.totalCount > 0 && selectVoucherList.data != null)
                {
                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";
                    var saveVoucherDatas = new List<MC_BILL_VOUCHER_DATA>();
                    var pushData = new List<Request_Voucher_Archive_Data>();
                    foreach (var item in selectVoucherList.data)
                    {
                        apiResResult = false;

                        #region 准备-凭证归档数据推送的实体
                        var addItem = new Request_Voucher_Archive_Data()
                        {
                            voucherPeriod = item.accounting_period,
                            voucherDate = item.accounting_date,
                            voucherNo = item.voucher_no,
                            preparedBy = item.voucher_creator_name,
                            reviewer = item.voucher_approver_name,
                            businessEntityCode = item.company_code,
                            businessEntityName = item.company_name,
                            buzDeclare = item.remark,
                            voucherType = item.voucher_type,
                            lines = new List<Lines>(),
                            custom = new Custom()
                            {
                                batchName = item.batch_name,
                                oaprocesscode = item.oa_flow_code,
                                journalName = "",
                                classificationZha = item.voucher_ledger,
                                voucherlabor = item.voucher_creator_job_num,
                                auditabor = item.voucher_approver_job_num,
                                logZha = item.accounting_name,
                                totalDebit = item.debit_total_amount,
                                totallCredit = item.credit_total_amount,
                                totalAs = item.voucher_total_amount,
                                reversalStatus = item.write_off_status,
                                source = item.voucher_source,
                                batchStatus = item.batch_status
                            }
                        };
                        foreach (var item_summary in item.summary_details)
                        {
                            addItem.lines.Add(new Lines()
                            {
                                deptCode = item_summary.dept_code,
                                deptName = item_summary.dept_name,
                                transactionUnitCode = item_summary.item_current_code,
                                transactionUnitName = item_summary.item_current_name,
                                projectCode = item_summary.item_project_code,
                                projectName = item_summary.item_project_name,
                                employeeCode = item_summary.user_code,
                                employeeName = item_summary.user_name,
                                subjectCode = item_summary.account_code,
                                subjectName = item_summary.accounting_title,
                                merchantCode = item_summary.item_supplier_code,
                                merchantName = item_summary.item_supplier_name,
                                voucherAbstract = item_summary.summary,
                                custom = new Lines_Custom()
                                {
                                    productCode = item_summary.item_product_code,
                                    productName = item_summary.item_product_name
                                },
                                debitAmount = ToDouble(item_summary.debit_amount),
                                creditAmount = ToDouble(item_summary.credit_amount),
                                originalAmount = ToDouble(item_summary.original_currency_amount),
                                currency = item.currency
                            });
                        }
                        pushData.Add(addItem);
                        #endregion
                        apiRequestData = JsonConvert.SerializeObject(pushData);
                        logger.LogError("推送凭证归档数据请求参数Push_Voucher_Archives_Data：" + apiRequestData);
                        apiResData = this.httpClient.PostJson(thirdparty_api, pushData, "Authorization", $"Bearer {eleLoginToken}");
                        logger.LogError("凭证推送接口api为：" + thirdparty_api);
                        logger.LogError("凭证推送接口报文参数为：" + pushData);
                        logger.LogError("凭证推送接口Token值为：Bearer " + eleLoginToken);
                        logger.LogError("Push_Voucher_Archives_Data方式是推送凭证归档数据请求的返回结果：" + apiResData);
                        //获取凭证接口返回数据
                        JObject jsonResult = JsonConvert.DeserializeObject<JObject>(apiResData);
                        //获取返回数据是否存在错误信息
                        var errorArray = jsonResult.getJArray("data");
                        logger.LogError("推送凭证接口获取返回数据是否存在错误信息：" + errorArray);
                        if (errorArray == null)
                        {
                            var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                            apiResResult = eleArchivePushResult.code == 0 ? true : false;
                            result = apiResResult;
                            apiResMsg = eleArchivePushResult.errMsg;
                            if (eleArchivePushResult.code == 0)
                            {
                                var pushDetails = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(eleArchivePushResult.data.ToString());
                                if (pushDetails != null)
                                {
                                    bool isUpdate = true;
                                    for (int i = 0; i < pushDetails.Count; i++)
                                    {
                                        if (pushDetails[i].ContainsKey("success") && !Convert.ToBoolean(pushDetails[i]["success"]))
                                        {
                                            isUpdate = false;
                                            break;
                                        }
                                    }
                                    if (isUpdate)
                                    {
                                        item.ispost = 1; // 更改凭证是否发送的属性为: 已发送
                                        saveVoucherDatas.Add(item);
                                    }
                                }
                            }
                        }

                        //写入调用日志
                        await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                        {
                            ApiName = "VOUCHER_PUSH",
                            ApiDesc = "凭证归档数据推送API",
                            CallTime = DateTime.Now,
                            CallResult = apiResResult,
                            CallResultData = apiResData,
                            CallResultMsg = apiResMsg,
                            RequestData = apiRequestData,
                            Remark = "调用API: " + thirdparty_api
                        });

                    }

                    if (saveVoucherDatas != null && saveVoucherDatas.Count > 0)
                    {
                        await this.objectQuery.BulkCreateOrUpdate<MC_BILL_VOUCHER_DATA>(saveVoucherDatas);
                    }
                }

            }
            catch (Exception ex)
            {
                logger.LogError("推送凭证归档数据的错误为Push_Voucher_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 推送电子回单归档数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_Ele_Receipt_Archives_Data(string eleLoginToken)
        {
            var result = false;
            try
            {
                //获取请求的api地址
                string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_ele_receipt_archive_url"]; // 电子回单归档数据推送API
                var apiResResult = false;
                var apiResData = "";
                var apiResMsg = "";
                var apiRequestData = "";

                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "ne",
                    Mode = "ne",
                    Type = "number",
                    Values = new string[] { "1" }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectEleReceiptList = await this.objectQuery.GetList<MC_BILL_ELE_RECEIPT_DATA>(filters, sorters, 0, 1);

                if (selectEleReceiptList.data != null && selectEleReceiptList.totalCount > 0)
                {
                    var saveEleReceiptDatas = new List<MC_BILL_ELE_RECEIPT_DATA>();
                    var pushData = new List<Request_EleReceipt_Archive_Data>();

                    var addItem = new Request_EleReceipt_Archive_Data()
                    {
                        accountNo = selectEleReceiptList.data[0].our_bank_no,//************
                        transDate = Convert.ToDateTime(selectEleReceiptList.data[0].trades_time).ToString("yyyy-MM-hh"),
                        remarks = selectEleReceiptList.data[0].trades_remark,
                        drOrCr = selectEleReceiptList.data[0].debit_credit_mark,
                        transNo = selectEleReceiptList.data[0].trades_serial_no,
                        opAccountName = selectEleReceiptList.data[0].opp_bank_name,
                        opAccountNo = selectEleReceiptList.data[0].opp_bank_no,
                        opAccountBankName = selectEleReceiptList.data[0].opp_bank_deposit,
                        extraRemarks = selectEleReceiptList.data[0].trades_use,
                        transCcy = selectEleReceiptList.data[0].currency,
                        transAmtD = selectEleReceiptList.data[0].trades_amount.ToString(),
                        receipts = new List<Receipts>(),
                    };
                    addItem.receipts.Add(new Receipts()
                    {
                        base64 = GetBase(selectEleReceiptList.data[0].img_data)
                    });
                    pushData.Add(addItem);

                    saveEleReceiptDatas.Add(selectEleReceiptList.data[0]);

                    apiRequestData = JsonConvert.SerializeObject(pushData);
                    apiResData = this.httpClient.PostJson(thirdparty_api + $"{selectEleReceiptList.data[0].our_bank_no}", pushData, "Authorization", $"Bearer {eleLoginToken}");
                    if (!string.IsNullOrEmpty(apiResData))
                    {
                        var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                        apiResResult = eleArchivePushResult.code == 0 ? true : false;
                        result = apiResResult;
                        apiResMsg = eleArchivePushResult.errMsg;
                        if (eleArchivePushResult.code == 0)
                        {
                            var pushDetails = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(eleArchivePushResult.data.ToString());
                            if (pushDetails != null)
                            {
                                var errEleReceiptMstIds = new List<string>();
                                for (int i = 0; i < pushDetails.Count; i++)
                                {
                                    if (pushDetails[i].ContainsKey("success") && !Convert.ToBoolean(pushDetails[i]["success"]))
                                    {
                                        errEleReceiptMstIds.Add(pushDetails[i]["transNo"].ToString());
                                    }
                                }
                                var pushSuccessEleReceiptList = saveEleReceiptDatas.Where(w => !errEleReceiptMstIds.Contains(w.trades_serial_no.ToString())).ToList();
                                if (pushSuccessEleReceiptList != null && pushSuccessEleReceiptList.Count > 0)
                                {
                                    for (int i = 0; i < pushSuccessEleReceiptList.Count; i++)
                                    {
                                        pushSuccessEleReceiptList[i].ispost = 1; // 更改凭证是否发送的属性为: 已发送
                                    }
                                    await this.objectQuery.BulkCreateOrUpdate<MC_BILL_ELE_RECEIPT_DATA>(pushSuccessEleReceiptList);
                                }
                            }
                        }

                    }
                }
                //if (selectEleReceiptList.data != null && selectEleReceiptList.totalCount > 0)
                //{
                //    var saveEleReceiptDatas = new List<MC_BILL_ELE_RECEIPT_DATA>();
                //    var pushData = new List<Request_EleReceipt_Archive_Data>();
                //    foreach (var item in selectEleReceiptList.data)
                //    {
                //        #region 准备-电子回单归档数据推送的实体
                //        var addItem = new Request_EleReceipt_Archive_Data()
                //        {
                //            //accountNo = item.our_bank_no,//************
                //            accountNo = "************",//************
                //            transDate = Convert.ToDateTime(item.trades_time).ToString("yyyy-MM-hh"),
                //            remarks = item.trades_remark,
                //            drOrCr = item.debit_credit_mark,
                //            transNo = item.trades_serial_no,
                //            opAccountName = item.opp_bank_name,
                //            opAccountNo = item.opp_bank_no,
                //            opAccountBankName = item.opp_bank_deposit,
                //            extraRemarks = item.trades_use,
                //            transCcy = item.currency,
                //            transAmtD = item.trades_amount.ToString(),
                //            receipts = new List<Receipts>()
                //        };
                //        addItem.receipts.Add(new Receipts()
                //        {
                //            base64 = GetBase(item.img_data)
                //        });
                //        pushData.Add(addItem);
                //        #endregion

                //        saveEleReceiptDatas.Add(item);
                //    }


                //}

                //写入调用日志
                await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                {
                    ApiName = "ELE_RECEIPT_PUSH",
                    ApiDesc = "电子回单归档数据推送API",
                    CallTime = DateTime.Now,
                    CallResult = apiResResult,
                    CallResultData = apiResData,
                    CallResultMsg = apiResMsg,
                    RequestData = apiRequestData,
                    Remark = "调用API: " + thirdparty_api
                });
            }
            catch (Exception ex)
            {
                logger.LogError("推送电子回单归档数据的错误为Push_Ele_Receipt_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        private double ToDouble(string data)
        {
            double value = 0;
            if (!string.IsNullOrEmpty(data))
            {
                try
                {
                    value = Convert.ToDouble(data);
                }
                catch
                {

                }

            }
            return value;
        }

        /// <summary>
        /// 返回base64文件
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public string GetBase(string url)
        {
            var docId = url.Split('=');
            string base64 = "";
            if (docId != null && docId.Length > 0)
            {
                var base64_byte = this.httpClient.DowLoadFile(docId[1]).Item1;
                base64 = Convert.ToBase64String(base64_byte);
            }
            return base64;
        }

        /// <summary>
        /// 批量返回base64文件
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public List<string> GetListBase(List<string> url)
        {
            List<string> listbase64 = new List<string>();
            foreach (var item in url)
            {
                var docId = item.Split('=');
                var base64_byte = this.httpClient.DowLoadFile(docId[1]).Item1;
                var base64 = Convert.ToBase64String(base64_byte);
                listbase64.Add(base64);
            }
            return listbase64;
        }


        /// <summary>
        /// 推送 凭证和单据关系接口API
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_Voucher2Bill_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_MASTER_DATA> pushBillList, string eleLoginToken)
        {
            var result = false;
            try
            {
                if (pushVoucherList != null && pushVoucherList.Count > 0)
                {
                    //获取请求的api地址
                    string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_voucher2bill_archive_url"]; // 凭证和单据关系接口地址
                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";

                    var pushDatas = new List<Request_Voucher2Bill_Relation_Data>();
                    foreach (var item in pushVoucherList)
                    {
                        var selectBill = pushBillList.Where(w => w.bill_no == item.oa_flow_code).FirstOrDefault();
                        if (selectBill != null)
                        {
                            var addItem = new Request_Voucher2Bill_Relation_Data()
                            {
                                voucherId = string.Empty,
                                businessEntityName = item.company_name,
                                voucherPeriod = item.accounting_period,
                                voucherType = item.voucher_type,
                                voucherNo = item.voucher_no,
                                bills = new List<Voucher2Bill_Bill>()
                            };
                            addItem.bills.Add(new Voucher2Bill_Bill()
                            {
                                fieldCode = "fd_id",
                                value = selectBill.third_bill_guid
                            });
                            pushDatas.Add(addItem);
                        }
                    }

                    apiRequestData = JsonConvert.SerializeObject(pushDatas);
                    apiResData = this.httpClient.PostJson(thirdparty_api, pushDatas, "Authorization", $"Bearer {eleLoginToken}");
                    if (!string.IsNullOrEmpty(apiResData))
                    {
                        var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                        apiResResult = eleArchivePushResult.code == 0 ? true : false;
                        result = apiResResult;
                        apiResMsg = eleArchivePushResult.errMsg;
                        if (eleArchivePushResult.code != 0)
                        {
                            this.logger.LogError($"凭证和单据关系数据推送API, 执行结果返回失败. 返回结果: {apiResData}");
                        }
                        else
                        {
                            result = true;
                        }
                    }

                    //写入调用日志
                    await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                    {
                        ApiName = "Voucher2Bill_PUSH",
                        ApiDesc = "凭证和单据关系数据推送API",
                        CallTime = DateTime.Now,
                        CallResult = apiResResult,
                        CallResultData = apiResData,
                        CallResultMsg = apiResMsg,
                        RequestData = apiRequestData,
                        Remark = "调用API: " + thirdparty_api
                    });

                }
            }
            catch (Exception ex)
            {
                logger.LogError("推送 凭证和单据关系接口API的错误为Push_Voucher2Bill_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 推送 凭证和发票关系接口API
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_Voucher2Invoice_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_INVOICE_DATA> pushInvoiceList, string eleLoginToken)
        {
            var result = false;
            try
            {
                if (pushVoucherList != null && pushVoucherList.Count > 0)
                {
                    //获取请求的api地址
                    string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_voucher2invoice_archive_url"]; // 凭证和发票关系接口地址
                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";

                    var pushDatas = new List<Request_Voucher2Invoice_Relation_Data>();
                    foreach (var item in pushVoucherList)
                    {
                        var selectInvoiceList = pushInvoiceList.Where(w => w.bill_no == item.oa_flow_code).ToList();
                        if (selectInvoiceList != null && selectInvoiceList.Count > 0)
                        {
                            var addItem = new Request_Voucher2Invoice_Relation_Data()
                            {
                                voucherId = string.Empty,
                                businessEntityName = item.company_name,
                                voucherPeriod = item.accounting_period,
                                voucherType = item.voucher_type,
                                voucherNo = item.voucher_no,
                                invoiceList = new List<Voucher2Invoice_Invoice>()
                            };
                            foreach (var item_inv in selectInvoiceList)
                            {
                                addItem.invoiceList.Add(new Voucher2Invoice_Invoice()
                                {
                                    invoiceNumber = item_inv.number,
                                    invoiceCode = item_inv.code,
                                    type = this.GetEleArchiveInvoiceType(item_inv.type),
                                    invoiceDetail = new Voucher2Invoice_InvoiceDetail()
                                    {
                                        departureTime = item_inv.travel_details != null && item_inv.travel_details.Count > 0 ? item_inv.travel_details[0].departure_time : string.Empty,
                                        passengerName = item_inv.travel_details != null && item_inv.travel_details.Count > 0 ? item_inv.travel_details[0].passenger_name : string.Empty,
                                    }
                                });
                            }
                            pushDatas.Add(addItem);
                        }
                    }

                    apiRequestData = JsonConvert.SerializeObject(pushDatas);
                    apiResData = this.httpClient.PostJson(thirdparty_api, pushDatas, "Authorization", $"Bearer {eleLoginToken}");
                    if (!string.IsNullOrEmpty(apiResData))
                    {
                        var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                        apiResResult = eleArchivePushResult.code == 0 ? true : false;
                        apiResMsg = eleArchivePushResult.errMsg;
                        if (eleArchivePushResult.code != 0)
                        {
                            this.logger.LogError($"凭证和发票关系数据推送API, 执行结果返回失败. 返回结果: {apiResData}");
                        }
                        else
                        {
                            result = true;
                        }
                    }

                    //写入调用日志
                    await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                    {
                        ApiName = "Voucher2Invoice_PUSH",
                        ApiDesc = "凭证和发票关系数据推送API",
                        CallTime = DateTime.Now,
                        CallResult = apiResResult,
                        CallResultData = apiResData,
                        CallResultMsg = apiResMsg,
                        RequestData = apiRequestData,
                        Remark = "调用API: " + thirdparty_api
                    });

                }
            }
            catch (Exception ex)
            {
                logger.LogError("推送 凭证和发票关系接口API的错误为Push_Voucher2Invoice_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 推送 凭证和流水关系接口API
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Push_Voucher2Batch_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_ELE_RECEIPT_DATA> pushEleReceiptList, string eleLoginToken)
        {
            var result = false;
            try
            {
                if (pushVoucherList != null && pushVoucherList.Count > 0)
                {
                    //获取请求的api地址
                    string thirdparty_api = this.configuration["formal_ele_archives_settings:formal_voucher2batch_archive_url"]; // 凭证和流水关系接口
                    var apiResResult = false;
                    var apiResData = "";
                    var apiResMsg = "";
                    var apiRequestData = "";


                    var pushDatas = new List<Request_Voucher2Batch_Relation_Data>();
                    foreach (var item in pushVoucherList)
                    {
                        var selectEleReceiptList = pushEleReceiptList.Where(w => w.oa_flow_code == item.oa_flow_code).ToList();
                        if (selectEleReceiptList != null && selectEleReceiptList.Count > 0)
                        {
                            var addItem = new Request_Voucher2Batch_Relation_Data()
                            {
                                voucherId = string.Empty,
                                businessEntityName = item.company_name,
                                voucherPeriod = item.accounting_period,
                                voucherType = item.voucher_type,
                                voucherNo = item.voucher_no,
                                transNos = selectEleReceiptList.Select(s => s.trades_serial_no).ToArray(),
                                details = null
                            };
                            pushDatas.Add(addItem);
                        }
                    }

                    apiRequestData = JsonConvert.SerializeObject(pushDatas);
                    apiResData = this.httpClient.PostJson(thirdparty_api, pushDatas, "Authorization", $"Bearer {eleLoginToken}");
                    if (!string.IsNullOrEmpty(apiResData))
                    {
                        var eleArchivePushResult = JsonConvert.DeserializeObject<Response_EleArchivePushResult>(apiResData);
                        apiResResult = eleArchivePushResult.code == 0 ? true : false;
                        apiResMsg = eleArchivePushResult.errMsg;
                        if (eleArchivePushResult.code != 0)
                        {
                            this.logger.LogError($"凭证和流水关系数据推送API, 执行结果返回失败. 返回结果: {apiResData}");
                        }
                        else
                        {
                            result = true;
                        }
                    }

                    //写入调用日志
                    await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                    {
                        ApiName = "Voucher2Batch_PUSH",
                        ApiDesc = "凭证和流水关系数据推送API",
                        CallTime = DateTime.Now,
                        CallResult = apiResResult,
                        CallResultData = apiResData,
                        CallResultMsg = apiResMsg,
                        RequestData = apiRequestData,
                        Remark = "调用API: " + thirdparty_api
                    });

                }
            }
            catch (Exception ex)
            {
                logger.LogError("推送 凭证和流水关系接口API的错误为Push_Voucher2Batch_Archives_Data：" + ex);
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 获取第三方电子档案的发票类型
        /// </summary>
        /// <param name="invType"></param>
        /// <returns></returns>
        private string GetEleArchiveInvoiceType(string invType)
        {
            /*
                10100 增值税专用发票
                10101 增值税普通发票
                10102 增值税电子普通发票
                10103 增值税普通发票(卷票)
                10104 机动车销售统一发票
                10105 二手车销售统一发票
                10106 增值税电子专业发票
                10200 定额发票
                10400 机打发票
                10500 出租车发票
                10503 火车票
                10505 客运汽车
                10505a 船票
                10506 航空运输电子客票行程单
                10507 过路费发票
                10900 可报销其他发票
                20100 国际小票
                20105 滴滴出行行程单
                10902 完税证明
             */
            string result = "OTHER";
            try
            {
                switch (invType.Trim().ToLower())
                {
                    case "10100": // 增值税专用发票
                        result = "SPECIAL_VAT_PAPER";
                        break;
                    case "10101": // 增值税普通发票
                        result = "VAT_PAPER";
                        break;
                    case "10102": // 增值税电子普通发票
                        result = "VAT_ELECTRONIC";
                        break;
                    case "10103": // 增值税普通发票(卷票)
                        result = "VAT_PAPER_ROLL";
                        break;
                    case "10104": // 机动车销售统一发票
                        result = "SPECIAL_VAT_MOTOR_VEHICLE";
                        break;
                    case "10105": // 二手车销售统一发票
                        result = "SPECIAL_VAT_SECOND_HAND_CAR";
                        break;
                    case "10106": // 增值税电子专业发票
                        result = "SPECIAL_VAT_PAPER";
                        break;
                    case "10200": // 定额发票
                        result = "QUOTA_PAPER";
                        break;
                    case "10400": // 机打发票
                        result = "MACHINE_PAPER";
                        break;
                    case "10500": // 出租车发票
                        result = "VAT_TAXI";
                        break;
                    case "10503": // 火车票
                        result = "VAT_TRAIN";
                        break;
                    case "10505": // 客运汽车
                        result = "VAT_BUS";
                        break;
                    case "10505a": // 船票
                        result = "SHIP_PAPER";
                        break;
                    case "10506": // 航空运输电子客票行程单
                        result = "VAT_FLIGHT";
                        break;
                    case "10507": // 过路费发票
                        result = "VAT_PASSAGE";
                        break;
                    case "10900": // 可报销其他发票
                    case "20100": // 国际小票
                    case "20105": // 滴滴出行行程单
                    case "10902": // 完税证明
                    default:
                        result = "OTHER";
                        break;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 调用推送 凭证和单据关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        public async Task<bool> SendOaMaster(string eleLoginToken)
        {
            int ispost = 1;
            int oa_send = 0;
            bool result = false;
            List<MC_BILL_VOUCHER_DATA> voucherlist = new List<MC_BILL_VOUCHER_DATA>();
            try
            {
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { ispost.ToString() }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "oa_send",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { oa_send.ToString() }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                //获取凭证数据
                var selectVoucherList = await this.objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 50);

                //获取主表中bill_no的值
                var bill_no_array = selectVoucherList.data.Select(s => s.oa_flow_code).ToArray();

                var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = bill_no_array
                });
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "11" }
                });

                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filter, sorters, 0, 50);

                var responseResult = await Push_Voucher2Bill_Archives_Data(selectVoucherList.data, selectMasterList.data, eleLoginToken);
                //推送成功则修改OA单据发送状态
                if (responseResult)
                {
                    foreach (var item in selectVoucherList.data)
                    {
                        item.oa_send = 1;
                        voucherlist.Add(item);
                    }
                    result = await this.objectQuery.BulkCreateOrUpdate<MC_BILL_VOUCHER_DATA>(voucherlist);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 调用推送 凭证和单据发票关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        public async Task<bool> SendInvoice(string eleLoginToken)
        {
            int ispost = 1;
            int inv_send = 0;
            bool result = false;
            List<MC_BILL_VOUCHER_DATA> voucherlist = new List<MC_BILL_VOUCHER_DATA>();
            try
            {
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { ispost.ToString() }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "inv_send",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { inv_send.ToString() }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                //获取凭证数据
                var selectVoucherList = await this.objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 50);

                //获取主表中bill_no的值
                var bill_no_array = selectVoucherList.data.Select(s => s.oa_flow_code).ToArray();

                var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = bill_no_array
                });
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "12" }
                });
                //获取主表中的数据
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filter, sorters, 0, 50);

                //获取发票表中bill_no的值
                var invoince_bill_no_array = selectMasterList.data.Select(s => s.bill_no).ToArray();

                var filterinvoice = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filterinvoice.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "bill_no",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = invoince_bill_no_array
                });

                var selectInvoiceList = await this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filterinvoice, sorters, 0, 50);

                var responseResult = await Push_Voucher2Invoice_Archives_Data(selectVoucherList.data, selectInvoiceList.data, eleLoginToken);
                //推送成功则修改OA单据下发票发送状态
                if (responseResult)
                {
                    foreach (var item in selectVoucherList.data)
                    {
                        item.inv_send = 1;
                        voucherlist.Add(item);
                    }
                    result = await this.objectQuery.BulkCreateOrUpdate<MC_BILL_VOUCHER_DATA>(voucherlist);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 调用推送 凭证和单据电子回单关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        public async Task<bool> Send_Archives_Data(string eleLoginToken)
        {
            int ispost = 1;
            int nc_send = 0;
            bool result = false;
            List<MC_BILL_VOUCHER_DATA> voucherlist = new List<MC_BILL_VOUCHER_DATA>();
            try
            {
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { ispost.ToString() }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "nc_send",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { nc_send.ToString() }
                });

                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                //获取凭证数据
                var selectVoucherList = await this.objectQuery.GetList<MC_BILL_VOUCHER_DATA>(filters, sorters, 0, 50);

                //获取电子回单表中匹配的数据
                var archives_array = selectVoucherList.data.Select(s => s.oa_flow_code).ToArray();

                var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "oa_flow_code",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = archives_array
                });
                filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "数字",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "number",
                    Values = new string[] { ispost.ToString() }
                });

                var selectEleReceiptList = await this.objectQuery.GetList<MC_BILL_ELE_RECEIPT_DATA>(filter, sorters, 0, 50);

                var responseResult = await Push_Voucher2Batch_Archives_Data(selectVoucherList.data, selectEleReceiptList.data, eleLoginToken);
                //推送成功则修改OA单据下电子回单发送状态
                if (responseResult)
                {
                    foreach (var item in selectVoucherList.data)
                    {
                        item.nc_send = 1;
                        voucherlist.Add(item);
                    }
                    result = await this.objectQuery.BulkCreateOrUpdate<MC_BILL_VOUCHER_DATA>(voucherlist);
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
            return result;
        }
        #endregion
    }
}
