﻿using Docpark.MultipleComparisons.Interface.Models;
using DocPark.MongoDb;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class ThirdPartyMongoDbService : IThirdPartyMongoDbService
    {
        private readonly ILogger logger;
        private readonly IConfiguration configuration;
        private readonly IMongodbManager mongodbManager;

        public ThirdPartyMongoDbService(ILogger<ThirdPartyMongoDbService> _logger, IConfiguration _configuration, IMongodbManager _mongodbManager)
        {
            this.logger = _logger;
            this.configuration = _configuration;
            this.mongodbManager = _mongodbManager;
        }

        public async Task<List<MONGODB_THIRDPARTY_CALL_LOG>> GetCallLog(string apiName, string keywords, DateTime beginTime, DateTime endTime)
        {
            try
            {
                var coll = mongodbManager.GetCollection<MONGODB_THIRDPARTY_CALL_LOG>("MONGODB_THIRDPARTY_CALL_LOG");
                var selectList = await coll.Find(x => x.CallTime >= beginTime && x.CallTime <= endTime && x.ApiName.ToLower().Contains(apiName.ToLower()) && (x.RequestData.Contains(keywords) || x.CallResultData.Contains(keywords))).ToListAsync();

                return selectList;
            }
            catch (Exception e)
            {
                this.LogException("GetCallLog", e);
            }

            return null;
        }

        /// <summary>
        /// 新增第三方接口日志
        /// </summary>
        /// <param name="saveData"></param>
        /// <returns></returns>
        public async Task<bool> AddCallLog(MONGODB_THIRDPARTY_CALL_LOG saveData)
        {
            try
            {
                var coll = mongodbManager.GetCollection<MONGODB_THIRDPARTY_CALL_LOG>("MONGODB_THIRDPARTY_CALL_LOG");
                await coll.InsertOneAsync(saveData);

                return true;
            }
            catch (Exception e)
            {
                this.LogException("AddCallLog", e);
            }

            return false;
        }

        public async Task<bool> DelCallLog(string id)
        {
            try
            {
                var coll = mongodbManager.GetCollection<MONGODB_THIRDPARTY_CALL_LOG>("MONGODB_THIRDPARTY_CALL_LOG");
                var filter = Builders<MONGODB_THIRDPARTY_CALL_LOG>.Filter.Eq("Id", id);
                await coll.DeleteOneAsync(x=>x.Id == id);

                return true;
            }
            catch (Exception e)
            {
                this.LogException("DelCallLog", e);
            }

            return false;
        }

        /// <summary>
        /// 异常日志
        /// </summary>
        /// <param name="methodName">执行方法</param>
        /// <param name="exception">异常错误消息</param>
        /// <param name="requestParams">请求参数</param>
        private void LogException(string methodName, Exception exception, string requestParams = "")
        {
            StringBuilder errMsg = new StringBuilder();
            errMsg.AppendLine("[ThirdpartyApi]-第三方API->异常日志")
                .AppendLine($"执行日期: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}")
                .AppendLine($"执行方法: {methodName}")
                .AppendLine("执行异常, 异常消息如下:")
                .AppendLine(exception.Message)
                .AppendLine("堆栈消息如下:")
                .AppendLine(exception.StackTrace)
                .AppendLine("请求参数如下:")
                .AppendLine(string.IsNullOrEmpty(requestParams) ? "无" : requestParams);

            this.logger.LogError(exception, exception.Message, errMsg.ToString());
        }
    }
}
