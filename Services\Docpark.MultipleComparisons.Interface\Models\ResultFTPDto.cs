﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class ResultFTPDto
    {
        public bool State { get; set; }

        public string Message { get; set; }

        public int TotalCount { get; set; }

        public DataModel Data { get; set; }

        public class DataModel
        {
            public string DocumentId { get; set; }
        }
    }
}
