﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 第三方接口调用日志
    /// </summary>
    public class MONGODB_THIRDPARTY_CALL_LOG
    {
        /// <summary>
        /// MongoDB-ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        /// <summary>
        /// 调用的API名称
        /// </summary>
        public string ApiName { get; set; }
        /// <summary>
        /// 调用的API描述
        /// </summary>
        public string ApiDesc { get; set; }
        /// <summary>
        /// 调用时间
        /// </summary>
        [BsonDateTimeOptions(Kind = System.DateTimeKind.Local)]
        public DateTime CallTime { get; set; }
        /// <summary>
        /// 调用结果
        /// </summary>
        public bool CallResult { get; set; }
        /// <summary>
        /// 调用结果消息
        /// </summary>
        public string CallResultMsg { get; set; }
        /// <summary>
        /// 调用结果数据
        /// </summary>
        public string CallResultData { get; set; }
        /// <summary>
        /// 请求数据
        /// </summary>
        public string RequestData { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
