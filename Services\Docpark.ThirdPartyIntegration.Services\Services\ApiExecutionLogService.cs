using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// API执行日志服务实现
    /// </summary>
    public class ApiExecutionLogService : IApiExecutionLogService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly ILogger<ApiExecutionLogService> _logger;
        private const string CollectionName = "api_execution_logs";

        public ApiExecutionLogService(
            IMongoRepository mongoRepository,
            ILogger<ApiExecutionLogService> logger)
        {
            _mongoRepository = mongoRepository;
            _logger = logger;
        }

        public async Task<string> CreateAsync(ApiExecutionLog log)
        {
            try
            {
                log.Id = null; // Let MongoDB generate the ID
                await _mongoRepository.InsertOneAsync(CollectionName, log);
                _logger.LogDebug("Created API execution log for API: {ApiConfigId}", log.ApiConfigId);
                return log.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating API execution log for API: {ApiConfigId}", log.ApiConfigId);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(ApiExecutionLog log)
        {
            try
            {
                var filter = Builders<ApiExecutionLog>.Filter.Eq(x => x.Id, log.Id);
                var result = await _mongoRepository.ReplaceOneAsync(CollectionName, filter, log);
                
                _logger.LogDebug("Updated API execution log: {LogId}", log.Id);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating API execution log: {LogId}", log.Id);
                throw;
            }
        }

        public async Task<ApiExecutionLog> GetByIdAsync(string id)
        {
            try
            {
                var filter = Builders<ApiExecutionLog>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<ApiExecutionLog>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API execution log by id: {Id}", id);
                throw;
            }
        }

        public async Task<List<ApiExecutionLog>> GetByApiConfigIdAsync(string apiConfigId, int limit = 100)
        {
            try
            {
                var filter = Builders<ApiExecutionLog>.Filter.Eq(x => x.ApiConfigId, apiConfigId);
                var sort = Builders<ApiExecutionLog>.Sort.Descending(x => x.StartTime);
                
                return await _mongoRepository.FindListAsync<ApiExecutionLog>(
                    CollectionName,
                    filter,
                    sort,
                    limit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API execution logs for API: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        public async Task<List<ApiExecutionLog>> QueryAsync(
            string apiConfigId = null,
            ExecutionStatus? status = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            int limit = 100)
        {
            try
            {
                var filterBuilder = Builders<ApiExecutionLog>.Filter;
                var filters = new List<FilterDefinition<ApiExecutionLog>>();

                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    filters.Add(filterBuilder.Eq(x => x.ApiConfigId, apiConfigId));
                }

                if (status.HasValue)
                {
                    filters.Add(filterBuilder.Eq(x => x.Status, status.Value));
                }

                if (startTime.HasValue)
                {
                    filters.Add(filterBuilder.Gte(x => x.StartTime, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filters.Add(filterBuilder.Lte(x => x.StartTime, endTime.Value));
                }

                var filter = filters.Count > 0 
                    ? filterBuilder.And(filters) 
                    : filterBuilder.Empty;

                var sort = Builders<ApiExecutionLog>.Sort.Descending(x => x.StartTime);

                return await _mongoRepository.FindListAsync<ApiExecutionLog>(
                    CollectionName,
                    filter,
                    sort,
                    limit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying API execution logs");
                throw;
            }
        }

        public async Task<long> DeleteOldLogsAsync(DateTime olderThan)
        {
            try
            {
                var filter = Builders<ApiExecutionLog>.Filter.Lt(x => x.StartTime, olderThan);
                var result = await _mongoRepository.DeleteManyAsync<ApiExecutionLog>(CollectionName, filter);
                
                _logger.LogInformation("Deleted {Count} old API execution logs", result.DeletedCount);
                return result.DeletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting old API execution logs");
                throw;
            }
        }
    }
}
