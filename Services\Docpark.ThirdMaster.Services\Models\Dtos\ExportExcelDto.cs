﻿using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class ExportExcelDto
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 文档类型
        /// </summary>
        public string DocType { get; set; }
        /// <summary>
        /// 勾选的MstId
        /// </summary>
        public List<Guid> MstIds { get; set; } = new List<Guid>();

        public List<ShortFieldDto> ShortFields { get; set; } = new List<ShortFieldDto>();
    }

    public class ShortFieldDto
    {
        public string Field { get; set; }
        public string Identity { get; set; }
        public string FieldName { get; set; }
    }

    public class ExportExcelBySearchDto
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        [Required]
        public string BusinessType { get; set; }
        /// <summary>
        /// 文档类型
        /// </summary>
        [Required]
        public string DocType { get; set; }

        public List<Filter> Filters { get; set; } = new List<Filter>();

        public List<ShortFieldDto> ShortFields { get; set; } = new List<ShortFieldDto>();
        /// <summary>
        /// 分页数
        /// </summary>
        [Range(0, int.MaxValue)]
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 分页大小
        /// </summary>
        [Range(0, 1000)]
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 是否启用权限
        /// </summary>
        public bool EnablePremission { get; set; }
    }
}
