### 第三方接口集成系统功能测试

### 1. 测试系统健康状态
GET http://localhost:5000/api/health
Accept: application/json

### 2. 创建基础认证配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试基础认证",
  "description": "用于测试的基础认证配置",
  "type": 1,
  "parameters": {
    "username": "test_user",
    "password": "test_password"
  },
  "isEnabled": true
}

### 3. 获取所有授权配置
GET http://localhost:5000/api/AuthenticationConfig
Accept: application/json

### 4. 创建简单的API配置（使用httpbin进行测试）
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "测试API - httpbin",
  "description": "使用httpbin.org进行测试的API配置",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/json",
  "method": "GET",
  "authenticationConfigId": null,
  "parameters": [],
  "timeoutSeconds": 30,
  "retryCount": 1,
  "isEnabled": true
}

### 5. 获取所有API配置
GET http://localhost:5000/api/ApiConfiguration
Accept: application/json

### 6. 手动执行API（需要替换为实际的API配置ID）
# 注意：需要先执行步骤4创建API配置，然后从响应中获取ID替换下面的{api-config-id}
POST http://localhost:5000/api/ApiExecution/{api-config-id}/execute
Accept: application/json

### 7. 查看API响应数据
GET http://localhost:5000/api/ApiResponseData/query?limit=5
Accept: application/json

### 8. 获取执行日志
GET http://localhost:5000/api/ApiExecutionLog/query?limit=5
Accept: application/json

### 9. 获取数据统计
GET http://localhost:5000/api/ApiResponseData/statistics
Accept: application/json

### 10. 测试OAuth2配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试OAuth2",
  "description": "OAuth2测试配置",
  "type": 2,
  "parameters": {
    "clientId": "test_client",
    "clientSecret": "test_secret",
    "tokenUrl": "https://httpbin.org/post",
    "scope": "read"
  },
  "isEnabled": true
}

### 11. 创建带参数的API配置
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "带参数的测试API",
  "description": "测试动态参数功能",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/get",
  "method": "GET",
  "authenticationConfigId": null,
  "parameters": [
    {
      "name": "test_param",
      "location": "query",
      "type": 1,
      "value": "static_value",
      "isRequired": false,
      "description": "静态参数测试"
    },
    {
      "name": "current_time",
      "location": "query",
      "type": 2,
      "format": "yyyy-MM-dd HH:mm:ss",
      "isRequired": false,
      "description": "当前时间参数"
    },
    {
      "name": "timestamp",
      "location": "query",
      "type": 3,
      "format": "seconds",
      "isRequired": false,
      "description": "时间戳参数"
    }
  ],
  "timeoutSeconds": 30,
  "retryCount": 1,
  "isEnabled": true
}

### 12. 测试API Key认证
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "API Key测试",
  "description": "API Key认证测试",
  "type": 3,
  "parameters": {
    "keyName": "X-API-Key",
    "keyValue": "test-api-key-12345",
    "location": "header"
  },
  "isEnabled": true
}

### 13. 测试Bearer Token认证
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "Bearer Token测试",
  "description": "Bearer Token认证测试",
  "type": 4,
  "parameters": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token"
  },
  "isEnabled": true
}

### 14. 批量执行API测试（需要替换为实际的API配置ID数组）
POST http://localhost:5000/api/ApiExecution/batch-execute
Content-Type: application/json

[
  "{api-config-id-1}",
  "{api-config-id-2}"
]

### 15. 清理测试数据（删除30天前的数据）
DELETE http://localhost:5000/api/ApiResponseData/cleanup?olderThanDays=30
Accept: application/json
