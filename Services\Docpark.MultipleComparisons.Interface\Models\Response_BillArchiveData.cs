﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 单据归档数据
    /// </summary>
    public class Response_BillArchiveData
    {
        /// <summary>
        /// 单据来源
        /// </summary>
        public string third_bill_source { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string business_type { get; set; }
        /// <summary>
        /// 单据id
        /// </summary>
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string bill_no { get; set; }
        /// <summary>
        /// 单据标题
        /// </summary>
        public string third_bill_title { get; set; }
        /// <summary>
        /// 提单人代码
        /// </summary>
        public string proposer_code { get; set; }
        /// <summary>
        /// 提单人名称
        /// </summary>
        public string proposer_name { get; set; }
        /// <summary>
        /// 提单时间
        /// </summary>
        public string bill_submit_time { get; set; }
        /// <summary>
        /// 提单人部门编码
        /// </summary>
        public string proposer_dept_code { get; set; }
        /// <summary>
        /// 提单人部门名称
        /// </summary>
        public string proposer_dept_name { get; set; }
        /// <summary>
        /// 成本中心编码
        /// </summary>
        public string cost_center_code { get; set; }
        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string cost_center_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string supplier_code { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string supplier_name { get; set; }
        /// <summary>
        /// 费用明细
        /// </summary>
        public List<MC_Expense_details> expense_details { get; set; }
        /// <summary>
        /// 报销总金额
        /// </summary>
        public Double total_amount { get; set; }
        /// <summary>
        /// 财务核定总金额
        /// </summary>
        public Double total_approved_amoun { get; set; }
        /// <summary>
        /// 费用凭证号
        /// </summary>
        public string expense_voucher_no { get; set; }
        /// <summary>
        /// 支付凭证号
        /// </summary>
        public string payment_voucher_no { get; set; }
        /// <summary>
        /// 业务状态
        /// </summary>
        public string business_status { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 附件数据集合
        /// </summary>
        public List<Annex_items> annex_items { get; set; }
        /// <summary>
        /// 发票数据集合
        /// </summary>
        public List<Invoice_Items> invoice_items { get; set; }
        /// <summary>
        /// 凭证数据集合
        /// </summary>
        public List<Voucher_Items> voucher_items { get; set; }
        /// <summary>
        /// 电子回单集合
        /// </summary>
        public List<Ele_Receipt_Items> ele_receipt_items { get; set; }
    }

    /// <summary>
    /// 发票数据
    /// </summary>
    public class Invoice_Items
    {
        /// <summary>
        /// 单据id
        /// </summary>
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string bill_no { get; set; }
        /// <summary>
        /// 单据mstid
        /// </summary>
        public Guid bill_mstid { get; set; }
        /// <summary>
        /// 发票明细id
        /// </summary>
        public string third_invoice_guid { get; set; }
        /// <summary>
        /// 第三方发票类型
        /// </summary>
        public string third_type { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string number { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string date { get; set; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string check_code { get; set; }
        /// <summary>
        /// 乘车人姓名
        /// </summary>
        public string passenger_name { get; set; }
        /// <summary>
        /// 出发时间
        /// </summary>
        public DateTime departure_time { get; set; }
        /// <summary>
        /// 税前金额
        /// </summary>
        public Double pretax_amount { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public Double tax { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string tax_rate { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        public Double total { get; set; }
        /// <summary>
        /// 购买方名称
        /// </summary>
        public string buyer { get; set; }
        /// <summary>
        /// 购买方纳税人识别号
        /// </summary>
        public string buyer_tax_id { get; set; }
        /// <summary>
        /// 销售方名称
        /// </summary>
        public string seller { get; set; }
        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        public string seller_tax_id { get; set; }
        /// <summary>
        /// 是否必须匹配, 00 无需匹配, 01 需要匹配(默认)
        /// </summary>
        public string is_required_match { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 附件数据集合
        /// </summary>
        public List<Annex_items> annex_items { get; set; }
    }

    /// <summary>
    /// 凭证数据
    /// </summary>
    public class Voucher_Items {
        /// <summary>
        /// 公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 日记账名
        /// </summary>
        public string accounting_name { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public string accounting_date { get; set; }
        /// <summary>
        /// 凭证编号
        /// </summary>
        public string voucher_no { get; set; }
        /// <summary>
        /// 批名
        /// </summary>
        public string batch_name { get; set; }
        /// <summary>
        /// 合计金额贷方
        /// </summary>
        public string credit_total_amount { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public string write_off_status { get; set; }
        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucher_type { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 凭证合计金额
        /// </summary>
        public string voucher_total_amount { get; set; }
        /// <summary>
        /// 凭证审核人工号
        /// </summary>
        public string voucher_approver_job_num { get; set; }
        /// <summary>
        /// 凭证审核人名称
        /// </summary>
        public string voucher_approver_name { get; set; }
        /// <summary>
        /// 会计期间
        /// </summary>
        public string accounting_period { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string voucher_source { get; set; }
        /// <summary>
        /// 合计金额借方
        /// </summary>
        public string debit_total_amount { get; set; }
        /// <summary>
        /// 制单人名称
        /// </summary>
        public string voucher_creator_name { get; set; }
        /// <summary>
        /// 制单人工号
        /// </summary>
        public string voucher_creator_job_num { get; set; }
        /// <summary>
        /// OA流程编码
        /// </summary>
        public string oa_flow_code { get; set; }
        /// <summary>
        /// 摘要详情
        /// </summary>
        public List<summary_details> summary_details { get; set; }
    }

    /// <summary>
    /// 电子回单数据
    /// </summary>
    public class Ele_Receipt_Items
    {
        /// <summary>
        /// 打印日期
        /// </summary>
        public string print_date { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        public string print_times { get; set; }
        /// <summary>
        /// 打印渠道
        /// </summary>
        public string print_channel { get; set; }
        /// <summary>
        /// 电子回单编号
        /// </summary>
        public string ele_receipt_no { get; set; }
        /// <summary>
        /// 验证码
        /// </summary>
        public string verify_ode { get; set; }
        /// <summary>
        /// 付款人账户名称
        /// </summary>
        public string payer_account_title { get; set; }
        /// <summary>
        /// 付款人账号
        /// </summary>
        public string payer_account { get; set; }
        /// <summary>
        /// 付款人开户银行
        /// </summary>
        public string payer_bank { get; set; }
        /// <summary>
        /// 收款人账户名称
        /// </summary>
        public string payee_account_title { get; set; }
        /// <summary>
        /// 收款人账号
        /// </summary>
        public string payee_account { get; set; }
        /// <summary>
        /// 收款人开户银行
        /// </summary>
        public string payee_bank { get; set; }
        /// <summary>
        /// 交易名称
        /// </summary>
        public string trades_name { get; set; }
        /// <summary>
        /// 支付凭证号
        /// </summary>
        public string payment_voucher_no { get; set; }
        /// <summary>
        /// oa流程编码
        /// </summary>
        public string oa_flow_code { get; set; }
        /// <summary>
        /// 交易时间
        /// </summary>
        public string trades_time { get; set; }
        /// <summary>
        /// 交易网点
        /// </summary>
        public string trades_outlets { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        public string apply_date { get; set; }
        /// <summary>
        /// 交易流水号
        /// </summary>
        public string trades_serial_no { get; set; }
        /// <summary>
        /// 交易金额（大写）
        /// </summary>
        public string trades_amount_cn { get; set; }
        /// <summary>
        /// 交易金额（小写）
        /// </summary>
        public string trades_amount { get; set; }
        /// <summary>
        /// 回单摘要
        /// </summary>
        public string trades_summary { get; set; }
        /// <summary>
        /// 电子回单时间戳
        /// </summary>
        public string trades_timestamp { get; set; }
        /// <summary>
        /// 交易附言
        /// </summary>
        public string trades_remark { get; set; }
    }
}
