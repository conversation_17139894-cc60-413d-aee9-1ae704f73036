using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.ThirdMaster.Services.Models;
using DocPark.MongoDb;
using DocPark.Workflow.Share;
using Microsoft.Azure.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace Docpark.ThirdMaster.Services.Backgroundjob.YaoPi
{
    [MasterServiceType("yaopi")]
    public class IAMSyncBackgroundJob : BackgroundService, IThirdMasterBackgroundJob
    {
        private const string OrgJobIdentifier = "IAM_ORG_SYNC";
        private const string AccountJobIdentifier = "IAM_ACCOUNT_SYNC";

        private readonly ILogger<IAMSyncBackgroundJob> _logger;
        private readonly IConfiguration _configuration;
        private readonly IMongodbManager _mongodbManager;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IComHttpClient _comHttpClient; // <-- 注入 IComHttpClient
        private Timer _timer;

        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        private bool _jobEnabled;
        private string _schedule;
        private TimeSpan _dailyRunTime;

        public IAMSyncBackgroundJob(
            ILogger<IAMSyncBackgroundJob> logger,
            IConfiguration configuration,
            IMongodbManager mongodbManager,
            IHttpClientFactory httpClientFactory,
            IComHttpClient comHttpClient) // <-- 修改构造函数
        {
            _logger = logger;
            _configuration = configuration;
            _mongodbManager = mongodbManager;
            _httpClientFactory = httpClientFactory;
            _comHttpClient = comHttpClient; // <-- 存储注入的服务
        }


        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await StartAsync(stoppingToken);
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("IAM Sync Background Job is starting.");
            _jobEnabled = _configuration.GetValue<bool>("IAMSyncJobSettings:Enabled");
            _schedule = _configuration.GetValue<string>("IAMSyncJobSettings:Schedule");
            TimeSpan.TryParse(_configuration.GetValue<string>("IAMSyncJobSettings:DailyRunTime"), out _dailyRunTime);

            if (!_jobEnabled)
            {
                _logger.LogWarning("IAM Sync Background Job is disabled in configuration.");
                return Task.CompletedTask;
            }
            Console.WriteLine($"IAM Sync Job is enabled. Schedule: {_schedule}, Daily Run Time: {_dailyRunTime}");
            _timer = new Timer(ExecuteTask, null, TimeSpan.FromSeconds(10), TimeSpan.FromMinutes(1));
            return Task.CompletedTask;
        }

        private void ExecuteTask(object state)
        {
            if (!ShouldRunNow()) return;
            if (!_semaphore.Wait(0))
            {
                _logger.LogInformation("IAM Sync Job is already running. Skipping this execution.");
                return;
            }
            try
            {
                RunJobAsync().Wait();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while running IAM Sync Job.");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private bool ShouldRunNow()
        {
            if (string.IsNullOrEmpty(_schedule)) return false;
            var now = DateTime.Now;
            return _schedule.ToLower() switch
            {
                "everyminute" => true,
                "hourly" => now.Minute == 0,
                "daily" => now.Hour == _dailyRunTime.Hours && now.Minute == _dailyRunTime.Minutes,
                _ => false
            };
        }

        private async Task RunJobAsync()
        {
            _logger.LogInformation("IAM Sync Job started at: {time}", DateTimeOffset.Now);

            await SyncOrgsAsync();
            await SyncAccountsAsync();

            //同步到核心系统
            await SyncToInternalSystemAsync();

            _logger.LogInformation("IAM Sync Job finished at: {time}", DateTimeOffset.Now);
        }

        private async Task SyncOrgsAsync()
        {
            _logger.LogInformation("Starting organization sync...");
            var orgCollectionName = _configuration["IAMSyncJobSettings:OrgMongoDbCollectionName"];
            if (string.IsNullOrEmpty(orgCollectionName))
            {
                _logger.LogError("Organization MongoDB collection name is not configured.");
                return;
            }
            try
            {
                var client = CreateIamHttpClient();
                var apiUrl = $"{_configuration["IAMSyncJobSettings:ApiBaseUrl"].TrimEnd('/')}/esc-idm/api/v1/org/list";
                var lastSyncTime = await GetLastSyncTimeAsync(OrgJobIdentifier);
                var nextSyncTime = DateTime.Now;
                long processedCount = 0;
                await PaginateAndUpsert<OrgItem, OrgApiResponse>(client, apiUrl, ConvertToUnixTimestamp(lastSyncTime).ToString(), orgCollectionName, (count) => processedCount += count);
                await SaveLastSyncTimeAsync(OrgJobIdentifier, nextSyncTime);
                _logger.LogInformation("Organization sync finished successfully. Total records processed: {processedCount}", processedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An exception occurred during organization sync process.");
            }
        }

        private async Task SyncAccountsAsync()
        {
            _logger.LogInformation("Starting account sync...");
            var accountCollectionName = _configuration["IAMSyncJobSettings:AccountMongoDbCollectionName"];
            if (string.IsNullOrEmpty(accountCollectionName))
            {
                _logger.LogError("Account MongoDB collection name is not configured.");
                return;
            }
            try
            {
                var client = CreateIamHttpClient();
                var apiUrl = $"{_configuration["IAMSyncJobSettings:ApiBaseUrl"].TrimEnd('/')}/esc-idm/api/v1/account/list";
                var lastSyncTime = await GetLastSyncTimeAsync(AccountJobIdentifier);
                var nextSyncTime = DateTime.Now;
                long processedCount = 0;
                await PaginateAndUpsert<AccountItem, AccountApiResponse>(client, apiUrl,
                    ConvertToUnixTimestamp(lastSyncTime).ToString(), accountCollectionName, (count) => processedCount += count);
                await SaveLastSyncTimeAsync(AccountJobIdentifier, nextSyncTime);
                _logger.LogInformation("Account sync finished successfully. Total records processed: {processedCount}", processedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An exception occurred during account sync process.");
            }
        }

        /// <summary>
        /// 将从MongoDB同步的数据，通过内部服务写入到核心系统。
        /// </summary>
        private async Task SyncToInternalSystemAsync()
        {
            _logger.LogInformation("Starting sync to internal system using IComHttpClient...");

            try
            {
                var accountCollectionName = _configuration["IAMSyncJobSettings:AccountMongoDbCollectionName"];

                int total = 0;
                do
                {
                    var accountsFromMongo = await _mongodbManager.GetCollection<AccountItem>(accountCollectionName)
                        .Find(w => w.SyncStatus == 0).Limit(50).ToListAsync();
                    total = accountsFromMongo.Count;
                    if (!accountsFromMongo.Any())
                    {
                        _logger.LogInformation("No accounts found in MongoDB to sync to internal system.");
                        break;
                    }
                    var accountsToCreate = accountsFromMongo;

                    _logger.LogInformation("{count} new accounts to be created in internal system.", accountsToCreate.Count);



                    var list_SaveUser = new List<Model_ThirdpartyCreateUser>();
                    foreach (var account in accountsToCreate)
                    {

                        var userLoginProviders = new List<UserLoginProvider>();
                        if (!string.IsNullOrEmpty(account.AccountNo))
                        {
                            userLoginProviders.Add(new UserLoginProvider()
                            {
                                ProviderKey = account.AccountNo,
                                LoginProvider = "OAuth2.0"
                            });
                        }
                        if (!string.IsNullOrEmpty(account.idt_user__pk_psndoc))
                        {
                            userLoginProviders.Add(new UserLoginProvider()
                            {
                                LoginProvider = "SignatureLogin",
                                ProviderKey = account.idt_user__pk_psndoc
                            });
                        }
                        list_SaveUser.Add(new Model_ThirdpartyCreateUser
                        {
                            AssignedRoleNames = new string[] { "User" }, // 默认分配 "User" 角色
                            OrganizationUnits = new List<long>() { 2 },
                            SendActivationEmail = false,
                            SetRandomPassword = true,
                            UserLoginProviders = userLoginProviders,
                            User = new Model_CreateUserDto
                            {
                                UserName = account.AccountNo,
                                Password = "", //默认密码为空，内部系统会使用默认密码策略
                                Name = account.AccountName,
                                EmailAddress = string.IsNullOrEmpty(account.Email) ? $"{account.AccountNo}@docpark.com" : account.Email,
                                PhoneNumber = account.Mobile,
                                IsActive = account.ActionFlag == 2 || account.ActionFlag==4 ? false:true,
                                ShouldChangePasswordOnNextLogin = true,
                                IsLockoutEnabled = true,
                                IsTwoFactorEnabled = true,
                                Surname = account.AccountName
                            }
                        });
                    }

                    // 5. 调用 IComHttpClient 的方法进行同步
                    var strResult = _comHttpClient.SyncThirdpartyUser(list_SaveUser);
                    if (!string.IsNullOrEmpty(strResult))
                    {
                        var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                        if (payload != null && Convert.ToBoolean(payload["success"]))
                        {
                            var idsToCreate = accountsToCreate.Select(a => a.Id).ToList();
                            var filter = Builders<AccountItem>.Filter.In(x => x.Id, idsToCreate);
                            var update = Builders<AccountItem>.Update.Set(u => u.SyncStatus, 1);

                            await _mongodbManager.GetCollection<AccountItem>(accountCollectionName).UpdateManyAsync(filter, update);
                            _logger.LogInformation("Successfully synced users to internal system. Result: {result}", payload["result"]?.ToString());
                        }
                        else
                        {
                            var errorMessage = payload?["error"]?["message"]?.ToString() ?? "Unknown error";
                            _logger.LogError("Failed to sync users to internal system. Response: {error}", errorMessage);
                        }
                    }
                    else
                    {
                        _logger.LogError("Failed to sync users to internal system. The response was null or empty.");
                    }
                } while (total > 0);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An exception occurred during internal system sync process.");
            }
        }

        private async Task PaginateAndUpsert<TItem, TResponse>(HttpClient client, string apiUrl, string lastSyncTime, string collectionName, Action<long> onUpserted) where TItem : IApiItem where TResponse : IApiResponse<TItem>
        {
            var pageSize = _configuration.GetValue<int>("IAMSyncJobSettings:PageSize", 100);
            long totalRecords = 0;
            int currentPage = 1;
            var collection = _mongodbManager.GetCollection<TItem>(collectionName);
            do
            {
                var requestPayload = new ApiRequest { Page = currentPage, Size = pageSize, StartTime = lastSyncTime };
                var jsonPayload = JsonConvert.SerializeObject(requestPayload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(apiUrl, content);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("API request to {apiUrl} failed with status code: {statusCode}", apiUrl, response.StatusCode);
                    break;
                }
                var jsonResponse = await response.Content.ReadAsStringAsync();
                //var jsonResponse = File.ReadAllText(@"E:\temp\users.json");
                Console.WriteLine($"URL:{apiUrl},lastSyncTime:{lastSyncTime},Response:{jsonResponse}");
                var apiResponse = JsonConvert.DeserializeObject<TResponse>(jsonResponse);
                if (apiResponse.Code != "0")
                {
                    _logger.LogError("API at {apiUrl} returned an error. Code: {code}, Message: {message}", apiUrl, apiResponse.Code, apiResponse.Msg);
                    break;
                }
                if (apiResponse.Data?.List == null || !apiResponse.Data.List.Any())
                {
                    _logger.LogInformation("No new data found from {apiUrl}.", apiUrl);
                    break;
                }
                totalRecords = apiResponse.Data.Total;
                var itemsToUpsert = apiResponse.Data.List;
                var bulkOps = new List<WriteModel<TItem>>();
                foreach (var item in itemsToUpsert)
                {
                    if (item.Id == null)
                    {
                        // 强制分配一个新的 ObjectId 或 GUID
                        item.Id = ObjectId.GenerateNewId().ToString(); // 或 Guid.NewGuid().ToString()
                        var insertOne= new InsertOneModel<TItem>(item);
                        bulkOps.Add(insertOne);
                    }
                    else
                    {
                        var filter = Builders<TItem>.Filter.Eq(i => i.Id, item.Id);
                        var replacement = item;
                        var upsertOne = new ReplaceOneModel<TItem>(filter, replacement) { IsUpsert = true };
                        bulkOps.Add(upsertOne);
                    }
                }
                if (bulkOps.Any())
                {
                    var result = await collection.BulkWriteAsync(bulkOps);
                    var upsertedCount = result.MatchedCount + result.Upserts.Count;
                    onUpserted(upsertedCount);
                    _logger.LogInformation("Page {currentPage} for {apiUrl}: Upserted {count} records.", currentPage, apiUrl, upsertedCount);
                }
                currentPage++;
            } while ((currentPage - 1) * pageSize < totalRecords);
        }

        private HttpClient CreateIamHttpClient()
        {
            var appKey = _configuration["IAMSyncJobSettings:AppKey"];
            var appSecret = _configuration["IAMSyncJobSettings:AppSecret"];
            var client = _httpClientFactory.CreateClient();
            var secret = $"{appKey}:{appSecret}";
            var token = Convert.ToBase64String(Encoding.UTF8.GetBytes(secret));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", token);
            return client;
        }

        private async Task<DateTime> GetLastSyncTimeAsync(string jobIdentifier)
        {
            var stateCollectionName = _configuration["IAMSyncJobSettings:SyncStateCollectionName"];
            if (string.IsNullOrEmpty(stateCollectionName)) return DateTime.Now.AddYears(-5);
            var collection = _mongodbManager.GetCollection<SyncState>(stateCollectionName);
            var filter = Builders<SyncState>.Filter.Eq(s => s.JobName, jobIdentifier);
            var syncState = await collection.Find(filter).FirstOrDefaultAsync();
            if (syncState != null)
                return syncState.LastUpdated;
            else
                return DateTime.Now.AddYears(-5);
        }

        private async Task SaveLastSyncTimeAsync(string jobIdentifier, DateTime syncTime)
        {
            var stateCollectionName = _configuration["IAMSyncJobSettings:SyncStateCollectionName"];
            if (string.IsNullOrEmpty(stateCollectionName)) return;
            var collection = _mongodbManager.GetCollection<SyncState>(stateCollectionName);
            var filter = Builders<SyncState>.Filter.Eq(s => s.JobName, jobIdentifier);
            var update = Builders<SyncState>.Update
                .Set(s => s.LastUpdated, syncTime)
                .SetOnInsert(s => s.JobName, jobIdentifier);
            await collection.UpdateOneAsync(filter, update, new UpdateOptions { IsUpsert = true });
        }

        private double ConvertToUnixTimestamp(DateTime date)
        {
            DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan diff = date.ToUniversalTime() - origin;
            return Math.Floor(diff.TotalSeconds);
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("IAM Sync Background Job is stopping.");
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
            _semaphore?.Dispose();
        }
    }

    #region Shared Models
    public interface IApiItem { string Id { get; set; } }
    public interface IApiResponse<TItem> where TItem : IApiItem
    {
        string Code { get; set; }
        string Msg { get; set; }
        IApiData<TItem> Data { get; set; }
    }
    public interface IApiData<TItem> where TItem : IApiItem
    {
        long Total { get; set; }
        List<TItem> List { get; set; }
    }
    public class SyncState
    {
        public ObjectId Id { get; set; }
        public string JobName { get; set; }
        public DateTime LastUpdated { get; set; }
    }
    public class ApiRequest
    {
        [JsonProperty("size")] public int Size { get; set; }
        [JsonProperty("page")] public int Page { get; set; }
        [JsonProperty("startTime")] public string StartTime { get; set; }
    }
    #endregion

    #region Organization Models
    public class OrgApiResponse : IApiResponse<OrgItem>
    {
        [JsonProperty("code")] public string Code { get; set; }
        [JsonProperty("timestamp")] public string Timestamp { get; set; }
        [JsonProperty("data")] public OrgApiData Data { get; set; }
        [JsonProperty("msg")] public string Msg { get; set; }
        IApiData<OrgItem> IApiResponse<OrgItem>.Data { get => Data; set => Data = (OrgApiData)value; }
    }
    public class OrgApiData : IApiData<OrgItem>
    {
        [JsonProperty("total")] public long Total { get; set; }
        [JsonProperty("size")] public int Size { get; set; }
        [JsonProperty("page")] public int Page { get; set; }
        [JsonProperty("list")] public List<OrgItem> List { get; set; }
    }
    public class OrgItem : IApiItem
    {
        [JsonProperty("idt_org__id")] public string Id { get; set; }
        [JsonProperty("idt_org__parent_id")] public string ParentId { get; set; }
        [JsonProperty("idt_org__name")] public string Name { get; set; }
        [JsonProperty("idt_org__org_code")] public string OrgCode { get; set; }
        [JsonProperty("idt_org__status")] public int Status { get; set; }
        [JsonProperty("request_log__action_flag")] public int ActionFlag { get; set; }
    }
    #endregion

    #region Account Models
    public class AccountApiResponse : IApiResponse<AccountItem>
    {
        [JsonProperty("code")] public string Code { get; set; }
        [JsonProperty("timestamp")] public string Timestamp { get; set; }
        [JsonProperty("data")] public AccountApiData Data { get; set; }
        [JsonProperty("msg")] public string Msg { get; set; }
        IApiData<AccountItem> IApiResponse<AccountItem>.Data { get => Data; set => Data = (AccountApiData)value; }
    }
    public class AccountApiData : IApiData<AccountItem>
    {
        [JsonProperty("total")] public long Total { get; set; }
        [JsonProperty("size")] public int Size { get; set; }
        [JsonProperty("page")] public int Page { get; set; }
        [JsonProperty("list")] public List<AccountItem> List { get; set; }
    }
    public class AccountItem : IApiItem
    {
        [JsonProperty("app_account__id")] public string Id { get; set; }
        [JsonProperty("app_account__status")] public int Status { get; set; }
        [JsonProperty("useTypes")] public List<IAMUserType> UseTypes { get; set; }

        [JsonProperty("idt_user__email")] public string Email { get; set; }
        [JsonProperty("idt_user__mobile")] public string Mobile { get; set; }
        [JsonProperty("jobs")] public List<JobItem> Jobs { get; set; }
        /// <summary>
        /// 唯一标识符，通常是用户的UUID或ID。
        /// </summary>
        [JsonProperty("app_account__account_uuid")] public string AccountUuid { get; set; }

        [JsonProperty("idt_user__pk_psndoc")]
        public string idt_user__pk_psndoc { get; set; }
        [JsonProperty("orgs")] public List<OrgInfo> Orgs { get; set; }
        [JsonProperty("app_account__account_no")] public string AccountNo { get; set; }
        [JsonProperty("app_account__account_name")] public string AccountName { get; set; }
        [JsonProperty("request_log__action_flag")] public int ActionFlag { get; set; }

        /// <summary>
        /// 同步状态
        /// </summary>
        public int SyncStatus { get; set; } = 0;

        ///<summary>
        ///批次号
        ///</summary>
        public string BatchSize { get; set; }

        public AccountItem()
        {
            this.SyncStatus = 0;
            this.BatchSize =DateTime.Now.ToShortDateString();
        }
    }
    public class JobItem
    {
        [JsonProperty("idt_job__name")] public string Name { get; set; }
        [JsonProperty("idt_job__id")] public string Id { get; set; }
    }
    public class OrgInfo
    {
        [JsonProperty("idt_org__id")] public string Id { get; set; }
        [JsonProperty("idt_org__name")] public string Name { get; set; }

        [JsonProperty("idt_org__org_code")]
        public string code { get; set; }
    }

    public class IAMUserType
    {
        [JsonProperty("idt_user_type__name")]
        public string name { get; set; }

        [JsonProperty("idt_user_type__code")]
        public string code { get; set; }
    }
    #endregion
}
