### 第三方接口集成系统 API 测试

### 1. 创建BasicAuth授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试BasicAuth",
  "description": "用于测试的基础认证",
  "type": 1,
  "parameters": {
    "username": "test_user",
    "password": "test_password"
  },
  "isEnabled": true
}

### 2. 创建OAuth2授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试OAuth2",
  "description": "用于测试的OAuth2认证",
  "type": 2,
  "parameters": {
    "clientId": "test_client_id",
    "clientSecret": "test_client_secret",
    "tokenUrl": "https://httpbin.org/post",
    "scope": "read"
  },
  "isEnabled": true
}

### 3. 获取所有授权配置
GET http://localhost:5000/api/AuthenticationConfig

### 4. 创建API配置
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "测试API",
  "description": "用于测试的API配置",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/get",
  "method": "GET",
  "authenticationConfigId": "替换为实际的授权配置ID",
  "parameters": [
    {
      "name": "test_param",
      "location": "query",
      "type": 1,
      "value": "test_value",
      "isRequired": false,
      "description": "测试参数"
    },
    {
      "name": "current_time",
      "location": "query",
      "type": 2,
      "format": "yyyy-MM-dd HH:mm:ss",
      "isRequired": false,
      "description": "当前时间参数"
    }
  ],
  "schedule": {
    "type": 1,
    "intervalMinutes": 60,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 3
}

### 5. 获取所有API配置
GET http://localhost:5000/api/ApiConfiguration

### 6. 手动执行API（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/execute

### 7. 测试API连接（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/test

### 8. 查看API响应数据（需要替换为实际的API配置ID）
GET http://localhost:5000/api/ApiResponseData/by-api/替换为实际的API配置ID?limit=10

### 9. 获取数据统计
GET http://localhost:5000/api/ApiResponseData/statistics

### 10. 查询执行日志
GET http://localhost:5000/api/ApiExecutionLog/query?limit=10

### 11. 创建一个简单的测试API配置（不需要授权）
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "简单测试API",
  "description": "不需要授权的测试API",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/json",
  "method": "GET",
  "parameters": [],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 1
}

### 12. 获取启用的API配置
GET http://localhost:5000/api/ApiConfiguration/enabled

### 13. 批量执行API（需要替换为实际的API配置ID数组）
POST http://localhost:5000/api/ApiExecution/batch-execute
Content-Type: application/json

[
  "替换为实际的API配置ID1",
  "替换为实际的API配置ID2"
]

### 14. 清理过期数据（删除30天前的数据）
DELETE http://localhost:5000/api/ApiResponseData/cleanup?olderThanDays=30

### ========== 第二阶段新功能测试 ==========

### 15. 验证数据映射配置
POST http://localhost:5000/api/DataMapping/validate
Content-Type: application/json

{
  "isEnabled": true,
  "rules": [
    {
      "sourcePath": "$.data[*].id",
      "targetField": "user_id",
      "transformType": 1,
      "isRequired": true
    },
    {
      "sourcePath": "$.data[*].name",
      "targetField": "user_name",
      "transformType": 1,
      "defaultValue": "Unknown"
    }
  ],
  "rootPath": "$",
  "keepOriginalData": true,
  "mappedDataName": "mapped_users"
}

### 16. 生成映射预览
POST http://localhost:5000/api/DataMapping/preview
Content-Type: application/json

{
  "sampleData": {
    "data": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "created_at": "2023-12-01T10:30:00Z"
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "created_at": "2023-12-01T11:00:00Z"
      }
    ]
  },
  "mappingConfig": {
    "isEnabled": true,
    "rules": [
      {
        "sourcePath": "$.data[*].id",
        "targetField": "user_id",
        "transformType": 2,
        "isRequired": true
      },
      {
        "sourcePath": "$.data[*].name",
        "targetField": "user_name",
        "transformType": 1
      },
      {
        "sourcePath": "$.data[*].created_at",
        "targetField": "created_date",
        "transformType": 3,
        "transformParameter": "yyyy-MM-ddTHH:mm:ssZ"
      }
    ],
    "rootPath": "$",
    "keepOriginalData": true,
    "mappedDataName": "mapped_users"
  }
}

### 17. 测试数据转换
POST http://localhost:5000/api/DataMapping/test-transform
Content-Type: application/json

{
  "value": "2023-12-01T10:30:00Z",
  "transformType": 3,
  "parameter": "yyyy-MM-dd"
}

### 18. 测试数据过滤
POST http://localhost:5000/api/ResponseProcessing/test-filter
Content-Type: application/json

{
  "testData": [
    {"id": 1, "name": "John", "age": 25, "status": "active"},
    {"id": 2, "name": "Jane", "age": 30, "status": "inactive"},
    {"id": 3, "name": "Bob", "age": 35, "status": "active"}
  ],
  "filterConfig": {
    "isEnabled": true,
    "conditions": [
      {
        "fieldPath": "$.status",
        "operator": 0,
        "value": "active"
      },
      {
        "fieldPath": "$.age",
        "operator": 3,
        "value": "25"
      }
    ],
    "logicalOperator": 0
  }
}

### 19. 测试数据验证
POST http://localhost:5000/api/ResponseProcessing/test-validation
Content-Type: application/json

{
  "testData": [
    {"id": 1, "email": "<EMAIL>", "age": 25},
    {"id": 2, "email": "invalid-email", "age": -5},
    {"id": 3, "email": "<EMAIL>", "age": 30}
  ],
  "validationConfig": {
    "isEnabled": true,
    "rules": [
      {
        "fieldPath": "$.email",
        "type": 3,
        "isRequired": true,
        "errorMessage": "邮箱格式无效"
      },
      {
        "fieldPath": "$.age",
        "type": 1,
        "parameter": "0-120",
        "isRequired": true,
        "errorMessage": "年龄必须在0-120之间"
      }
    ],
    "failureStrategy": 0
  }
}

### 20. 获取支持的数据转换类型
GET http://localhost:5000/api/DataMapping/transform-types

### 21. 获取支持的过滤操作符
GET http://localhost:5000/api/ResponseProcessing/filter-operators

### 22. 获取支持的聚合类型
GET http://localhost:5000/api/ResponseProcessing/aggregation-types

### 23. 更新API配置的数据映射设置（需要替换为实际的API配置ID）
PUT http://localhost:5000/api/DataMapping/api-config/替换为实际的API配置ID/mapping
Content-Type: application/json

{
  "isEnabled": true,
  "rules": [
    {
      "sourcePath": "$.slideshow.slides[*].title",
      "targetField": "slide_title",
      "transformType": 1,
      "isRequired": false
    },
    {
      "sourcePath": "$.slideshow.slides[*].type",
      "targetField": "slide_type",
      "transformType": 1,
      "defaultValue": "unknown"
    }
  ],
  "rootPath": "$.slideshow",
  "keepOriginalData": true,
  "mappedDataName": "processed_slides"
}

### 24. 获取API配置的数据映射设置（需要替换为实际的API配置ID）
GET http://localhost:5000/api/DataMapping/api-config/替换为实际的API配置ID/mapping

### 25. 更新API配置的响应处理设置（需要替换为实际的API配置ID）
PUT http://localhost:5000/api/ResponseProcessing/api-config/替换为实际的API配置ID/processing
Content-Type: application/json

{
  "isEnabled": true,
  "deduplication": {
    "isEnabled": true,
    "strategy": 0,
    "keyFields": ["id"],
    "hashAlgorithm": "SHA256",
    "scopeDays": 30
  },
  "filter": {
    "isEnabled": true,
    "conditions": [
      {
        "fieldPath": "$.status",
        "operator": 0,
        "value": "active"
      }
    ],
    "logicalOperator": 0
  },
  "validation": {
    "isEnabled": true,
    "rules": [
      {
        "fieldPath": "$.id",
        "type": 1,
        "parameter": "1-999999",
        "isRequired": true,
        "errorMessage": "ID必须在1-999999之间"
      }
    ],
    "failureStrategy": 1
  },
  "errorHandling": 0,
  "batchProcessing": {
    "isEnabled": false,
    "batchSize": 100,
    "intervalMs": 1000,
    "maxWaitSeconds": 30
  }
}

### ========== 定时任务调度系统测试 ==========

### 26. 启动调度器
POST http://localhost:5000/api/Scheduler/start

### 27. 获取调度器统计信息
GET http://localhost:5000/api/Scheduler/statistics

### 28. 调度API任务（需要替换为实际的API配置ID）
POST http://localhost:5000/api/Scheduler/schedule/替换为实际的API配置ID
Content-Type: application/json

{
  "type": 1,
  "intervalMinutes": 30,
  "isEnabled": true,
  "priority": 5,
  "timeoutMinutes": 10,
  "retryCount": 3,
  "retryIntervalMinutes": 5,
  "allowConcurrentExecution": false,
  "jobGroup": "API_JOBS",
  "description": "定时执行API任务",
  "timeZone": "UTC",
  "misfireInstruction": 0,
  "maxExecutionCount": 0
}

### 29. 使用Cron表达式调度任务（每天上午9点执行）
POST http://localhost:5000/api/Scheduler/schedule/替换为实际的API配置ID
Content-Type: application/json

{
  "type": 3,
  "cronExpression": "0 0 9 * * ?",
  "isEnabled": true,
  "priority": 8,
  "timeoutMinutes": 15,
  "retryCount": 2,
  "allowConcurrentExecution": false,
  "jobGroup": "DAILY_JOBS",
  "description": "每日上午9点执行API任务",
  "timeZone": "Asia/Shanghai",
  "misfireInstruction": 1
}

### 30. 验证Cron表达式
POST http://localhost:5000/api/Scheduler/validate-cron
Content-Type: application/json

{
  "cronExpression": "0 0 9 * * ?"
}

### 31. 获取Cron表达式的下几次执行时间
POST http://localhost:5000/api/Scheduler/cron-next-times
Content-Type: application/json

{
  "cronExpression": "0 */30 * * * ?",
  "count": 10
}

### 32. 获取所有任务状态
GET http://localhost:5000/api/Scheduler/jobs

### 33. 获取特定任务状态（需要替换为实际的任务键）
GET http://localhost:5000/api/Scheduler/job-status/API_JOBS.api-job-替换为实际的API配置ID

### 34. 立即执行任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/trigger/API_JOBS.api-job-替换为实际的API配置ID

### 35. 暂停任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/pause-job/API_JOBS.api-job-替换为实际的API配置ID

### 36. 恢复任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/resume-job/API_JOBS.api-job-替换为实际的API配置ID

### 37. 获取任务执行历史（需要替换为实际的任务键）
GET http://localhost:5000/api/Scheduler/job-history/API_JOBS.api-job-替换为实际的API配置ID?limit=20

### 38. 重新调度任务（需要替换为实际的任务键）
PUT http://localhost:5000/api/Scheduler/reschedule/API_JOBS.api-job-替换为实际的API配置ID
Content-Type: application/json

{
  "type": 1,
  "intervalMinutes": 60,
  "isEnabled": true,
  "priority": 7,
  "timeoutMinutes": 20,
  "retryCount": 5,
  "allowConcurrentExecution": true,
  "jobGroup": "API_JOBS",
  "description": "重新调度的API任务 - 每小时执行一次"
}

### 39. 取消调度任务（需要替换为实际的任务键）
DELETE http://localhost:5000/api/Scheduler/unschedule/API_JOBS.api-job-替换为实际的API配置ID

### 40. 清理过期执行历史（删除30天前的记录）
DELETE http://localhost:5000/api/Scheduler/cleanup-history?olderThanDays=30

### 41. 暂停调度器
POST http://localhost:5000/api/Scheduler/pause

### 42. 恢复调度器
POST http://localhost:5000/api/Scheduler/resume

### 43. 停止调度器
POST http://localhost:5000/api/Scheduler/stop

### ========== 第三阶段新功能测试 - 数据同步 ==========

### 44. 创建同步配置
POST http://localhost:5000/api/DataSync/configurations
Content-Type: application/json

{
  "apiConfigId": "{{apiConfigId}}",
  "strategy": 2,
  "conflictResolution": 2,
  "enableIncrementalSync": true,
  "incrementalField": "updated_at",
  "uniqueIdentifierField": "id",
  "versionField": "version",
  "checkpointField": "checkpoint",
  "batchSize": 100,
  "maxRetryCount": 3,
  "retryIntervalSeconds": 30,
  "dataRetentionDays": 30,
  "enableDataValidation": true,
  "enableDataCompression": false,
  "customSyncRules": "{\"rules\": []}"
}

### 45. 获取同步配置
GET http://localhost:5000/api/DataSync/configurations/{{syncConfigId}}

### 46. 根据API配置ID获取同步配置
GET http://localhost:5000/api/DataSync/configurations/by-api/{{apiConfigId}}

### 47. 更新同步配置
PUT http://localhost:5000/api/DataSync/configurations/{{syncConfigId}}
Content-Type: application/json

{
  "apiConfigId": "{{apiConfigId}}",
  "strategy": 2,
  "conflictResolution": 4,
  "enableIncrementalSync": true,
  "incrementalField": "modified_time",
  "uniqueIdentifierField": "uuid",
  "batchSize": 200,
  "maxRetryCount": 5,
  "retryIntervalSeconds": 60
}

### 48. 执行增量同步
POST http://localhost:5000/api/DataSync/execute/incremental/{{apiConfigId}}

### 49. 执行增量同步（指定上次同步时间）
POST http://localhost:5000/api/DataSync/execute/incremental/{{apiConfigId}}?lastSyncTime=2023-12-01T10:00:00Z

### 50. 执行全量同步
POST http://localhost:5000/api/DataSync/execute/full/{{apiConfigId}}

### 51. 获取同步会话
GET http://localhost:5000/api/DataSync/sessions/{{sessionId}}

### 52. 获取同步会话历史
GET http://localhost:5000/api/DataSync/sessions/history/{{apiConfigId}}?limit=20

### 53. 获取未解决的冲突
GET http://localhost:5000/api/DataSync/conflicts/unresolved

### 54. 获取特定API的未解决冲突
GET http://localhost:5000/api/DataSync/conflicts/unresolved?apiConfigId={{apiConfigId}}

### 55. 解决数据冲突
POST http://localhost:5000/api/DataSync/conflicts/{{conflictId}}/resolve
Content-Type: application/json

{
  "strategy": 2,
  "resolvedData": {
    "id": "123",
    "name": "Updated Name",
    "version": "2.0"
  }
}

### 56. 获取同步统计信息
GET http://localhost:5000/api/DataSync/statistics/{{apiConfigId}}

### 57. 获取指定时间范围的同步统计信息
GET http://localhost:5000/api/DataSync/statistics/{{apiConfigId}}?startDate=2023-12-01&endDate=2023-12-31

### 58. 重置同步状态
POST http://localhost:5000/api/DataSync/reset/{{apiConfigId}}

### 59. 清理过期的同步数据
DELETE http://localhost:5000/api/DataSync/cleanup?olderThanDays=30

### 60. 删除同步配置
DELETE http://localhost:5000/api/DataSync/configurations/{{syncConfigId}}

### ========== 第三阶段新功能测试 - 监控系统 ==========

### 61. 执行健康检查
POST http://localhost:5000/api/Monitoring/health-check/{{apiConfigId}}

### 62. 批量执行健康检查
POST http://localhost:5000/api/Monitoring/health-check/batch
Content-Type: application/json

{
  "apiConfigIds": ["{{apiConfigId1}}", "{{apiConfigId2}}"]
}

### 63. 获取健康检查历史
GET http://localhost:5000/api/Monitoring/health-check/history/{{apiConfigId}}?limit=50

### 64. 获取指定时间范围的健康检查历史
GET http://localhost:5000/api/Monitoring/health-check/history/{{apiConfigId}}?startTime=2023-12-01T00:00:00Z&endTime=2023-12-31T23:59:59Z&limit=100

### 65. 获取系统健康状态概览
GET http://localhost:5000/api/Monitoring/health-overview

### 66. 记录性能指标
POST http://localhost:5000/api/Monitoring/metrics
Content-Type: application/json

{
  "apiConfigId": "{{apiConfigId}}",
  "apiName": "测试API",
  "metricName": "response_time",
  "value": 150.5,
  "unit": "ms",
  "metricType": "latency",
  "timestamp": "2023-12-01T10:30:00Z"
}

### 67. 批量记录性能指标
POST http://localhost:5000/api/Monitoring/metrics/batch
Content-Type: application/json

[
  {
    "apiConfigId": "{{apiConfigId}}",
    "metricName": "response_time",
    "value": 120.3,
    "unit": "ms",
    "metricType": "latency"
  },
  {
    "apiConfigId": "{{apiConfigId}}",
    "metricName": "availability",
    "value": 1,
    "unit": "boolean",
    "metricType": "availability"
  }
]

### 68. 获取性能指标
GET http://localhost:5000/api/Monitoring/metrics/{{apiConfigId}}?metricName=response_time&limit=100

### 69. 获取性能统计信息
GET http://localhost:5000/api/Monitoring/statistics/{{apiConfigId}}

### 70. 获取API可用性统计
GET http://localhost:5000/api/Monitoring/availability/{{apiConfigId}}

### 71. 获取响应时间趋势
GET http://localhost:5000/api/Monitoring/response-time-trend/{{apiConfigId}}?interval=hour

### 72. 清理过期的监控数据
DELETE http://localhost:5000/api/Monitoring/cleanup?olderThanDays=30

### ========== 第三阶段新功能测试 - 告警系统 ==========

### 73. 创建告警规则
POST http://localhost:5000/api/Alert/rules
Content-Type: application/json

{
  "name": "API响应时间告警",
  "description": "当API响应时间超过5秒时触发告警",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "alertType": "ResponseTime",
  "severity": 3,
  "conditions": "{\"threshold\": 5000, \"operator\": \">\"}",
  "evaluationIntervalMinutes": 5,
  "triggerThreshold": 2,
  "recoveryThreshold": 1,
  "silencePeriodMinutes": 60,
  "notificationChannels": [1, 6],
  "notificationConfig": "{\"email\": \"<EMAIL>\", \"webhook\": \"https://webhook.example.com\"}"
}

### 74. 获取告警规则
GET http://localhost:5000/api/Alert/rules/{{ruleId}}

### 75. 获取所有告警规则
GET http://localhost:5000/api/Alert/rules

### 76. 获取启用的告警规则
GET http://localhost:5000/api/Alert/rules?enabledOnly=true

### 77. 更新告警规则
PUT http://localhost:5000/api/Alert/rules/{{ruleId}}
Content-Type: application/json

{
  "name": "API响应时间告警（更新）",
  "description": "当API响应时间超过3秒时触发告警",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "alertType": "ResponseTime",
  "severity": 2,
  "conditions": "{\"threshold\": 3000, \"operator\": \">\"}",
  "evaluationIntervalMinutes": 3,
  "triggerThreshold": 1,
  "recoveryThreshold": 1,
  "silencePeriodMinutes": 30
}

### 78. 触发告警
POST http://localhost:5000/api/Alert/trigger
Content-Type: application/json

{
  "alertId": "test_alert_001",
  "apiConfigId": "{{apiConfigId}}",
  "title": "API响应时间过长",
  "description": "API响应时间超过阈值",
  "severity": 3,
  "alertType": "ResponseTime",
  "source": "MonitoringSystem"
}

### 79. 确认告警
POST http://localhost:5000/api/Alert/acknowledge/test_alert_001
Content-Type: application/json

{
  "acknowledgedBy": "admin",
  "notes": "已确认，正在处理"
}

### 80. 解决告警
POST http://localhost:5000/api/Alert/resolve/test_alert_001
Content-Type: application/json

{
  "resolvedBy": "admin",
  "resolutionNotes": "已优化API性能，问题解决"
}

### 81. 获取活跃告警
GET http://localhost:5000/api/Alert/active

### 82. 获取特定严重程度的活跃告警
GET http://localhost:5000/api/Alert/active?severity=3

### 83. 获取特定API的活跃告警
GET http://localhost:5000/api/Alert/active?apiConfigId={{apiConfigId}}

### 84. 获取告警历史
GET http://localhost:5000/api/Alert/history?limit=50

### 85. 获取特定API的告警历史
GET http://localhost:5000/api/Alert/history?apiConfigId={{apiConfigId}}&limit=20

### 86. 获取指定时间范围的告警历史
GET http://localhost:5000/api/Alert/history?startTime=2023-12-01T00:00:00Z&endTime=2023-12-31T23:59:59Z

### 87. 评估告警规则
POST http://localhost:5000/api/Alert/evaluate

### 88. 评估特定API的告警规则
POST http://localhost:5000/api/Alert/evaluate/{{apiConfigId}}

### 89. 获取告警统计信息
GET http://localhost:5000/api/Alert/statistics

### 90. 获取指定时间范围的告警统计信息
GET http://localhost:5000/api/Alert/statistics?startTime=2023-12-01T00:00:00Z&endTime=2023-12-31T23:59:59Z

### 91. 静默告警
POST http://localhost:5000/api/Alert/silence/test_alert_001
Content-Type: application/json

{
  "silenceDurationMinutes": 120,
  "silencedBy": "admin",
  "reason": "维护期间静默"
}

### 92. 取消静默告警
POST http://localhost:5000/api/Alert/unsilence/test_alert_001
Content-Type: application/json

{
  "unsilencedBy": "admin"
}

### 93. 清理过期的告警数据
DELETE http://localhost:5000/api/Alert/cleanup?olderThanDays=90

### 94. 删除告警规则
DELETE http://localhost:5000/api/Alert/rules/{{ruleId}}

###############################################
# 限流管理 API 测试
###############################################

### 95. 创建限流规则
POST http://localhost:5000/api/RateLimit/rules
Content-Type: application/json

{
  "name": "API限流规则",
  "description": "测试API的限流规则",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "limitType": 1,
  "limitDimension": "IP",
  "limitThreshold": 100,
  "timeWindowSeconds": 60,
  "priority": 100,
  "matchConditions": "{}",
  "responseConfig": "{\"message\": \"请求过于频繁，请稍后再试\"}"
}

### 96. 获取所有限流规则
GET http://localhost:5000/api/RateLimit/rules

### 97. 获取启用的限流规则
GET http://localhost:5000/api/RateLimit/rules?enabledOnly=true

### 98. 获取限流规则详情
GET http://localhost:5000/api/RateLimit/rules/{{rateLimitRuleId}}

### 99. 更新限流规则
PUT http://localhost:5000/api/RateLimit/rules/{{rateLimitRuleId}}
Content-Type: application/json

{
  "name": "更新的限流规则",
  "description": "更新后的限流规则描述",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "limitType": 1,
  "limitDimension": "IP",
  "limitThreshold": 200,
  "timeWindowSeconds": 60,
  "priority": 90
}

### 100. 检查限流状态
GET http://localhost:5000/api/RateLimit/check/{{apiConfigId}}?clientIdentifier=test-client&requestPath=/api/test

### 101. 获取限流统计信息
GET http://localhost:5000/api/RateLimit/statistics?apiConfigId={{apiConfigId}}

### 102. 重置限流计数器
POST http://localhost:5000/api/RateLimit/reset?apiConfigId={{apiConfigId}}&clientIdentifier=test-client

### 103. 获取客户端限流状态
GET http://localhost:5000/api/RateLimit/status?apiConfigId={{apiConfigId}}&clientIdentifier=test-client

### 104. 清理过期限流数据
POST http://localhost:5000/api/RateLimit/cleanup?olderThanHours=24

### 105. 删除限流规则
DELETE http://localhost:5000/api/RateLimit/rules/{{rateLimitRuleId}}

###############################################
# 熔断器管理 API 测试
###############################################

### 106. 创建熔断器配置
POST http://localhost:5000/api/CircuitBreaker/configs
Content-Type: application/json

{
  "name": "API熔断器配置",
  "description": "测试API的熔断器配置",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "failureThreshold": 5,
  "successThreshold": 3,
  "timeoutMs": 30000,
  "openDurationSeconds": 60,
  "halfOpenMaxRequests": 10,
  "statisticsWindowSeconds": 60,
  "minimumThroughput": 10,
  "errorRateThreshold": 50.0,
  "slowCallThresholdMs": 10000,
  "slowCallRateThreshold": 50.0
}

### 107. 获取所有熔断器配置
GET http://localhost:5000/api/CircuitBreaker/configs

### 108. 获取启用的熔断器配置
GET http://localhost:5000/api/CircuitBreaker/configs?enabledOnly=true

### 109. 获取熔断器配置详情
GET http://localhost:5000/api/CircuitBreaker/configs/{{circuitBreakerConfigId}}

### 110. 根据API配置ID获取熔断器配置
GET http://localhost:5000/api/CircuitBreaker/configs/api/{{apiConfigId}}

### 111. 更新熔断器配置
PUT http://localhost:5000/api/CircuitBreaker/configs/{{circuitBreakerConfigId}}
Content-Type: application/json

{
  "name": "更新的熔断器配置",
  "description": "更新后的熔断器配置描述",
  "apiConfigId": "{{apiConfigId}}",
  "isEnabled": true,
  "failureThreshold": 10,
  "successThreshold": 5,
  "timeoutMs": 30000,
  "openDurationSeconds": 120
}

### 112. 检查熔断器状态
GET http://localhost:5000/api/CircuitBreaker/check/{{apiConfigId}}

### 113. 获取熔断器状态
GET http://localhost:5000/api/CircuitBreaker/status/{{apiConfigId}}

### 114. 手动打开熔断器
POST http://localhost:5000/api/CircuitBreaker/open/{{apiConfigId}}
Content-Type: application/json

"手动打开熔断器进行测试"

### 115. 手动关闭熔断器
POST http://localhost:5000/api/CircuitBreaker/close/{{apiConfigId}}
Content-Type: application/json

"手动关闭熔断器"

### 116. 重置熔断器状态
POST http://localhost:5000/api/CircuitBreaker/reset/{{apiConfigId}}

### 117. 获取熔断器统计信息
GET http://localhost:5000/api/CircuitBreaker/statistics?apiConfigId={{apiConfigId}}

### 118. 记录请求结果
POST http://localhost:5000/api/CircuitBreaker/record?apiConfigId={{apiConfigId}}&isSuccess=true&responseTimeMs=500

### 119. 删除熔断器配置
DELETE http://localhost:5000/api/CircuitBreaker/configs/{{circuitBreakerConfigId}}

###############################################
# API网关 API 测试
###############################################

### 120. 处理网关请求
POST http://localhost:5000/api/ApiGateway/process?apiConfigId={{apiConfigId}}
Content-Type: application/json
X-API-Key: test-api-key-12345678901234567890

{
  "testData": "网关请求测试数据"
}

### 121. 获取网关日志
GET http://localhost:5000/api/ApiGateway/logs?limit=50

### 122. 获取指定API的网关日志
GET http://localhost:5000/api/ApiGateway/logs?apiConfigId={{apiConfigId}}&limit=20

### 123. 获取时间范围内的网关日志
GET http://localhost:5000/api/ApiGateway/logs?startTime=2024-01-01T00:00:00Z&endTime=2024-12-31T23:59:59Z&limit=100

### 124. 获取网关统计信息
GET http://localhost:5000/api/ApiGateway/statistics

### 125. 获取指定API的网关统计信息
GET http://localhost:5000/api/ApiGateway/statistics?apiConfigId={{apiConfigId}}

### 126. 获取时间范围内的网关统计信息
GET http://localhost:5000/api/ApiGateway/statistics?startTime=2024-01-01T00:00:00Z&endTime=2024-12-31T23:59:59Z

### 127. 清理过期网关日志
POST http://localhost:5000/api/ApiGateway/cleanup?olderThanDays=30

### 128. 验证API密钥
POST http://localhost:5000/api/ApiGateway/validate-key?apiKey=test-api-key-12345678901234567890

### 129. 获取客户端标识
GET http://localhost:5000/api/ApiGateway/client-identifier

### 130. 网关健康检查
GET http://localhost:5000/api/ApiGateway/health

### 131. 获取网关信息
GET http://localhost:5000/api/ApiGateway/info
