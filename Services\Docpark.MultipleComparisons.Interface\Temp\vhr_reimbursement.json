{"fonts": [{"name": "simsun", "file": "simsun.ttf"}], "margin": {"left": 10, "top": 10, "right": 10}, "footer": {"separator": -25, "content": [{"cell": {"position": {"x": 0, "y": -10}, "text_color": "0,0,0", "align": "C", "font": {"family": "simsun", "size": 12}, "format": "{page_no}/{nb}"}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": -20}, "to": {"x": -10, "y": -20}}}]}, "header": {"separator": 25, "content": []}, "contents": [{"content": [{"cell": {"position": {"x": 80, "y": 30}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 16}, "txt": "日常报销单"}}]}, {"source": "main_src1", "content": [{"cell": {"position": {"x": 10, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "报销单号: "}}, {"cell": {"position": {"x": 51, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt_bandding": "no"}}]}, {"source": "main_src2", "content": [{"cell": {"position": {"x": 10, "y": 60}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "员工: "}}, {"cell": {"position": {"x": 51, "y": 60}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt_bandding": "staff"}}, {"cell": {"position": {"x": 110, "y": 60}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "部门: "}}, {"cell": {"position": {"x": 145, "y": 60}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt_bandding": "department"}}]}, {"source": "main_src3", "content": [{"cell": {"position": {"x": 10, "y": 70}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "最终审批: "}}, {"cell": {"position": {"x": 51, "y": 70}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "刘海"}}, {"cell": {"position": {"x": 110, "y": 70}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "提交日期: "}}, {"cell": {"position": {"x": 145, "y": 70}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt_bandding": "date"}}]}, {"source": "main_src4", "content": [{"cell": {"position": {"x": 10, "y": 80}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "总金额(小写): "}}, {"cell": {"position": {"x": 51, "y": 80}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt_bandding": "total"}}]}, {"content": [{"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 45}, "to": {"x": 202, "y": 45}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 55}, "to": {"x": 202, "y": 55}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 65}, "to": {"x": 202, "y": 65}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 75}, "to": {"x": 202, "y": 75}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 85}, "to": {"x": 202, "y": 85}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 95}, "to": {"x": 202, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 45}, "to": {"x": 10, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 50, "y": 45}, "to": {"x": 50, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 105, "y": 45}, "to": {"x": 105, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 144, "y": 45}, "to": {"x": 144, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 202, "y": 45}, "to": {"x": 202, "y": 95}}}]}], "repeaters": [{"source": "list_src", "repeater": []}]}