using System;
using System.Collections.Generic;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Models
{
    /// <summary>
    /// 熔断器结果
    /// </summary>
    public class CircuitBreakerResult
    {
        /// <summary>
        /// 是否允许请求
        /// </summary>
        public bool IsAllowed { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public CircuitBreakerState State { get; set; }

        /// <summary>
        /// 拒绝原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 下次重试时间
        /// </summary>
        public DateTime? NextRetryTime { get; set; }

        /// <summary>
        /// 失败计数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 成功计数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
