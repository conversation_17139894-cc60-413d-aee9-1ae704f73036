using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// API执行控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ApiExecutionController : ControllerBase
    {
        private readonly IApiExecutionService _executionService;
        private readonly ILogger<ApiExecutionController> _logger;

        public ApiExecutionController(
            IApiExecutionService executionService,
            ILogger<ApiExecutionController> logger)
        {
            _executionService = executionService;
            _logger = logger;
        }

        /// <summary>
        /// 手动执行API
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <returns></returns>
        [HttpPost("{apiConfigId}/execute")]
        public async Task<ActionResult<ApiExecutionResult>> ExecuteApi(string apiConfigId)
        {
            try
            {
                var executedBy = User?.Identity?.Name ?? "Anonymous";
                var result = await _executionService.ExecuteManuallyAsync(apiConfigId, executedBy);
                
                if (result.IsSuccess)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing API: {ApiConfigId}", apiConfigId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量执行API
        /// </summary>
        /// <param name="apiConfigIds"></param>
        /// <returns></returns>
        [HttpPost("batch-execute")]
        public async Task<ActionResult<List<ApiExecutionResult>>> ExecuteBatch([FromBody] List<string> apiConfigIds)
        {
            try
            {
                if (apiConfigIds == null || apiConfigIds.Count == 0)
                {
                    return BadRequest("API configuration IDs are required");
                }

                var results = await _executionService.ExecuteBatchAsync(apiConfigIds);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing batch APIs");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <returns></returns>
        [HttpPost("{apiConfigId}/test")]
        public async Task<ActionResult> TestConnection(string apiConfigId)
        {
            try
            {
                var result = await _executionService.TestApiConnectionAsync(apiConfigId);
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing API connection: {ApiConfigId}", apiConfigId);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
