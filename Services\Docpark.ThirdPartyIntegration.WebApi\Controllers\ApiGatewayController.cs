using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// API网关控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ApiGatewayController : ControllerBase
    {
        private readonly IApiGatewayService _apiGatewayService;
        private readonly ILogger<ApiGatewayController> _logger;

        public ApiGatewayController(IApiGatewayService apiGatewayService, ILogger<ApiGatewayController> logger)
        {
            _apiGatewayService = apiGatewayService;
            _logger = logger;
        }

        /// <summary>
        /// 处理网关请求
        /// </summary>
        [HttpPost("process")]
        public async Task<IActionResult> ProcessGatewayRequest([FromQuery] string apiConfigId)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                var response = await _apiGatewayService.ProcessRequestAsync(HttpContext, apiConfigId);
                
                // 设置响应头
                foreach (var header in response.Headers)
                {
                    Response.Headers[header.Key] = header.Value;
                }

                return StatusCode(response.StatusCode, response.Body);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理网关请求失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "处理网关请求失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取网关日志
        /// </summary>
        [HttpGet("logs")]
        public async Task<IActionResult> GetGatewayLogs(
            [FromQuery] string apiConfigId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int limit = 100)
        {
            try
            {
                var logs = await _apiGatewayService.GetGatewayLogsAsync(apiConfigId, startTime, endTime, limit);
                return Ok(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网关日志失败");
                return StatusCode(500, new { message = "获取网关日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取网关统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetGatewayStatistics(
            [FromQuery] string apiConfigId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _apiGatewayService.GetGatewayStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取网关统计信息失败");
                return StatusCode(500, new { message = "获取网关统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的网关日志
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupExpiredGatewayLogs([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var deletedCount = await _apiGatewayService.CleanupExpiredGatewayLogsAsync(olderThanDays);
                return Ok(new { message = "清理完成", deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期网关日志失败");
                return StatusCode(500, new { message = "清理过期网关日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验证API密钥
        /// </summary>
        [HttpPost("validate-key")]
        public async Task<IActionResult> ValidateApiKey([FromQuery] string apiKey, [FromQuery] string apiConfigId = null)
        {
            try
            {
                var isValid = await _apiGatewayService.ValidateApiKeyAsync(apiKey, apiConfigId);
                return Ok(new { isValid, message = isValid ? "API密钥有效" : "API密钥无效" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证API密钥失败");
                return StatusCode(500, new { message = "验证API密钥失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取客户端标识
        /// </summary>
        [HttpGet("client-identifier")]
        public IActionResult GetClientIdentifier()
        {
            try
            {
                var clientIdentifier = _apiGatewayService.GetClientIdentifier(HttpContext);
                return Ok(new { clientIdentifier });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端标识失败");
                return StatusCode(500, new { message = "获取客户端标识失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        [HttpGet("health")]
        public IActionResult HealthCheck()
        {
            return Ok(new 
            { 
                status = "healthy", 
                timestamp = DateTime.UtcNow,
                service = "API Gateway"
            });
        }

        /// <summary>
        /// 获取网关信息
        /// </summary>
        [HttpGet("info")]
        public IActionResult GetGatewayInfo()
        {
            return Ok(new
            {
                name = "Docpark Third Party Integration API Gateway",
                version = "1.0.0",
                description = "企业级第三方接口集成API网关",
                features = new[]
                {
                    "请求限流",
                    "熔断保护",
                    "负载均衡",
                    "请求日志",
                    "统计分析"
                },
                timestamp = DateTime.UtcNow
            });
        }
    }
}
