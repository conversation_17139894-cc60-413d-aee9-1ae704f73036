﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class VoucherService : IVoucherService
    {
        private readonly IObjectQuery _objectQuery;
        private readonly IConfiguration _configuration;
        private readonly IComHttpClient _comHttpClient;
        private readonly ILogger<VoucherService> _logger;
        public VoucherService(IObjectQuery objectQuery, IConfiguration configuration, IComHttpClient comHttpClient, ILogger<VoucherService> logger)
        {
            _objectQuery = objectQuery;
            _configuration = configuration;
            _comHttpClient = comHttpClient;
            _logger = logger;
        }

        /// <summary>
        /// 生成记账凭证
        /// </summary>
        /// <param name="inputDto"></param>
        /// <returns></returns>
        public async Task<ResponseResult> GenerateVoucherInfo(RequestVoucherDto inputDto)
        {
            var result = new ResponseResult() { isSuccess = false };
            var accountingVoucherId = "AccountingVoucher";//凭证表单标识
            try
            {
                if (inputDto == null && inputDto.BillNos.Count == 0)
                {
                    result.msg = "参数不能为空";
                    return result;
                }
                var ids = inputDto.BillNos.ToArray();

                var filter = new List<Filter>();

                filter.Add(new Filter()
                {
                    DisplayType = "文本",
                    Field = "ArchivesKey",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = ids
                });

                filter.Add(new Filter()
                {
                    DisplayType = "文本",
                    Field = "state",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "比对完成" }
                });

                var sort = new List<Sorter>()
                {
                  new Sorter()
                    {
                        Field = "application_time",
                        Order = "descend"
                    }
                };

                //获取报销单数据
                var reimbursementList = await _objectQuery.GetList<Reimbursement>(filter, sort, 0, 100);
                if (inputDto.BillNos.Count > 0 && inputDto.BillNos.Count != reimbursementList.totalCount)
                {
                    result.msg = "选中的数据存在状态不是对比完成，请重新选择";
                    return result;
                }

                var list = new List<AccountingVoucher>();
                var applicant = "张一";

                if (reimbursementList.data.Count > 0)
                {
                    int VoucherNumber = await GetMaxVoucherNo();
                    foreach (var item in reimbursementList.data)
                    {
                        VoucherNumber++;
                        if (item == null) continue;

                        var data = new AccountingVoucher();
                        var now = DateTime.Now;
                        data.MstId = Guid.NewGuid();
                        data.CompanyName = item.company;
                        data.AccountingUnit = item.company;
                        data.PreparationUnit = item.company;
                        data.CompanyId = "CN04";
                        data.Year = now.Year;
                        data.Month = now.Month;
                        data.VoucherNo = VoucherNumber.ToString().PadLeft(8, '0');
                        data.Bookkeeping = "张二";
                        data.Reviewer = "张二";
                        data.ArchivesKey = $"{data.VoucherNo}{data.Year}{data.Month}";
                        data.Status = "已收集";
                        data.Maker = applicant;
                        data.KeepYear = "10年";
                        data.CreateDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        data.AnnexNumber = 1;
                        data.SecretLevel = "公开";

                        var details = new List<VoucherDetail>();
                        details.Add(new VoucherDetail()
                        {
                            Summary = "创建人_日常报销",
                            AccountingSubjects = "5501 差旅费",
                            Debit = item.totalamount,
                            Lender = "0"
                        });

                        details.Add(new VoucherDetail()
                        {
                            Summary = "创建人_日常报销",
                            AccountingSubjects = "2181 其他应付款",
                            Debit = "0",
                            Lender = item.totalamount
                        });

                        data.VoucherDetail = details;
                        list.Add(data);

                        item.VoucherNo = data.VoucherNo;
                    }
                }

                if (list.Count == 0)
                {
                    result.msg = "没有生成记账凭证的数据，请重试";
                    return result;
                }

                //插入记账凭证
                var flag = await _objectQuery.BulkCreateOrUpdate<AccountingVoucher>(list);
                if (flag)
                {
                    var appKey = _configuration.GetValue<string>("VoucherAppKey");
                    var appSecret = _configuration.GetValue<string>("VoucherAppSecret");
                    var timestamp = GetTimeStamp().ToString();

                    var requestModel = await _comHttpClient.GetReimbursementConfig();

                    for (var i = 0; i < list.Count; i++)
                    {
                        var item = list[i];

                        //生成记账凭证pdf,上传
                        var (voucherFileName, voucherPdfPath) = await GenerateVoucherPdf(item);
                        if (!string.IsNullOrEmpty(voucherFileName) && !string.IsNullOrEmpty(voucherPdfPath))
                        {
                            var uploadPdfDto = new UploadDto()
                            {
                                AppKey = appKey,
                                Timestamp = timestamp,
                                AppSecret = appSecret,
                                Token = Md5Token(appKey, appSecret, timestamp),
                                FileName = voucherFileName,
                                FileStream = voucherPdfPath,
                                FileType = "1",
                                DocTypeID = accountingVoucherId,//"AccountingVoucher",
                                DocumentNo = item.ArchivesKey,
                                ClientNo = "client000000001",
                                FileLabel = "上传记账凭证Pdf附件"
                            };

                            var uploadPdfResult = await SendFileInfo(uploadPdfDto, _configuration.GetValue<string>("UploadFileURL"));
                            var uploadPdfResultJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(uploadPdfResult);
                            //上传成功就删除本地文件
                            if (uploadPdfResultJson.ContainsKey("code"))
                            {
                                if (uploadPdfResultJson["code"].Equals("0")) DeleteFile(voucherPdfPath);
                                else _logger.LogError($"上传记账凭证PDF附件异常，异常明细:{uploadPdfResult}");
                            }
                        }

                        //voucher flow
                        var archivesKey = "";
                        var billNo = "";
                        if (i < reimbursementList.totalCount)
                        {
                            var reimbursement = reimbursementList.data[i];
                            if (reimbursement != null)
                            {
                                archivesKey = reimbursement.ArchivesKey;
                                billNo = reimbursement.ArchivesKey;
                            }
                        }

                        var flow = new VoucherFlowInputDto()
                        {
                            AppKey = appKey,
                            Timestamp = timestamp,
                            AppSecret = appSecret,
                            Token = Md5Token(appKey, appSecret, timestamp),
                            Data = new List<VoucherFlowDto>()
                            {
                                new VoucherFlowDto()
                                {
                                    ParentDocTypeID = accountingVoucherId,//"AccountingVoucher",
                                    ParentArchivesKey = item.ArchivesKey,
                                    DocTypeID = "Reimbursement",
                                    ArchivesKey = archivesKey
                                }
                            }
                        };
                        var urlFlow = (requestModel == null ? "" : requestModel.ArchivesBaseUrl) + _configuration.GetValue<string>("SendVoucherFlowUrl");
                        var resultFlow = await SendInfo(flow, urlFlow);
                        var resultFlowJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(resultFlow);
                        if (resultFlowJson.ContainsKey("code"))
                        {
                            if (!resultFlowJson["code"].ToString().Equals("0")) _logger.LogError($"添加MetaDataRelation关系异常，异常明细:{resultFlow}");
                        }

                        //upload file
                        timestamp = GetTimeStamp().ToString();

                        if (!string.IsNullOrEmpty(billNo))
                        {
                            var (billInvoiceList, count) = await GetBillInvoiceData(billNo);

                            if (billInvoiceList != null && count > 0)
                            {
                                foreach (var bill in billInvoiceList)
                                {
                                    var detailItems = bill.annex_items;
                                    if (detailItems != null)
                                    {
                                        foreach (var detail in detailItems)
                                        {
                                            var url = detail.annex_details;
                                            if (string.IsNullOrEmpty(url) || url.IndexOf("http") < 0) continue;
                                            var documentId = url.Substring(url.LastIndexOf("=") + 1);
                                            if (string.IsNullOrEmpty(documentId)) continue;
                                            var fileInfo = _comHttpClient.GetDocumentURL(documentId);
                                            var jsonResult = JsonConvert.DeserializeObject<Dictionary<string, string>>(fileInfo);
                                            var fileName = @"1.jpg";
                                            if (jsonResult.ContainsKey("fileName")) fileName = jsonResult["fileName"].ToString();
                                            var (_fileName, filePath) = DownloadFile(url, "Temp", fileName);

                                            var uploadDto = new UploadDto()
                                            {
                                                AppKey = appKey,
                                                Timestamp = timestamp,
                                                AppSecret = appSecret,
                                                Token = Md5Token(appKey, appSecret, timestamp),
                                                FileName = fileName,
                                                FileStream = filePath,
                                                FileType = "1",
                                                DocTypeID = "Reimbursement",
                                                DocumentNo = archivesKey,
                                                ClientNo = "client000000001",
                                                FileLabel = "上传发票附件"
                                            };

                                            var uploadResult = await SendFileInfo(uploadDto, _configuration.GetValue<string>("UploadFileURL"));
                                            var uploadResultJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(uploadResult);
                                            //上传成功就删除本地文件
                                            if (uploadResultJson.ContainsKey("code"))
                                            {
                                                if (uploadResultJson["code"].Equals("0")) DeleteFile(filePath);
                                                else _logger.LogError($"上传发票附件异常，异常明细:{resultFlow}");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //生成报销单PDF
                    for (var i = 0; i < reimbursementList.data.Count; i++)
                    {
                        var item = reimbursementList.data[i];
                        var voucherItem = list[i];
                        if (item == null || voucherItem == null) continue;

                        //生成记账凭证pdf,上传
                        var (reimbursementFileName, reimbursementPdfPath) = await GenerateReimbursementPdf(item);
                        if (!string.IsNullOrEmpty(reimbursementFileName) && !string.IsNullOrEmpty(reimbursementPdfPath))
                        {
                            timestamp = GetTimeStamp().ToString();
                            var uploadPdfDto = new UploadDto()
                            {
                                AppKey = appKey,
                                Timestamp = timestamp,
                                AppSecret = appSecret,
                                Token = Md5Token(appKey, appSecret, timestamp),
                                FileName = reimbursementFileName,
                                FileStream = reimbursementPdfPath,
                                FileType = "1",
                                DocTypeID = accountingVoucherId,
                                DocumentNo = voucherItem.ArchivesKey,
                                ClientNo = "client000000001",
                                FileLabel = "上传报销单PDF附件"
                            };

                            var uploadPdfResult = await SendFileInfo(uploadPdfDto, _configuration.GetValue<string>("UploadFileURL"));
                            var uploadPdfResultJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(uploadPdfResult);
                            //上传成功就删除本地文件
                            if (uploadPdfResultJson.ContainsKey("code"))
                            {
                                if (uploadPdfResultJson["code"].Equals("0")) DeleteFile(reimbursementPdfPath);
                                else _logger.LogError($"上传记账凭证PDF附件异常，异常明细:{uploadPdfResult}");
                            }
                        }
                    }

                    //修改报销单状态
                    if (reimbursementList.data.Count > 0)
                    {
                        var updateData = new List<Reimbursement>();
                        foreach (var item in reimbursementList.data)
                        {
                            item.state = "已收集";
                            updateData.Add(item);
                        }

                        if (updateData.Count > 0)
                        {
                            var flagReimburse = await _objectQuery.BulkCreateOrUpdate<Reimbursement>(reimbursementList.data);
                            if (flagReimburse == false) _logger.LogError("修改报销单状态失败");
                            else
                            {
                                foreach (var item in reimbursementList.data)
                                {
                                    //修改票夹状态Status   0：草稿   1：提交中   2：提交
                                    //var requestModel = await _comHttpClient.GetReimbursementConfig();
                                    var thirdticketrUrlSStatus = requestModel == null ? "" : requestModel.ThirdticketholderUrl;
                                    Dictionary<string, object> dic = new Dictionary<string, object>()
                                    {
                                        ["Id"] = item.tic_id,
                                        ["Name"] = "",
                                        ["Owner"] = 2,
                                        ["Status"] = 2
                                    };

                                    var requestData = _comHttpClient.Post(thirdticketrUrlSStatus + $"invoice/folder/SetStatus", dic);
                                    var resData = JsonConvert.DeserializeObject<Dictionary<string, object>>(requestData);
                                    if (resData.ContainsKey("Code") && resData["Code"] != null && resData["Code"].ToString() == "0")
                                    {
                                        _logger.LogError("生成记账凭证时, 修改票夹(票夹MstId"+ item.tic_id + ")状态失败。" + (resData.ContainsKey("Code") && resData["Code"] != null && !string.IsNullOrEmpty(resData["Code"].ToString()) ? resData["Code"].ToString() : ""));
                                    }
                                }
                            }
                        }
                    }

                    result.isSuccess = flag;
                    result.msg = "生成记账凭证成功";
                }
                else
                {
                    result.isSuccess = flag;
                    result.msg = "生成记账凭证失败";
                    _logger.LogError(result.msg);
                }
            }
            catch (Exception ex)
            {
                result.msg = "生成记账凭证异常，明细:" + ex.Message;
                _logger.LogError("生成记账凭证异常," + ex.StackTrace.ToString());
            }
            return result;
        }

        public async Task<(string, string)> GenerateVoucherPdf(AccountingVoucher accountingVoucher)
        {
            var requestModel = await _comHttpClient.GetReimbursementConfig();
            var pdfApiBaseUrl = requestModel == null ? "" : requestModel.PdfApiBaseUrl;  //.GetAllConfig("ReimbursementConfigManagement.PdfApiBaseUrl");//_configuration.GetValue<string>("PdfApiBaseUrl");
            //1、上传模板json
            var templateName = "vhr"; //$"vhr{DateTime.Now.ToString("yyyyMMddHHmmss")}";
            var fullPath = GetFileFullPath("Temp/" + templateName + ".json");//CopyTemplateFile("Temp/" + templateName + ".json");
            if (string.IsNullOrEmpty(fullPath)) _logger.LogError($"记账凭证,新建模板失败。");

            var uploadTemplateJsonUrl = pdfApiBaseUrl + "contract/template/upload/";
            var files = new Dictionary<string, string>();
            files.Add("template_file", fullPath);
            var uploadTemplate = await UploadFileInfo(uploadTemplateJsonUrl, files, new Dictionary<string, string>());
            var uploadTemplateJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(uploadTemplate);
            if (uploadTemplateJson.ContainsKey("code"))
            {
                if (!uploadTemplateJson["code"].ToString().Equals("0"))
                {
                    _logger.LogError($"记账凭证,上传模板失败。异常明细:{uploadTemplate}"); //DeleteFile(fullPath); 
                    return ("", "");
                }
            }

            //2、生成pdf
            var newFileName = $"Voucher{DateTime.Now.ToString("yyyyMMddHHmmss")}";
            var createPdfUrl = $"{pdfApiBaseUrl}contract/resule/create/{newFileName}/{templateName}/";
            var pdfData = new PdfDataSourceDto();
            var sources = new List<SourceDto>();
            sources.Add(new SourceDto()
            {
                Name = "main_src1",
                Source = new
                {
                    prepared_by = accountingVoucher.PreparationUnit,
                    prepared_date = DateTime.Now.ToString("yyyy/MM/dd"),
                    accounted_by = accountingVoucher.AccountingUnit,
                    number = "0001",
                    total_number = "0001"
                }
            });

            sources.Add(new SourceDto()
            {
                Name = "main_src2",
                Source = new
                {
                    treasurer = "",
                    accounter = accountingVoucher.Bookkeeping,
                    reviewer = accountingVoucher.Reviewer,
                    cashier = "",
                    maker = accountingVoucher.Maker,
                    handler = ""
                }
            });

            var repeaterSourceDetailList = new List<RepeaterSourceDetailDto>();
            if (accountingVoucher.VoucherDetail != null)
            {
                foreach (var item in accountingVoucher.VoucherDetail)
                {
                    if (item == null) continue;
                    repeaterSourceDetailList.Add(new RepeaterSourceDetailDto()
                    {
                        Abstract = item.Summary,
                        Subject = item.AccountingSubjects,
                        Debit = item.Debit,
                        Credit = item.Lender
                    });
                }
            }

            pdfData.Sources = sources;
            pdfData.RepeaterSources = new List<RepeaterSourceDto>()
            {
               new RepeaterSourceDto()
               {
                Name="list_src",
                RepeaterSource=repeaterSourceDetailList
               }
            };

            var createPdtResult = await SendInfo(pdfData, createPdfUrl, true);
            var createPdtResultJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(createPdtResult);
            if (createPdtResultJson.ContainsKey("code"))
            {
                if (!createPdtResultJson["code"].ToString().Equals("0"))
                {
                    _logger.LogError($"记账凭证,生成PDF失败。异常明细:{createPdtResult}");
                    return ("", "");
                }
            }

            //3、获取pdf
            var downloadPdfUrl = $"{pdfApiBaseUrl}contract/resule/pdf/{newFileName}/{templateName}/";
            return DownloadFile(downloadPdfUrl, "Temp", newFileName + ".pdf");
        }

        private async Task<(string, string)> GenerateReimbursementPdf(Reimbursement reimbursement)
        {
            var requestModel = await _comHttpClient.GetReimbursementConfig();
            var pdfApiBaseUrl = requestModel == null ? "" : requestModel.PdfApiBaseUrl;
            //1、上传模板json
            var templateName = "vhr_reimbursement";
            var fullPath = GetFileFullPath("Temp/" + templateName + ".json");
            if (string.IsNullOrEmpty(fullPath)) _logger.LogError($"报销单模板不存在");

            var uploadTemplateJsonUrl = pdfApiBaseUrl + "contract/template/upload/";
            var files = new Dictionary<string, string>();
            files.Add("template_file", fullPath);
            var uploadTemplate = await UploadFileInfo(uploadTemplateJsonUrl, files, new Dictionary<string, string>());
            var uploadTemplateJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(uploadTemplate);
            if (uploadTemplateJson.ContainsKey("code"))
            {
                if (!uploadTemplateJson["code"].ToString().Equals("0"))
                {
                    _logger.LogError($"报销单上传模板失败。异常明细:{uploadTemplate}");
                    return ("", "");
                }
            }

            //2、生成pdf
            var newFileName = $"Reimbursement{DateTime.Now.ToString("yyyyMMddHHmmss")}";
            var createPdfUrl = $"{pdfApiBaseUrl}contract/resule/create/{newFileName}/{templateName}/";
            var pdfData = new
            {
                sources = new List<object>()
                {
                    new
                    {
                        name="main_src1",
                        source=new{
                            no=reimbursement.ArchivesKey
                        }
                    },
                    new {
                        name="main_src2",
                        source=new{
                            staff=reimbursement.applicant,
                            department=reimbursement.department
                        }
                    },
                    new {
                        name="main_src3",
                        source=new{
                            date=string.IsNullOrEmpty(reimbursement.application_time)?DateTime.Now.ToString("yyyy-MM-dd"):reimbursement.application_time.Split(" ")[0]
                        }
                    },
                    new { 
                        name="main_src4",
                        source=new{
                            total=$"CNY {reimbursement.totalamount}"
                        }
                    }
                }
            };

            var createPdtResult = await SendInfo(pdfData, createPdfUrl, true);
            var createPdtResultJson = JsonConvert.DeserializeObject<Dictionary<string, string>>(createPdtResult);
            if (createPdtResultJson.ContainsKey("code"))
            {
                if (!createPdtResultJson["code"].ToString().Equals("0"))
                {
                    _logger.LogError($"报销单生成PDF失败。异常明细:{createPdtResult}");
                    return ("", "");
                }
            }

            //3、获取pdf
            var downloadPdfUrl = $"{pdfApiBaseUrl}contract/resule/pdf/{newFileName}/{templateName}/";
            return DownloadFile(downloadPdfUrl, "Temp", newFileName + ".pdf");
        }

        /// <summary>
        /// 获取最大凭证号
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxVoucherNo()
        {
            var filter = new List<Filter>();
            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "CompanyId",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { "CN04" }
            });

            var sortList = new List<Sorter>();
            sortList.Add(new Sorter()
            {
                Field = "VoucherNo",
                Order = "descend"
            });

            sortList.Add(new Sorter()
            {
                Field = "VoucherNo",
                Order = "descend"
            });

            var maxVoucherNo = 100;
            var (data, count) = await _objectQuery.GetList<AccountingVoucher>(filter, sortList, 0, 10);
            if (count > 0)
            {
                if (data.Count > 0) int.TryParse(data[0].VoucherNo, out maxVoucherNo);
            }
            return maxVoucherNo;
        }

        /// <summary>
        /// 获取发票附件信息
        /// </summary>
        /// <param name="billNo"></param>
        /// <returns></returns>
        private async Task<(List<MC_BILL_INVOICE_DATA>, int)> GetBillInvoiceData(string billNo)
        {
            var filter = new List<Filter>();
            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "bill_no",
                Method = "in",
                Mode = "in",
                Type = "Text",
                Values = new string[] { billNo }
            });

            return await _objectQuery.GetList<MC_BILL_INVOICE_DATA>(filter, new List<Sorter>(), 0, 10);
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="url"></param>
        /// <param name="savePath"></param>
        /// <param name="fileName"></param>
        /// <returns>(fileName,fullPath)</returns>
        private (string, string) DownloadFile(string url, string savePath, string fileName)
        {
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }
            var path = savePath + "/" + fileName;

            HttpClient httpClient = new HttpClient();
            var t = httpClient.GetByteArrayAsync(url);
            t.Wait();
            Stream responseStream = new MemoryStream(t.Result);
            Stream stream = new FileStream(path, FileMode.Create);
            byte[] bArr = new byte[1024];
            int size = responseStream.Read(bArr, 0, bArr.Length);
            while (size > 0)
            {
                stream.Write(bArr, 0, size);
                size = responseStream.Read(bArr, 0, bArr.Length);
            }
            stream.Close();
            responseStream.Close();

            var fileInfo = new FileInfo(path);
            return (fileInfo.Name, fileInfo.FullName);
        }

        /// <summary>
        /// 请求
        /// </summary>
        /// <param name="data"></param>
        /// <param name="apiUrl"></param>
        /// <param name="isLowerCase"></param>
        /// <returns></returns>
        private async Task<string> SendInfo(object data, string apiUrl, bool isLowerCase = false)
        {
            HttpClient httpClient = new HttpClient();
            string postJson = "";
            if (isLowerCase)
            {
                var serializerSettings = new JsonSerializerSettings
                {
                    ContractResolver = new UnderlineSplitContractResolver()
                };
                postJson = JsonConvert.SerializeObject(data, Formatting.Indented, serializerSettings);
            }
            else
            {
                postJson = JsonConvert.SerializeObject(data);
            }

            HttpContent content = new StringContent(postJson, Encoding.UTF8, "application/json");
            var result = httpClient.PostAsync(apiUrl, content).Result;
            return await result.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// 请求上传文件接口
        /// </summary>
        /// <param name="data"></param>
        /// <param name="apiUrl"></param>
        /// <returns></returns>
        private async Task<string> SendFileInfo(UploadDto data, string apiUrl)
        {
            var requestModel = await _comHttpClient.GetReimbursementConfig();
            var url = (requestModel == null ? "" : requestModel.ArchivesBaseUrl) + apiUrl;
            var client = new RestClient(url);
            client.Timeout = -1;

            var request = new RestRequest(Method.POST);
            request.AddHeader("Content-Type", "multipart/form-data");
            request.AddFile("FileStream", data.FileStream);
            request.AddParameter("AppKey", data.AppKey);
            request.AddParameter("Token", data.Token);
            request.AddParameter("Timestamp", data.Timestamp);
            request.AddParameter("ClientNo", data.ClientNo);
            request.AddParameter("DocTypeID", data.DocTypeID);
            request.AddParameter("FileLabel", data.FileLabel);
            request.AddParameter("FileType", data.FileType);
            request.AddParameter("FileName", data.FileName);
            request.AddParameter("DocumentNo", data.DocumentNo);
            IRestResponse response = await client.ExecuteAsync(request);
            return response.Content;
        }

        /// <summary>
        /// 请求上传文件接口
        /// </summary>
        /// <param name="data"></param>
        /// <param name="apiUrl"></param>
        /// <returns></returns>
        private async Task<string> UploadFileInfo(string url, Dictionary<string, string> files, Dictionary<string, string> parameters, bool isJson = true)
        {
            var client = new RestClient(url);
            client.Timeout = -1;

            var request = new RestRequest(Method.POST);
            request.AddHeader("Content-Type", "multipart/form-data");
            //request.AddHeader("Content-Type", "application/json");

            foreach (var k in files)
            {
                if (string.IsNullOrEmpty(k.Key) || string.IsNullOrEmpty(k.Value)) continue;
                if (isJson) request.AddFile(k.Key, k.Value, "application/json");
                else request.AddFile(k.Key, k.Value);
            }

            foreach (var k in parameters)
            {
                if (string.IsNullOrEmpty(k.Key) || string.IsNullOrEmpty(k.Value)) continue;
                request.AddParameter(k.Key, k.Value);
            }

            IRestResponse response = await client.ExecuteAsync(request);
            return response.Content;
        }

        /// <summary>
        /// delete file
        /// </summary>
        /// <param name="path"></param>
        private void DeleteFile(string path)
        {
            if (File.Exists(path)) File.Delete(path);
        }

        /// <summary>
        /// md5
        /// </summary>
        /// <param name="appKey"></param>
        /// <param name="appSecret"></param>
        /// <param name="timestamp"></param>
        /// <returns></returns>
        private string Md5Token(string appKey, string appSecret, string timestamp)
        {
            byte[] bt = Encoding.UTF8.GetBytes($"{appKey}+{timestamp}+{appSecret}");
            //创建默认实现的实例
            var md5 = MD5.Create();
            //计算指定字节数组的哈希值。
            var md5bt = md5.ComputeHash(bt);
            //将byte数组转换为字符串
            StringBuilder builder = new StringBuilder();
            foreach (var item in md5bt)
            {
                builder.Append(item.ToString("X2"));
            }

            var encrptionToken = builder.ToString();
            return encrptionToken;
        }

        /// <summary>
        /// 时间戳
        /// </summary>
        /// <returns></returns>
        private long GetTimeStamp()
        {
            var timeStamp = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
            return timeStamp;
        }

        private string CopyTemplateFile(string destFile, string templateFile = "Temp/vhr.json")
        {
            var fullPath = "";
            if (File.Exists(templateFile))
            {
                File.Copy(templateFile, destFile);
                if (File.Exists(destFile))
                {
                    var fileInfo = new FileInfo(destFile);
                    if (fileInfo != null) fullPath = fileInfo.FullName;
                }
            }

            return fullPath;
        }

        private string GetFileFullPath(string sourceFile)
        {
            var fullPath = "";
            if (File.Exists(sourceFile))
            {
                var fileInfo = new FileInfo(sourceFile);
                if (fileInfo != null) fullPath = fileInfo.FullName;
            }

            return fullPath;
        }

        /// <summary>
        /// 修改报销单整体--已收集
        /// </summary>
        /// <param name="archivesKey"></param>
        /// <returns></returns>
        public async Task<bool> UpdateReimbursementState(string archivesKey)
        {
            var result = false;
            var filter = new List<Filter>();
            filter.Add(new Filter()
            {
                DisplayType = "文本",
                Field = "ArchivesKey",
                Method = "in",
                Mode = "in",
                Type = "Text",
                Values = new string[] { archivesKey }
            });
            var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
            var selectReimList = await this._objectQuery.GetList<Reimbursement>(filter, sorters, 0, 50);

            if (selectReimList.totalCount > 0 && selectReimList.data != null)
            {
                for (int i = 0; i < selectReimList.totalCount; i++)
                {
                    selectReimList.data[i].state = "已收集";
                }
                result = await this._objectQuery.BulkCreateOrUpdate<Reimbursement>(selectReimList.data);
            }

            return result;
        }
    }
}