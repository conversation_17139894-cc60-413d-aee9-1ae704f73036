using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// API配置服务实现
    /// </summary>
    public class ApiConfigurationService : IApiConfigurationService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly ILogger<ApiConfigurationService> _logger;
        private const string CollectionName = "api_configurations";

        public ApiConfigurationService(
            IMongoRepository mongoRepository,
            ILogger<ApiConfigurationService> logger)
        {
            _mongoRepository = mongoRepository;
            _logger = logger;
        }

        public async Task<List<ApiConfiguration>> GetAllAsync()
        {
            try
            {
                var filter = Builders<ApiConfiguration>.Filter.Empty;
                return await _mongoRepository.FindListAsync<ApiConfiguration>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all API configurations");
                throw;
            }
        }

        public async Task<ApiConfiguration> GetByIdAsync(string id)
        {
            try
            {
                var filter = Builders<ApiConfiguration>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<ApiConfiguration>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API configuration by id: {Id}", id);
                throw;
            }
        }

        public async Task<ApiConfiguration> CreateAsync(ApiConfiguration config)
        {
            try
            {
                config.Id = null; // Let MongoDB generate the ID
                config.CreatedAt = DateTime.UtcNow;
                config.UpdatedAt = DateTime.UtcNow;

                await _mongoRepository.InsertOneAsync(CollectionName, config);
                _logger.LogInformation("Created API configuration: {Name}", config.Name);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating API configuration: {Name}", config.Name);
                throw;
            }
        }

        public async Task<ApiConfiguration> UpdateAsync(ApiConfiguration config)
        {
            try
            {
                config.UpdatedAt = DateTime.UtcNow;
                var filter = Builders<ApiConfiguration>.Filter.Eq(x => x.Id, config.Id);

                await _mongoRepository.ReplaceOneAsync(CollectionName, filter, config);
                _logger.LogInformation("Updated API configuration: {Id}", config.Id);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating API configuration: {Id}", config.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                var filter = Builders<ApiConfiguration>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.DeleteOneAsync<ApiConfiguration>(CollectionName, filter);

                _logger.LogInformation("Deleted API configuration: {Id}", id);
                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting API configuration: {Id}", id);
                throw;
            }
        }

        public async Task<bool> SetEnabledAsync(string id, bool enabled)
        {
            try
            {
                var filter = Builders<ApiConfiguration>.Filter.Eq(x => x.Id, id);
                var update = Builders<ApiConfiguration>.Update
                    .Set(x => x.IsEnabled, enabled)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                var result = await _mongoRepository.UpdateOneAsync(CollectionName, filter, update);
                _logger.LogInformation("Set API configuration {Id} enabled to {Enabled}", id, enabled);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting API configuration enabled: {Id}", id);
                throw;
            }
        }

        public async Task<List<ApiConfiguration>> GetEnabledAsync()
        {
            try
            {
                var filter = Builders<ApiConfiguration>.Filter.Eq(x => x.IsEnabled, true);
                return await _mongoRepository.FindListAsync<ApiConfiguration>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting enabled API configurations");
                throw;
            }
        }
    }
}
