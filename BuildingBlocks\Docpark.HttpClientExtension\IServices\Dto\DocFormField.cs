﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class DocFormField
    {
        public string Id { get; set; }
        public string Parent { get; set; }
        public string Name { get; set; }
        public string Identity { get; set; }
        public string PIdentity { get; set; }
        public string PropType { get; set; }
        public string CustomType { get; set; }
        public string PCustomType { get; set; }
        public string ParentName { get; set; }
    }
}
