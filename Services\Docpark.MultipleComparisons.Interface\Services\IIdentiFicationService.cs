﻿using Docpark.MultipleComparisons.Interface.Models.Entity;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IIdentiFicationService
    {
        Model_NuonuoInvoiceInspectionSettings GetInvoiceVerifySettingAsync(string _appKey, string _appSecret);
        string sendRequest(string _appKey, string _appSecret, string taxnum, Model_NuonuoInvoiceInspectionContent inspContent, out string errmsg);
        Task<(InvoiceOcrResultDtos model, string message)> VerifiyData(Request_FileData request_FileData);
        string VerifyResult(string taxnum, string invoiceCode, string invoiceNo, string invoiceDate, string optionfield);
    }
}