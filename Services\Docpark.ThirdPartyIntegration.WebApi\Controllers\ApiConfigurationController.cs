using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// API配置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ApiConfigurationController : ControllerBase
    {
        private readonly IApiConfigurationService _apiConfigService;
        private readonly ILogger<ApiConfigurationController> _logger;

        public ApiConfigurationController(
            IApiConfigurationService apiConfigService,
            ILogger<ApiConfigurationController> logger)
        {
            _apiConfigService = apiConfigService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有API配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<List<ApiConfiguration>>> GetAll()
        {
            try
            {
                var configs = await _apiConfigService.GetAllAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all API configurations");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据ID获取API配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiConfiguration>> GetById(string id)
        {
            try
            {
                var config = await _apiConfigService.GetByIdAsync(id);
                if (config == null)
                {
                    return NotFound();
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API configuration by id: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建API配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<ApiConfiguration>> Create([FromBody] ApiConfiguration config)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdConfig = await _apiConfigService.CreateAsync(config);
                return CreatedAtAction(nameof(GetById), new { id = createdConfig.Id }, createdConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating API configuration");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 更新API配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiConfiguration>> Update(string id, [FromBody] ApiConfiguration config)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != config.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var existingConfig = await _apiConfigService.GetByIdAsync(id);
                if (existingConfig == null)
                {
                    return NotFound();
                }

                var updatedConfig = await _apiConfigService.UpdateAsync(config);
                return Ok(updatedConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating API configuration: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除API配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(string id)
        {
            try
            {
                var result = await _apiConfigService.DeleteAsync(id);
                if (!result)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting API configuration: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 启用/禁用API配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="enabled"></param>
        /// <returns></returns>
        [HttpPatch("{id}/enabled")]
        public async Task<ActionResult> SetEnabled(string id, [FromBody] bool enabled)
        {
            try
            {
                var result = await _apiConfigService.SetEnabledAsync(id, enabled);
                if (!result)
                {
                    return NotFound();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting API configuration enabled: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取启用的API配置
        /// </summary>
        /// <returns></returns>
        [HttpGet("enabled")]
        public async Task<ActionResult<List<ApiConfiguration>>> GetEnabled()
        {
            try
            {
                var configs = await _apiConfigService.GetEnabledAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting enabled API configurations");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
