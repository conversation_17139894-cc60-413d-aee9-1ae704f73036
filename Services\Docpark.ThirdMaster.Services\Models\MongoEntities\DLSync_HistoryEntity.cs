﻿using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Docpark.ThirdMaster.Services.Models.MongoEntities
{
    public class DLSync_HistoryEntity : BaseEntity
    {
        public string TaskId { get; set; }
        
        public string CreateTime { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime ModifiedTime { get; set; }
        public string Title { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BussinessType { get; set; }
        /// <summary>
        /// 同步状态
        /// </summary>
        public bool Status { get; set; } = false;
        /// <summary>
        /// 重新同步
        /// </summary>
        public bool ReSync { get; set; } = false;
        /// <summary>
        /// 同步时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? SyncDate { get; set; }
        /// <summary>
        /// 失败消息
        /// </summary>
        public string Error { get; set; }
    }
}
