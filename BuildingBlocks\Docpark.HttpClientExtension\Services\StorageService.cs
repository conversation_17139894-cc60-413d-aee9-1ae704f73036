﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.HttpClientExtions;
using Docpark.HttpClientExtension.IServices;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class StorageService : IStorageService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IDocparkHttpClient docparkHttpClient;
        private readonly IDGrpc grpc;
        private readonly IConfiguration configuration;
        private readonly IHttpContextAccessor httpContextAccessor;

        private string BaseAddress
        {
            get;set;
        }

        public StorageService(IHttpClientFactory httpClientFactory, IDocparkHttpClient docparkHttpClient, IDGrpc grpc,IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor)
        {
            this.httpClientFactory = httpClientFactory;
            this.docparkHttpClient = docparkHttpClient;
            this.grpc = grpc;
            this.configuration = configuration;
            this.httpContextAccessor = httpContextAccessor;
            BaseAddress = grpc.GetStringAsync(ConstSettings.DocumentStorage).Result;
            OFDBaseAddress= grpc.GetStringAsync("App.Api.OfdSvc.Host").Result;
        }

        private string OFDBaseAddress
        {
            get;set;

        }
        public async Task<byte[]> GetDocument(string documentId)
        {
            var bytes = await DowLoadFile(documentId);
            return bytes.Item1;

        }

        public async Task<byte[]> DownloadOfdFirst(string docId)
        {
            var api = "/api/OfdService/get_ofd_invoice_img";
            var dic = new Dictionary<string, object>()
            {
                ["documentId"] = docId
            };

            var hContent = new StringContent(JsonConvert.SerializeObject(dic));
            hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            using (var httpClient = httpClientFactory.CreateClient())
            {
                httpClient.AddAuthenticate(grpc, configuration, httpContextAccessor);

                var result = await httpClient.PostAsync(api.ToUrl(OFDBaseAddress), hContent);

                var json = JsonConvert.DeserializeObject<Dictionary<string, object>>(result.Content.ReadAsStringAsync().Result);

                if (json != null && json.ContainsKey("state") && (bool)json["state"])
                {
                    var base64s = JsonConvert.DeserializeObject<string[]>(json["data"].ToString());
                    var first = base64s.FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(first))
                    {
                        return Convert.FromBase64String(first[(first.IndexOf(",") + 1)..]);
                    }
                }
                return null;
            }
        }

        public async Task<byte[]> DownloadZip(string docId, int pageIndex)
        {
            var api = $"api/ftp/viwer/zip/{docId}/"+ pageIndex;
            using (var httpClient = httpClientFactory.CreateClient())
            {
                httpClient.AddAuthenticate(grpc, configuration, httpContextAccessor);

                var result = await httpClient.GetStringAsync(api.ToUrl(BaseAddress));

                var json = JsonConvert.DeserializeObject<Dictionary<string, object>>(result);

                if (json != null && json.ContainsKey("state") && (bool)json["state"])
                {
                    var base64 = json["data"].ToString();
                    if (!string.IsNullOrWhiteSpace(base64))
                    {
                        return Convert.FromBase64String(base64[(base64.IndexOf(",") + 1)..]);
                    }
                }
                return null;
            }
        }


        public async Task<DocumentInfo> GetDocumentInfo(string docId)
        {
            var api = $"api/ftp/viwer/getdocumenturl/{docId}/1";

            using (var httpClient = httpClientFactory.CreateClient())
            {
                httpClient.AddAuthenticate(grpc, configuration, httpContextAccessor);
                string strResult = await httpClient.GetStringAsync(api.ToUrl(BaseAddress));

                var dicResult = JsonConvert.DeserializeObject<Dictionary<string, object>>(strResult);

                if (dicResult != null && dicResult.ContainsKey("state") && (bool)dicResult["state"])
                {
                    var result = JsonConvert.DeserializeObject<DocumentInfo>(dicResult["data"].ToString());
                    var host = await grpc.GetStringAsync("App.Api.Default.Host");
                    if (result.Url.IndexOf("http") < 0)
                    {
                        result.Url = $"{host.TrimEnd('/')}/{result.Url.TrimStart('/')}";
                    }
                    return result;
                }
                return null;
            }

        }

        private async Task<(byte[], MediaTypeHeaderValue)> DowLoadFile(string docId)
        { 
            string url = $"{BaseAddress}api/ftp/download/file?documentId={docId}";
            using (var client = httpClientFactory.CreateClient())
            {
                client.AddAuthenticate(grpc, configuration, httpContextAccessor);
                using (var response = await client.GetAsync(url))
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return (await response.Content.ReadAsByteArrayAsync(), response.Content.Headers.ContentType);
                }
            }
        }

    }

    public class DocumentInfo
    {
        /// <summary>
        /// 非图片类型的预览IFrame url
        /// </summary>
        public string Url { get; set; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 总共多少页
        /// </summary>
        public int? PageCount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string LinkType { get; set; }

        public List<string> Base64s { get; set; }
    }
}
