﻿using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Docpark.ThirdMaster.Services.Models.MongoEntities
{
    public class CaseNo_SerialNumber_Settings : BaseEntity
    {
        /// <summary>
        /// 编码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 流水号位数
        /// </summary>
        public int Place { get; set; }
        /// <summary>
        /// 重置类型
        /// </summary>
        public Enum_Rest Reset { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? CreateTime { get; set; }
        /// <summary>
        /// 最后执行时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? LastTime { get; set; }
        /// <summary>
        /// 当前计数
        /// </summary>
        public int Value { get; set; }
        /// <summary>
        /// 日期格式化
        /// </summary>
        public string DateFormat { get; set; }
        /// <summary>
        /// 流水号格式化
        /// </summary>
        public string ValueFormat { get; set; }
    }
    public enum Enum_Rest
    {
        /// <summary>
        /// 不自动重置
        /// </summary>
        None,
        /// <summary>
        /// 每日重置
        /// </summary>
        Day,
        /// <summary>
        /// 每周重置
        /// </summary>
        Week,
        /// <summary>
        /// 每月重置
        /// </summary>
        Month,
        /// <summary>
        /// 每年重置
        /// </summary>
        Year
    }
}
