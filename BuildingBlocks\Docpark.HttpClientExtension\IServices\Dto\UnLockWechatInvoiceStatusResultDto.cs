﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    /// <summary>
    /// 响应结果
    /// </summary>
    public class UnLockWechatInvoiceStatusResultDto
    {
        public UnLockWechatInvoiceStatusResultDto()
        {
            IsSuccess = false;
            Data = null;
            Message = string.Empty;
        }
        /// <summary>
        /// 是在执行成功, true 成功, false 失败
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 返回的执行结果
        /// </summary>
        public object Data { get; set; }
        /// <summary>
        /// 返回的消息
        /// </summary>
        public string Message { get; set; }
    }
}
