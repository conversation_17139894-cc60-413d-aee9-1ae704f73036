﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Model_AbpUserConfiguration
    {
        public Model_ConfigurationResult result { get; set; }
    }

    public class Model_ConfigurationResult
    {
        public Model_ConfigurationSetting setting { get; set; }

    }
    public class Model_ConfigurationSetting
    {
        public Dictionary<string, object> values { get; set; }

    }

    public class ApiHostModel
    { 
        public ReimbursementConfigManagement result { get; set; }
    }

    public class ReimbursementConfigManagement 
    {
        public ReimbursementConfigManagementDto reimbursementConfigManagement { get; set; }
    }

    public class ReimbursementConfigManagementDto
    { 
        public string ArchivesBaseUrl { get; set; }
        public string PdfApiBaseUrl { get; set; }
        public string ThirdticketholderUrl { get; set; }
        public string ThirdticketpriviewUrl { get; set; }
    }
}
