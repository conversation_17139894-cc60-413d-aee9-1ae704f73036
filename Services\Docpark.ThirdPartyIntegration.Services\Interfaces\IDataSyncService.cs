using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 数据同步服务接口
    /// </summary>
    public interface IDataSyncService
    {
        /// <summary>
        /// 创建同步配置
        /// </summary>
        Task<string> CreateSyncConfigurationAsync(SyncConfiguration config);

        /// <summary>
        /// 更新同步配置
        /// </summary>
        Task<bool> UpdateSyncConfigurationAsync(string id, SyncConfiguration config);

        /// <summary>
        /// 获取同步配置
        /// </summary>
        Task<SyncConfiguration> GetSyncConfigurationAsync(string id);

        /// <summary>
        /// 根据API配置ID获取同步配置
        /// </summary>
        Task<SyncConfiguration> GetSyncConfigurationByApiConfigIdAsync(string apiConfigId);

        /// <summary>
        /// 删除同步配置
        /// </summary>
        Task<bool> DeleteSyncConfigurationAsync(string id);

        /// <summary>
        /// 开始同步会话
        /// </summary>
        Task<string> StartSyncSessionAsync(string apiConfigId, SyncStrategy strategy = SyncStrategy.IncrementalSync);

        /// <summary>
        /// 结束同步会话
        /// </summary>
        Task<bool> EndSyncSessionAsync(string sessionId, SyncStatus status, string errorMessage = null);

        /// <summary>
        /// 更新同步会话进度
        /// </summary>
        Task<bool> UpdateSyncSessionProgressAsync(string sessionId, int processedRecords, int successRecords, int failedRecords);

        /// <summary>
        /// 获取同步会话
        /// </summary>
        Task<SyncSession> GetSyncSessionAsync(string sessionId);

        /// <summary>
        /// 获取API的同步会话历史
        /// </summary>
        Task<List<SyncSession>> GetSyncSessionHistoryAsync(string apiConfigId, int limit = 50);

        /// <summary>
        /// 执行增量同步
        /// </summary>
        Task<SyncSession> ExecuteIncrementalSyncAsync(string apiConfigId, DateTime? lastSyncTime = null);

        /// <summary>
        /// 执行全量同步
        /// </summary>
        Task<SyncSession> ExecuteFullSyncAsync(string apiConfigId);

        /// <summary>
        /// 检测数据冲突
        /// </summary>
        Task<List<DataConflict>> DetectDataConflictsAsync(string sessionId, List<object> newData);

        /// <summary>
        /// 解决数据冲突
        /// </summary>
        Task<bool> ResolveDataConflictAsync(string conflictId, ConflictResolutionStrategy strategy, object resolvedData = null);

        /// <summary>
        /// 获取未解决的冲突
        /// </summary>
        Task<List<DataConflict>> GetUnresolvedConflictsAsync(string apiConfigId = null);

        /// <summary>
        /// 获取同步统计信息
        /// </summary>
        Task<object> GetSyncStatisticsAsync(string apiConfigId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// 清理过期的同步数据
        /// </summary>
        Task<int> CleanupExpiredSyncDataAsync(int olderThanDays = 30);

        /// <summary>
        /// 验证同步配置
        /// </summary>
        Task<bool> ValidateSyncConfigurationAsync(SyncConfiguration config);

        /// <summary>
        /// 获取下次同步时间
        /// </summary>
        Task<DateTime?> GetNextSyncTimeAsync(string apiConfigId);

        /// <summary>
        /// 重置同步状态
        /// </summary>
        Task<bool> ResetSyncStateAsync(string apiConfigId);
    }
}
