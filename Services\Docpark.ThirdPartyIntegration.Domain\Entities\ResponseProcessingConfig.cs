using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 响应数据处理配置
    /// </summary>
    public class ResponseProcessingConfig
    {
        /// <summary>
        /// 是否启用响应处理
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 数据去重配置
        /// </summary>
        public DeduplicationConfig Deduplication { get; set; }

        /// <summary>
        /// 数据过滤配置
        /// </summary>
        public DataFilterConfig Filter { get; set; }

        /// <summary>
        /// 数据聚合配置
        /// </summary>
        public DataAggregationConfig Aggregation { get; set; }

        /// <summary>
        /// 数据验证配置
        /// </summary>
        public DataValidationConfig Validation { get; set; }

        /// <summary>
        /// 错误处理策略
        /// </summary>
        public ErrorHandlingStrategy ErrorHandling { get; set; } = ErrorHandlingStrategy.LogAndContinue;

        /// <summary>
        /// 批处理配置
        /// </summary>
        public BatchProcessingConfig BatchProcessing { get; set; }
    }

    /// <summary>
    /// 数据去重配置
    /// </summary>
    public class DeduplicationConfig
    {
        /// <summary>
        /// 是否启用去重
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 去重策略
        /// </summary>
        public DeduplicationStrategy Strategy { get; set; } = DeduplicationStrategy.HashBased;

        /// <summary>
        /// 用于去重的字段列表
        /// </summary>
        public List<string> KeyFields { get; set; } = new List<string>();

        /// <summary>
        /// 哈希算法（当使用HashBased策略时）
        /// </summary>
        public string HashAlgorithm { get; set; } = "SHA256";

        /// <summary>
        /// 去重范围（天数，0表示全部历史数据）
        /// </summary>
        public int ScopeDays { get; set; } = 30;
    }

    /// <summary>
    /// 数据过滤配置
    /// </summary>
    public class DataFilterConfig
    {
        /// <summary>
        /// 是否启用过滤
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 过滤条件列表
        /// </summary>
        public List<FilterCondition> Conditions { get; set; } = new List<FilterCondition>();

        /// <summary>
        /// 条件之间的逻辑关系
        /// </summary>
        public LogicalOperator LogicalOperator { get; set; } = LogicalOperator.And;
    }

    /// <summary>
    /// 过滤条件
    /// </summary>
    public class FilterCondition
    {
        /// <summary>
        /// 字段路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FieldPath { get; set; }

        /// <summary>
        /// 操作符
        /// </summary>
        public FilterOperator Operator { get; set; }

        /// <summary>
        /// 比较值
        /// </summary>
        [StringLength(500)]
        public string Value { get; set; }

        /// <summary>
        /// 是否区分大小写（字符串比较时）
        /// </summary>
        public bool CaseSensitive { get; set; } = false;
    }

    /// <summary>
    /// 数据聚合配置
    /// </summary>
    public class DataAggregationConfig
    {
        /// <summary>
        /// 是否启用聚合
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 分组字段
        /// </summary>
        public List<string> GroupByFields { get; set; } = new List<string>();

        /// <summary>
        /// 聚合操作列表
        /// </summary>
        public List<AggregationOperation> Operations { get; set; } = new List<AggregationOperation>();
    }

    /// <summary>
    /// 聚合操作
    /// </summary>
    public class AggregationOperation
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public AggregationType Type { get; set; }

        /// <summary>
        /// 源字段
        /// </summary>
        [Required]
        [StringLength(100)]
        public string SourceField { get; set; }

        /// <summary>
        /// 目标字段名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string TargetField { get; set; }
    }

    /// <summary>
    /// 数据验证配置
    /// </summary>
    public class DataValidationConfig
    {
        /// <summary>
        /// 是否启用验证
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 验证规则列表
        /// </summary>
        public List<ValidationRule> Rules { get; set; } = new List<ValidationRule>();

        /// <summary>
        /// 验证失败时的处理策略
        /// </summary>
        public ValidationFailureStrategy FailureStrategy { get; set; } = ValidationFailureStrategy.LogAndContinue;
    }

    /// <summary>
    /// 验证规则
    /// </summary>
    public class ValidationRule
    {
        /// <summary>
        /// 字段路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FieldPath { get; set; }

        /// <summary>
        /// 验证类型
        /// </summary>
        public ValidationType Type { get; set; }

        /// <summary>
        /// 验证参数（如正则表达式、范围值等）
        /// </summary>
        [StringLength(500)]
        public string Parameter { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        [StringLength(200)]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否必需字段
        /// </summary>
        public bool IsRequired { get; set; } = false;
    }

    /// <summary>
    /// 批处理配置
    /// </summary>
    public class BatchProcessingConfig
    {
        /// <summary>
        /// 是否启用批处理
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// 批处理间隔（毫秒）
        /// </summary>
        public int IntervalMs { get; set; } = 1000;

        /// <summary>
        /// 最大等待时间（秒）
        /// </summary>
        public int MaxWaitSeconds { get; set; } = 30;
    }

    #region 枚举定义

    /// <summary>
    /// 去重策略
    /// </summary>
    public enum DeduplicationStrategy
    {
        /// <summary>
        /// 基于哈希值
        /// </summary>
        HashBased = 0,

        /// <summary>
        /// 基于字段值
        /// </summary>
        FieldBased = 1,

        /// <summary>
        /// 基于内容比较
        /// </summary>
        ContentBased = 2
    }

    /// <summary>
    /// 逻辑操作符
    /// </summary>
    public enum LogicalOperator
    {
        /// <summary>
        /// 与
        /// </summary>
        And = 0,

        /// <summary>
        /// 或
        /// </summary>
        Or = 1
    }

    /// <summary>
    /// 过滤操作符
    /// </summary>
    public enum FilterOperator
    {
        /// <summary>
        /// 等于
        /// </summary>
        Equals = 0,

        /// <summary>
        /// 不等于
        /// </summary>
        NotEquals = 1,

        /// <summary>
        /// 大于
        /// </summary>
        GreaterThan = 2,

        /// <summary>
        /// 大于等于
        /// </summary>
        GreaterThanOrEqual = 3,

        /// <summary>
        /// 小于
        /// </summary>
        LessThan = 4,

        /// <summary>
        /// 小于等于
        /// </summary>
        LessThanOrEqual = 5,

        /// <summary>
        /// 包含
        /// </summary>
        Contains = 6,

        /// <summary>
        /// 不包含
        /// </summary>
        NotContains = 7,

        /// <summary>
        /// 开始于
        /// </summary>
        StartsWith = 8,

        /// <summary>
        /// 结束于
        /// </summary>
        EndsWith = 9,

        /// <summary>
        /// 正则匹配
        /// </summary>
        Regex = 10,

        /// <summary>
        /// 在列表中
        /// </summary>
        In = 11,

        /// <summary>
        /// 不在列表中
        /// </summary>
        NotIn = 12
    }

    /// <summary>
    /// 聚合类型
    /// </summary>
    public enum AggregationType
    {
        /// <summary>
        /// 计数
        /// </summary>
        Count = 0,

        /// <summary>
        /// 求和
        /// </summary>
        Sum = 1,

        /// <summary>
        /// 平均值
        /// </summary>
        Average = 2,

        /// <summary>
        /// 最大值
        /// </summary>
        Max = 3,

        /// <summary>
        /// 最小值
        /// </summary>
        Min = 4,

        /// <summary>
        /// 第一个值
        /// </summary>
        First = 5,

        /// <summary>
        /// 最后一个值
        /// </summary>
        Last = 6
    }

    /// <summary>
    /// 验证类型
    /// </summary>
    public enum ValidationType
    {
        /// <summary>
        /// 正则表达式
        /// </summary>
        Regex = 0,

        /// <summary>
        /// 数值范围
        /// </summary>
        Range = 1,

        /// <summary>
        /// 字符串长度
        /// </summary>
        Length = 2,

        /// <summary>
        /// 邮箱格式
        /// </summary>
        Email = 3,

        /// <summary>
        /// URL格式
        /// </summary>
        Url = 4,

        /// <summary>
        /// 日期格式
        /// </summary>
        Date = 5,

        /// <summary>
        /// 自定义验证
        /// </summary>
        Custom = 99
    }

    /// <summary>
    /// 验证失败策略
    /// </summary>
    public enum ValidationFailureStrategy
    {
        /// <summary>
        /// 记录日志并继续
        /// </summary>
        LogAndContinue = 0,

        /// <summary>
        /// 跳过当前记录
        /// </summary>
        SkipRecord = 1,

        /// <summary>
        /// 停止处理
        /// </summary>
        StopProcessing = 2
    }

    /// <summary>
    /// 错误处理策略
    /// </summary>
    public enum ErrorHandlingStrategy
    {
        /// <summary>
        /// 记录日志并继续
        /// </summary>
        LogAndContinue = 0,

        /// <summary>
        /// 重试
        /// </summary>
        Retry = 1,

        /// <summary>
        /// 停止处理
        /// </summary>
        StopProcessing = 2,

        /// <summary>
        /// 发送告警
        /// </summary>
        SendAlert = 3
    }

    #endregion
}
