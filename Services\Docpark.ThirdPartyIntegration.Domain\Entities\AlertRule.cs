using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 告警规则实体
    /// </summary>
    public class AlertRule
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// API配置ID（可选，为空表示全局规则）
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 告警类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AlertType { get; set; }

        /// <summary>
        /// 告警严重程度
        /// </summary>
        [Required]
        public AlertSeverity Severity { get; set; }

        /// <summary>
        /// 条件表达式（JSON格式）
        /// </summary>
        [Required]
        public string Conditions { get; set; }

        /// <summary>
        /// 阈值配置（JSON格式）
        /// </summary>
        public string Thresholds { get; set; }

        /// <summary>
        /// 评估间隔（分钟）
        /// </summary>
        public int EvaluationIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// 触发次数阈值
        /// </summary>
        public int TriggerThreshold { get; set; } = 1;

        /// <summary>
        /// 恢复次数阈值
        /// </summary>
        public int RecoveryThreshold { get; set; } = 1;

        /// <summary>
        /// 静默期（分钟）
        /// </summary>
        public int SilencePeriodMinutes { get; set; } = 60;

        /// <summary>
        /// 通知渠道
        /// </summary>
        public NotificationChannel[] NotificationChannels { get; set; }

        /// <summary>
        /// 通知配置（JSON格式）
        /// </summary>
        public string NotificationConfig { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string[] Tags { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }
    }
}
