﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class ResponseResult
    {
        public ResponseResult() {
            isSuccess = false;
            data = null;
            msg = "";
            count = 0;
        }

        //执行结果   成功为true   反之失败
        public bool isSuccess { get; set; }

        //返回结果
        public object data { get; set; }

        //返回消息
        public string msg { get; set; }

        //返回数据行数
        public int count { get; set; }
    }
}