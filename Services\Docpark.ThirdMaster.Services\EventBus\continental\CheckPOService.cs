﻿using DnsClient.Internal;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("CheckPO", "PO服务")]
    public class CheckPOService : IEventService
    {

        private readonly ILogger<CheckPOService> logger;
        private readonly IObjectQuery objectQuery;
        private readonly IEventBus eventBus;
        private readonly IConfiguration configuration;

        public CheckPOService(ILogger<CheckPOService> logger, IObjectQuery objectQuery, IEventBus eventBus, IConfiguration configuration)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.eventBus = eventBus;
            this.configuration = configuration;
        }

        

        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            StringBuilder resMessage = new StringBuilder();

            var invoice = await objectQuery.Get<InvoiceInfo>(new Guid(mqContent.MstId));


            var result = await objectQuery
                .GetList<CompanyInfo>(configuration["DocumentType:Company"], new List<Filter>() {
                    new Filter(){
                     Field="TaxNo",
                      Method="eq",
                       Mode="eq",
                        Values=new string[]{ invoice.buyer_tax_id }
                    }
                }, new List<Sorter>(), 0, 50);
            string poNo = "";
            string DN = "";

            CompanyInfo company = new CompanyInfo();
            if (result.totalCount > 0)
            {
                company = result.data.First();

                resMessage.AppendLine($"公司信息:{result.totalCount},{JsonConvert.SerializeObject(company)}");

                string SAPSys = company.SAP_BUKRS;
                string pFlag = "";//PO的判断规则
                string cFlagy = "";//寄售的判断规则
                string defaultJiShouPo = "";//寄售默认的PO号;
                if (company.PoList == null || company.PoList.Count <= 0)
                {
                    mqContent.TriggerStatus = false;
                    mqContent.TriggerMessage = "未配置PO规则";
                }
                else
                {
                    foreach (var item in company.PoList)
                    {
                        string po = item.PO;
                        int poLength = int.Parse(item.POLength) - po.Length;
                        int type = int.Parse(item.Type);
                        if (type == 0)
                        {
                            pFlag += "|((" + po + ")[0-9]{" + poLength + "})";
                        }
                        else
                        {
                            cFlagy += "|((" + po + ")[0-9]{" + poLength + "})";
                            if (string.IsNullOrEmpty(defaultJiShouPo))
                            {
                                defaultJiShouPo = "49".PadRight(poLength + 2, '0');
                            }
                        }
                    }
                    pFlag = @"(" + pFlag.TrimStart('|') + ")";
                    cFlagy = @"(" + cFlagy.TrimStart('|') + ")";

                    List<string> poList = new List<string>();
                    List<string> JiShouNo = new List<string>();
                    string fieldValue = invoice.remark;
                    if (string.IsNullOrEmpty(fieldValue))
                    {
                        fieldValue = "";
                    }
                    //获取备注中所有的数字
                    string strNumberRegex = @"([0-9]+)";
                    MatchCollection mstrs = Regex.Matches(fieldValue, strNumberRegex);
                    List<string> NumberList = new List<string>();
                    foreach (Match mat in mstrs)
                    {
                        if (!NumberList.Contains(mat.Value) && !string.IsNullOrEmpty(mat.Value))
                            NumberList.Add(mat.Value);
                    }


                    MatchCollection mats = Regex.Matches(fieldValue, pFlag);
                    foreach (Match mat in mats)
                    {
                        ///先查找对应的数字
                        string number = NumberList.Where(w => w.StartsWith(mat.Value)).FirstOrDefault();
                        //如果查找的数字匹配PO，那么说明这个是PO,否则则不是PO，
                        //比如12345，PO为1234，虽然PO能查找出来，但此数字并不属于PO。
                        if (number != null)
                        {
                            if (!poList.Contains(mat.Value))
                                poList.Add(mat.Value);
                            NumberList.Remove(number);
                            if (number != mat.Value)
                            {
                                NumberList.Add(number.Replace(mat.Value, ""));
                            }
                        }
                    }

                    ///寄售的查找
                    MatchCollection _mats = Regex.Matches(fieldValue, cFlagy);
                    foreach (Match mat in _mats)
                    {
                        ///先查找对应的数字
                        string number = NumberList.Where(w => w.StartsWith(mat.Value)).FirstOrDefault();
                        //如果查找的数字匹配PO，那么说明这个是PO,否则则不是PO，
                        //比如12345，PO为1234，虽然PO能查找出来，但此数字并不属于PO。
                        if (number != null)
                        {
                            if (!JiShouNo.Contains(mat.Value))
                                JiShouNo.Add(mat.Value);
                            NumberList.Remove(number);
                            if (number != mat.Value)
                            {
                                NumberList.Add(number.Replace(mat.Value, ""));
                            }
                        }
                    }
                    if (SAPSys != "AP1") ///AP1的不需要判断是否包含寄售
                    {
                        fieldValue = fieldValue.Replace("非寄售", "");
                        //寄售编号为空的则查是否包含寄售两个字
                        if (JiShouNo.Count == 0 && (fieldValue.Contains("寄售") || fieldValue == "寄售"))//包含寄售两个字给默认的PO号码
                        {
                            JiShouNo.Add(defaultJiShouPo);
                            //poNoPassFlag = "Y";
                        }
                    }


                    if (poList.Count > 0)
                    {
                        poNo = string.Join(";", poList.ToArray());
                    }

                    if (string.IsNullOrEmpty(poNo) && JiShouNo.Count == 0)
                    {
                        mqContent.TriggerMessage = "Pls check PO";
                        mqContent.TriggerStatus = false;
                    }
                    else
                    {
                        DN = JiShouNo.Count == 0 ? string.Join(";", NumberList.Where(w => w != "").ToArray())
                         : string.Join(";", JiShouNo.Where(w => w != "").ToArray());
                        mqContent.TriggerStatus = true;
                    }

                    resMessage.AppendLine($"PO:{poNo},DN:{DN}");
                }
            }
            else
            {
                mqContent.TriggerStatus = false;
                mqContent.TriggerMessage = "未配置PO规则";
            }

            Dictionary<string, object> data = new Dictionary<string, object>();
            data["PO"] = poNo;
            data["DN"] = DN;
            data["CompanyCode"] = company.CompanyCode;
            await objectQuery.CreateOrUpdate(mqContent.DocumentType, new Guid(mqContent.MstId), data);

            ///消息发布
            var msg = JsonConvert.SerializeObject(mqContent);

            resMessage.AppendLine("[发布消息]").AppendLine(msg);

            DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
            eventBus.Publish(publishEventData);

            return resMessage.ToString();
        }
    }
}
