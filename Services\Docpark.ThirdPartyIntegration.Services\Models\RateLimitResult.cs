using System;
using System.Collections.Generic;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Models
{
    /// <summary>
    /// 限流结果
    /// </summary>
    public class RateLimitResult
    {
        /// <summary>
        /// 是否允许请求
        /// </summary>
        public bool IsAllowed { get; set; }

        /// <summary>
        /// 限流规则ID
        /// </summary>
        public string RuleId { get; set; }

        /// <summary>
        /// 限流原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 剩余配额
        /// </summary>
        public int RemainingQuota { get; set; }

        /// <summary>
        /// 重置时间
        /// </summary>
        public DateTime? ResetTime { get; set; }

        /// <summary>
        /// 重试间隔（秒）
        /// </summary>
        public int RetryAfterSeconds { get; set; }

        /// <summary>
        /// 限流类型
        /// </summary>
        public RateLimitType LimitType { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
