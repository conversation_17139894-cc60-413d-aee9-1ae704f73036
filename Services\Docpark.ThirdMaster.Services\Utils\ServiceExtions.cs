﻿using DocPark.Workflow.Share;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using System;
using System.Linq;
using Docpark.ThirdMaster.Services.Backgroundjob;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Autofac.Core;

namespace Docpark.ThirdMaster.Services.Utils
{
    public static class ServiceExtions
    {
        /// <summary>
        /// 注册事件服务
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddEventService(this IServiceCollection services, IConfiguration configuration)
        {
            string companyCode = configuration["CompanyCode"];

            var serviceTypes = Assembly.GetExecutingAssembly().GetTypes()
            .Where(type => typeof(IEventService).IsAssignableFrom(type) && type.IsClass);

            foreach (var type in serviceTypes)
            {
                foreach (var attributes in type.CustomAttributes)
                {
                    if (attributes.AttributeType.Name == typeof(MasterServiceTypeAttribute).Name)
                    {
                        var _companyCode = (attributes).ConstructorArguments[0].Value.ToString();
                        if (companyCode == _companyCode)
                        {
                            services.AddSingleton(typeof(IEventService),type);
                        }
                    }
                }
            }
        }

        public static void AddThirdMasterBackgroundJob(this IServiceCollection services, IConfiguration configuration)
        {
            string companyCode = configuration["CompanyCode"];

            var serviceTypes = Assembly.GetExecutingAssembly().GetTypes()
            .Where(type => typeof(IThirdMasterBackgroundJob).IsAssignableFrom(type) && type.IsClass);

            foreach (var type in serviceTypes)
            {
                foreach (var attributes in type.CustomAttributes)
                {
                    if (attributes.AttributeType.Name == typeof(MasterServiceTypeAttribute).Name)
                    {
                        var _companyCode = (attributes).ConstructorArguments[0].Value.ToString();
                        if (companyCode == _companyCode)
                        {
                            var describe=ServiceDescriptor.Describe(typeof(IHostedService), type, ServiceLifetime.Singleton);
                            services.TryAddEnumerable(describe);
                            Console.WriteLine($"==========>加载后台任务:【{describe.ImplementationType.Name}】");
                        }
                    }
                }
            }
        }
    }
}
