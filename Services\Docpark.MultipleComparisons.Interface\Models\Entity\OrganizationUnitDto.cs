﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    public class OrganizationUnitDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int id { get; set; }
        /// <summary>
        /// 父节点ID
        /// </summary>
        public int? parentId { get; set; }
        /// <summary>
        /// 父节点简称
        /// </summary>
        public string parentShortName { get; set; }
        /// <summary>
        /// 编码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 显示名称
        /// </summary>
        public string displayName { get; set; }
        /// <summary>
        /// 类型, G 集团; A 区域; C 公司; D 部门
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string label { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string no { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        public string shortName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remarks { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int sort { get; set; }
        /// <summary>
        /// 是否激活
        /// </summary>
        public string isActive { get; set; }
        /// <summary>
        /// 人员数量
        /// </summary>
        public int memberCount { get; set; }
        /// <summary>
        /// 是否删除
        /// </summary>
        public string isDeleted { get; set; }
    }
}
