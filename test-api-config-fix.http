### 测试修复后的API配置创建

### 1. 创建API配置 - 使用新的baseUrl和endpoint字段
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "测试API配置",
  "description": "测试修复后的API配置创建",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/get",
  "method": "GET",
  "parameters": [],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 3
}

### 2. 获取所有API配置
GET http://localhost:5000/api/ApiConfiguration

### 3. 创建另一个API配置 - 测试不同的baseUrl和endpoint
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "JSON测试API",
  "description": "测试JSON端点",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/json",
  "method": "GET",
  "parameters": [],
  "schedule": {
    "type": 1,
    "intervalMinutes": 60,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 1
}
