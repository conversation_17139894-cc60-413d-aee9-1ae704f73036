{"fonts": [{"name": "simsun", "file": "simsun.ttf"}], "margin": {"left": 10, "top": 10, "right": 10}, "footer": {"separator": -25, "content": [{"cell": {"position": {"x": 0, "y": -10}, "text_color": "0,0,0", "align": "C", "font": {"family": "simsun", "size": 12}, "format": "{page_no}/{nb}"}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": -20}, "to": {"x": -10, "y": -20}}}]}, "header": {"separator": 25, "content": [{"cell": {"position": {"x": 5, "y": 10}, "text_color": "0,0,0", "align": "C", "font": {"family": "simsun", "size": 24}, "txt": "记账凭证"}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 55, "y": 20}, "to": {"x": -55, "y": 20}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 55, "y": 21}, "to": {"x": -55, "y": 21}}}, {"cell": {"position": {"x": -50, "y": 20}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "本币："}}, {"cell": {"position": {"x": -28, "y": 20}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "单位："}}, {"cell": {"position": {"x": -15, "y": 20}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt": "元"}}]}, "contents": [{"source": "main_src1", "content": [{"cell": {"position": {"x": 10, "y": 30}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "编制单位:"}}, {"cell": {"position": {"x": 30, "y": 30}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "prepared_by"}}, {"cell": {"position": {"x": 80, "y": 30}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "prepared_date"}}, {"cell": {"position": {"x": 10, "y": 40}, "text_color": "0,0,0", "align": "L", "font": {"family": "simsun", "size": 12}, "txt": "核算单位:"}}, {"cell": {"position": {"x": 30, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "accounted_by"}}, {"cell": {"position": {"x": -55, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "第"}}, {"cell": {"position": {"x": -48, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "number"}}, {"cell": {"position": {"x": -40, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "号"}}, {"cell": {"position": {"x": -30, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "number"}}, {"cell": {"position": {"x": -20, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt": "/"}}, {"cell": {"position": {"x": -15, "y": 40}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "total_number"}}]}, {"source": "main_src2", "content": [{"cell": {"position": {"x": 10, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "财务主管:"}}, {"cell": {"position": {"x": 20, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "treasurer"}}, {"cell": {"position": {"x": 50, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "记账:"}}, {"cell": {"position": {"x": 60, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "accounter"}}, {"cell": {"position": {"x": 80, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "复核:"}}, {"cell": {"position": {"x": 90, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "reviewer"}}, {"cell": {"position": {"x": 110, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "出纳:"}}, {"cell": {"position": {"x": 120, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "cashier"}}, {"cell": {"position": {"x": 140, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "制单:"}}, {"cell": {"position": {"x": 150, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "maker"}}, {"cell": {"position": {"x": 170, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "经办人:"}}, {"cell": {"position": {"x": 180, "y": 100}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 10}, "txt_bandding": "handler"}}]}, {"content": [{"cell": {"position": {"x": 10, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "摘要"}}, {"cell": {"position": {"x": 70, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "会计科目"}}, {"cell": {"position": {"x": 150, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "借方金额"}}, {"cell": {"position": {"x": 180, "y": 50}, "text_color": "0,0,0", "font": {"family": "simsun", "size": 12}, "txt": "贷方金额"}}]}, {"content": [{"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 45}, "to": {"x": 202, "y": 45}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 55}, "to": {"x": 202, "y": 55}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 65}, "to": {"x": 202, "y": 65}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 75}, "to": {"x": 202, "y": 75}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 85}, "to": {"x": 202, "y": 85}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 95}, "to": {"x": 202, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 10, "y": 45}, "to": {"x": 10, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 70, "y": 45}, "to": {"x": 70, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 143, "y": 45}, "to": {"x": 143, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 175, "y": 45}, "to": {"x": 175, "y": 95}}}, {"line": {"draw_color": "0,0,0", "w": 0.1, "from": {"x": 202, "y": 45}, "to": {"x": 202, "y": 95}}}]}], "repeaters": [{"source": "list_src", "repeater": [{"cell": {"position": {"x": 10}, "relative_position": {"y": 10}, "font": {"family": "simsun", "size": 10}, "txt_bandding": "abstract"}}, {"cell": {"position": {"x": 70}, "font": {"family": "simsun", "size": 10}, "txt_bandding": "subject"}}, {"cell": {"position": {"x": 150}, "align": "R", "w": 20, "font": {"family": "simsun", "size": 10}, "txt_bandding": "debit"}}, {"cell": {"position": {"x": 180}, "align": "R", "w": 20, "font": {"family": "simsun", "size": 10}, "txt_bandding": "credit"}}]}]}