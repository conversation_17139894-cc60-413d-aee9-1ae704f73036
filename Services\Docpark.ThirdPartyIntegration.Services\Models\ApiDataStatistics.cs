using System;

namespace Docpark.ThirdPartyIntegration.Services.Models
{
    /// <summary>
    /// API数据统计信息
    /// </summary>
    public class ApiDataStatistics
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public long TotalRecords { get; set; }

        /// <summary>
        /// 已处理记录数
        /// </summary>
        public long ProcessedRecords { get; set; }

        /// <summary>
        /// 未处理记录数
        /// </summary>
        public long UnprocessedRecords { get; set; }

        /// <summary>
        /// 总数据大小（字节）
        /// </summary>
        public long TotalDataSize { get; set; }

        /// <summary>
        /// 平均数据大小（字节）
        /// </summary>
        public double AverageDataSize { get; set; }

        /// <summary>
        /// 最早数据时间
        /// </summary>
        public DateTime? EarliestDataTime { get; set; }

        /// <summary>
        /// 最新数据时间
        /// </summary>
        public DateTime? LatestDataTime { get; set; }

        /// <summary>
        /// 成功响应数
        /// </summary>
        public long SuccessfulResponses { get; set; }

        /// <summary>
        /// 失败响应数
        /// </summary>
        public long FailedResponses { get; set; }

        /// <summary>
        /// 重复数据数量
        /// </summary>
        public long DuplicateDataCount { get; set; }

        /// <summary>
        /// 统计时间范围开始
        /// </summary>
        public DateTime? StatisticsPeriodStart { get; set; }

        /// <summary>
        /// 统计时间范围结束
        /// </summary>
        public DateTime? StatisticsPeriodEnd { get; set; }

        /// <summary>
        /// API配置ID（如果是单个API的统计）
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称（如果是单个API的统计）
        /// </summary>
        public string ApiName { get; set; }
    }
}
