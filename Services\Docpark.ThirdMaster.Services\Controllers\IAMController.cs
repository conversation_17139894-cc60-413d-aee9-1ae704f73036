﻿using Docpark.ThirdMaster.Services.Backgroundjob.YaoPi;
using Docpark.ThirdMaster.Services.Models;
using Microsoft.AspNetCore.Authorization;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Net.Http.Headers;

namespace Docpark.ThirdMaster.Services.Controllers
{
    [Authorize]
    public class IAMController : BaseController
    {
        private readonly ILogger<IAMController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;

        public IAMController(ILogger<IAMController> logger,IConfiguration configuration, IHttpClientFactory httpClientFactory)
        {
            this._logger = logger;
            this._configuration = configuration;
            this._httpClientFactory = httpClientFactory;
            // 构造函数可以用于初始化或注入服务
        }
        [HttpGet]
        public async Task<ResultDataDto> GetUserList(string startTime)
        {

            var client = CreateIamHttpClient();
            DateTime.TryParse(startTime, out var lastSyncTime);
            var lastSyncTimeStamp = ConvertToUnixTimestamp(lastSyncTime);
            long processedCount = 0;

            var apiUrl = $"{_configuration["IAMSyncJobSettings:ApiBaseUrl"].TrimEnd('/')}/esc-idm/api/v1/account/list";
            var data= await PaginateAndUpsert<AccountItem, AccountApiResponse>(client, apiUrl, lastSyncTimeStamp.ToString(), "orgCollectionName", (count) => processedCount += count);
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data= data
            };
        }

        [HttpGet]
        public async Task<ResultDataDto> GetOrgList(string startTime)
        {

            var client = CreateIamHttpClient();
            DateTime.TryParse(startTime, out var lastSyncTime);
            var lastSyncTimeStamp = ConvertToUnixTimestamp(lastSyncTime);
            long processedCount = 0;

            var apiUrl = $"{_configuration["IAMSyncJobSettings:ApiBaseUrl"].TrimEnd('/')}/esc-idm/api/v1/org/list";
            var data = await PaginateAndUpsert<OrgItem, OrgApiResponse>(client, apiUrl, lastSyncTimeStamp.ToString(), "orgCollectionName", (count) => processedCount += count);
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = data
            };
        }

        private HttpClient CreateIamHttpClient()
        {
            var appKey = _configuration["IAMSyncJobSettings:AppKey"];
            var appSecret = _configuration["IAMSyncJobSettings:AppSecret"];
            var client = _httpClientFactory.CreateClient();
            var secret = $"{appKey}:{appSecret}";
            var token = Convert.ToBase64String(Encoding.UTF8.GetBytes(secret));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", token);
            return client;
        }
        private double ConvertToUnixTimestamp(DateTime date)
        {
            DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan diff = date.ToUniversalTime() - origin;
            return Math.Floor(diff.TotalSeconds);
        }
        private async Task<string> PaginateAndUpsert<TItem, TResponse>(HttpClient client, string apiUrl, string lastSyncTime, string collectionName, Action<long> onUpserted) where TItem : IApiItem where TResponse : IApiResponse<TItem>
        {
            var pageSize = 100;
            long totalRecords = 0;
            int currentPage = 1;
            var requestPayload = new ApiRequest { Page = currentPage, Size = pageSize, StartTime = lastSyncTime };
            var jsonPayload = JsonConvert.SerializeObject(requestPayload);
            Console.WriteLine($"参数：{jsonPayload}");
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(apiUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("API request to {apiUrl} failed with status code: {statusCode}", apiUrl, response.StatusCode);
                return "";
            }
            var jsonResponse = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"URL:{apiUrl},Response:{jsonResponse}");
            var apiResponse = JsonConvert.DeserializeObject<TResponse>(jsonResponse);
            if (apiResponse.Code != "0")
            {
                _logger.LogError("API at {apiUrl} returned an error. Code: {code}, Message: {message}", apiUrl, apiResponse.Code, apiResponse.Msg);
                return "";
            }
            if (apiResponse.Data?.List == null || !apiResponse.Data.List.Any())
            {
                _logger.LogInformation("No new data found from {apiUrl}.", apiUrl);
                return "";
            }
            totalRecords = apiResponse.Data.Total;
            var itemsToUpsert = apiResponse.Data.List;
            var bulkOps = new List<WriteModel<TItem>>();
            foreach (var item in itemsToUpsert)
            {
                var filter = Builders<TItem>.Filter.Eq(i => i.Id, item.Id);
                var replacement = item;
                var upsertOne = new ReplaceOneModel<TItem>(filter, replacement) { IsUpsert = true };
                bulkOps.Add(upsertOne);
            }
            return jsonResponse;
        }
    }
}
