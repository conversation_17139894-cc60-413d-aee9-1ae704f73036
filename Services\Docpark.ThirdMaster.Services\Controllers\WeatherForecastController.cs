﻿using Docpark.ThirdMaster.Services.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Docpark.ThirdMaster.Services.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class WeatherForecastController : ControllerBase
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "<PERSON><PERSON>y", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<WeatherForecastController> _logger;

        public WeatherForecastController(ILogger<WeatherForecastController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IEnumerable<WeatherForecast> Get()
        {
            var rng = new Random();
            return null;
            string configXml = "ExportInvoice.xml";

            string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');
            for (int i = 0; i < flownames.Length; i++)
            {
                var fname = flownames[i];
                var OutFlord = EncryptionHelper.GetDataXMLByName(configXml, "" + fname + "/OutFlord");
                var savePath = OutFlord + "/test.txt";
                //System.IO.File.WriteAllText(savePath, fname);
            }

            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = rng.Next(-20, 55),
                Summary = Summaries[rng.Next(Summaries.Length)]
            })
            .ToArray();
        }
        [HttpGet,Route("writer")]
        public IActionResult TestWriter()
        {
            string configXml = "ExportInvoice.xml";

            string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');
            for (int i = 0; i < flownames.Length; i++)
            {
                var fname = flownames[i];
                var OutFlord = EncryptionHelper.GetDataXMLByName(configXml, "" + fname + "/OutFlord");
                var savePath = OutFlord + "/test.txt";
                System.IO.File.WriteAllText(savePath,fname);
            }
            return Ok();
        }
    }
}
