﻿using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class FormDataBase
    {
        public string documentTypeId { get; set; }
        public Guid mstId { get; set; }

        [JsonConverter(typeof(CustomerJsonConvert<int>))]
        public int status { get; set; } = 1;
        public Dictionary<string, object> formData { get; set; }

        public string CreatorUserId { get; set; }

        public DateTime CreationTime { get; set; }

        public string CreatorUserName { get; set; }
    }
}
