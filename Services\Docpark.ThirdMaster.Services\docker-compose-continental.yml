﻿version: '3.5'

services:
  continental:
    hostname: continental
    image: registry.cn-shanghai.aliyuncs.com/docparkv2/docpark.multiplecomparisons.interface:continental.bate
    labels:
      "type": "1"
    ports:
      - "8009:80"
    networks:
      - middleware
    env_file:
      ../envfile.env
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ConfigXml:/app/ConfigXml
      - /continental:/continenal
    environment:
      - IdentityUrl=${DOCPARK_HOST_WEB:-http://core}
      - Serilog:indexFormat=document_continental_logs
      - SubscriptionClientName=Document_Continental
      - EventBusConnection=rabbitmq
      - EventBusPort=5672
      - Grpc_Host=${DOCPARK_HOST_WEB:-http://core:5001}
      - DocumentType:Company=BillingCompany
      - TimeInterval=5
      - ArchiveTimeInterval=12 #小时
      - UpdateCol=1
    deploy:
      resources:
        limits: 
          memory: 1024M
      placement:
        constraints:
          - node.labels.nodetype == InDrive
      replicas: 1
      restart_policy:
        condition: on-failure
    logging: 
      driver: "json-file"
      options:
        max-size: "2048k"
        max-file: "10"
        
networks:
  middleware:
    external: true
    name: docpark 