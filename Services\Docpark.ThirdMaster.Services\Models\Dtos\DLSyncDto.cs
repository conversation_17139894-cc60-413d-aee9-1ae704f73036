﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Docpark.ThirdMaster.Services.Models.Dtos
{
    public class DLSyncSearchErrorListDto
    {
        public string TaskId { get; set; }

        public string Title { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public string BussinessType { get; set; }

        public string Error { get; set; }

        public bool Status { get; set; } = false;
        /// <summary>
        /// 分页数
        /// </summary>
        [Range(0, int.MaxValue)]
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 分页大小
        /// </summary>
        [Range(0, 1000)]
        public int PageSize { get; set; } = 10;
    }
}
