using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 限流服务实现
    /// </summary>
    public class RateLimitService : IRateLimitService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly ILogger<RateLimitService> _logger;

        private const string RateLimitRuleCollectionName = "rate_limit_rules";
        private const string RateLimitCounterCollectionName = "rate_limit_counters";

        public RateLimitService(
            IMongoRepository mongoRepository,
            ILogger<RateLimitService> logger)
        {
            _mongoRepository = mongoRepository;
            _logger = logger;
        }

        /// <summary>
        /// 创建限流规则
        /// </summary>
        public async Task<string> CreateRateLimitRuleAsync(RateLimitRule rule)
        {
            try
            {
                rule.Id = null; // 确保创建新记录
                rule.CreatedAt = DateTime.UtcNow;
                rule.UpdatedAt = DateTime.UtcNow;

                await _mongoRepository.InsertOneAsync(RateLimitRuleCollectionName, rule);
                _logger.LogInformation("创建限流规则成功，规则名称: {RuleName}", rule.Name);
                return rule.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建限流规则失败，规则名称: {RuleName}", rule.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新限流规则
        /// </summary>
        public async Task<bool> UpdateRateLimitRuleAsync(string id, RateLimitRule rule)
        {
            try
            {
                rule.Id = id;
                rule.UpdatedAt = DateTime.UtcNow;

                var filter = Builders<RateLimitRule>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.ReplaceOneAsync(RateLimitRuleCollectionName, filter, rule);
                
                _logger.LogInformation("更新限流规则成功，ID: {Id}", id);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新限流规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取限流规则
        /// </summary>
        public async Task<RateLimitRule> GetRateLimitRuleAsync(string id)
        {
            try
            {
                var filter = Builders<RateLimitRule>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<RateLimitRule>(RateLimitRuleCollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取所有限流规则
        /// </summary>
        public async Task<List<RateLimitRule>> GetAllRateLimitRulesAsync(bool enabledOnly = false)
        {
            try
            {
                var filterBuilder = Builders<RateLimitRule>.Filter;
                var filter = enabledOnly ? filterBuilder.Eq(x => x.IsEnabled, true) : filterBuilder.Empty;
                var sort = Builders<RateLimitRule>.Sort.Ascending(x => x.Priority);

                var rules = await _mongoRepository.GetCollection<RateLimitRule>(RateLimitRuleCollectionName)
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();

                return rules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流规则列表失败");
                throw;
            }
        }

        /// <summary>
        /// 删除限流规则
        /// </summary>
        public async Task<bool> DeleteRateLimitRuleAsync(string id)
        {
            try
            {
                var filter = Builders<RateLimitRule>.Filter.Eq(x => x.Id, id);
                var result = await _mongoRepository.DeleteOneAsync<RateLimitRule>(RateLimitRuleCollectionName, filter);
                
                _logger.LogInformation("删除限流规则成功，ID: {Id}", id);
                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除限流规则失败，ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 检查是否允许请求
        /// </summary>
        public async Task<RateLimitResult> IsRequestAllowedAsync(string apiConfigId, string clientIdentifier, string requestPath = null)
        {
            try
            {
                // 获取适用的限流规则
                var applicableRules = await GetApplicableRulesAsync(apiConfigId, clientIdentifier, requestPath);
                
                foreach (var rule in applicableRules.OrderBy(r => r.Priority))
                {
                    var result = await CheckRuleAsync(rule, clientIdentifier);
                    if (!result.IsAllowed)
                    {
                        _logger.LogWarning("请求被限流，规则: {RuleName}, 客户端: {ClientId}", rule.Name, clientIdentifier);
                        return result;
                    }
                }

                return new RateLimitResult
                {
                    IsAllowed = true,
                    Reason = "请求允许通过"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查限流规则失败，API配置ID: {ApiConfigId}, 客户端: {ClientId}", apiConfigId, clientIdentifier);
                // 发生错误时默认允许请求
                return new RateLimitResult
                {
                    IsAllowed = true,
                    Reason = "限流检查异常，默认允许"
                };
            }
        }

        /// <summary>
        /// 记录请求
        /// </summary>
        public async Task RecordRequestAsync(string apiConfigId, string clientIdentifier, bool isSuccess = true)
        {
            try
            {
                var applicableRules = await GetApplicableRulesAsync(apiConfigId, clientIdentifier);
                
                foreach (var rule in applicableRules)
                {
                    await UpdateCounterAsync(rule, clientIdentifier, isSuccess);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录请求失败，API配置ID: {ApiConfigId}, 客户端: {ClientId}", apiConfigId, clientIdentifier);
                // 记录失败不影响主流程
            }
        }

        /// <summary>
        /// 获取限流统计信息
        /// </summary>
        public async Task<object> GetRateLimitStatisticsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                // 这里实现限流统计逻辑
                var statistics = new
                {
                    TotalRules = await GetTotalRulesCountAsync(apiConfigId),
                    ActiveRules = await GetActiveRulesCountAsync(apiConfigId),
                    TotalRequests = await GetTotalRequestsCountAsync(apiConfigId, startTime, endTime),
                    BlockedRequests = await GetBlockedRequestsCountAsync(apiConfigId, startTime, endTime),
                    BlockRate = 0.0, // 计算阻塞率
                    TopBlockedClients = await GetTopBlockedClientsAsync(apiConfigId, startTime, endTime)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流统计信息失败");
                throw;
            }
        }

        /// <summary>
        /// 重置限流计数器
        /// </summary>
        public async Task<bool> ResetRateLimitCounterAsync(string apiConfigId, string clientIdentifier)
        {
            try
            {
                var filter = Builders<BsonDocument>.Filter.And(
                    Builders<BsonDocument>.Filter.Eq("apiConfigId", apiConfigId),
                    Builders<BsonDocument>.Filter.Eq("clientIdentifier", clientIdentifier)
                );

                var result = await _mongoRepository.DeleteManyAsync<BsonDocument>(RateLimitCounterCollectionName, filter);
                _logger.LogInformation("重置限流计数器成功，API配置ID: {ApiConfigId}, 客户端: {ClientId}", apiConfigId, clientIdentifier);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置限流计数器失败，API配置ID: {ApiConfigId}, 客户端: {ClientId}", apiConfigId, clientIdentifier);
                return false;
            }
        }

        /// <summary>
        /// 获取客户端限流状态
        /// </summary>
        public async Task<object> GetClientRateLimitStatusAsync(string apiConfigId, string clientIdentifier)
        {
            try
            {
                var applicableRules = await GetApplicableRulesAsync(apiConfigId, clientIdentifier);
                var statuses = new List<object>();

                foreach (var rule in applicableRules)
                {
                    var counter = await GetCounterAsync(rule, clientIdentifier);
                    statuses.Add(new
                    {
                        RuleId = rule.Id,
                        RuleName = rule.Name,
                        LimitType = rule.LimitType.ToString(),
                        Threshold = rule.LimitThreshold,
                        CurrentCount = counter?.Count ?? 0,
                        RemainingQuota = Math.Max(0, rule.LimitThreshold - (counter?.Count ?? 0)),
                        WindowStart = counter?.WindowStart,
                        WindowEnd = counter?.WindowEnd
                    });
                }

                return new
                {
                    ApiConfigId = apiConfigId,
                    ClientIdentifier = clientIdentifier,
                    Rules = statuses,
                    CheckTime = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端限流状态失败，API配置ID: {ApiConfigId}, 客户端: {ClientId}", apiConfigId, clientIdentifier);
                throw;
            }
        }

        /// <summary>
        /// 清理过期的限流数据
        /// </summary>
        public async Task<int> CleanupExpiredRateLimitDataAsync(int olderThanHours = 24)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddHours(-olderThanHours);
                var filter = Builders<BsonDocument>.Filter.Lt("createdAt", cutoffTime);

                var result = await _mongoRepository.DeleteManyAsync<BsonDocument>(RateLimitCounterCollectionName, filter);
                var deletedCount = (int)result.DeletedCount;

                _logger.LogInformation("清理过期限流数据完成，删除记录数: {DeletedCount}", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期限流数据失败");
                throw;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取适用的限流规则
        /// </summary>
        private async Task<List<RateLimitRule>> GetApplicableRulesAsync(string apiConfigId, string clientIdentifier, string requestPath = null)
        {
            var filterBuilder = Builders<RateLimitRule>.Filter;
            var filter = filterBuilder.And(
                filterBuilder.Eq(x => x.IsEnabled, true),
                filterBuilder.Or(
                    filterBuilder.Eq(x => x.ApiConfigId, apiConfigId),
                    filterBuilder.Eq(x => x.ApiConfigId, null) // 全局规则
                )
            );

            var rules = await _mongoRepository.GetCollection<RateLimitRule>(RateLimitRuleCollectionName)
                .Find(filter)
                .ToListAsync();

            // 这里可以添加更复杂的规则匹配逻辑
            return rules;
        }

        /// <summary>
        /// 检查单个规则
        /// </summary>
        private async Task<RateLimitResult> CheckRuleAsync(RateLimitRule rule, string clientIdentifier)
        {
            switch (rule.LimitType)
            {
                case RateLimitType.FixedWindow:
                    return await CheckFixedWindowAsync(rule, clientIdentifier);
                case RateLimitType.SlidingWindow:
                    return await CheckSlidingWindowAsync(rule, clientIdentifier);
                case RateLimitType.TokenBucket:
                    return await CheckTokenBucketAsync(rule, clientIdentifier);
                case RateLimitType.LeakyBucket:
                    return await CheckLeakyBucketAsync(rule, clientIdentifier);
                default:
                    return new RateLimitResult { IsAllowed = true, Reason = "未知限流类型" };
            }
        }

        /// <summary>
        /// 检查固定窗口限流
        /// </summary>
        private async Task<RateLimitResult> CheckFixedWindowAsync(RateLimitRule rule, string clientIdentifier)
        {
            var now = DateTime.UtcNow;
            var windowStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute / (rule.TimeWindowSeconds / 60) * (rule.TimeWindowSeconds / 60), 0);
            
            var counter = await GetOrCreateCounterAsync(rule, clientIdentifier, windowStart);
            
            if (counter.Count >= rule.LimitThreshold)
            {
                return new RateLimitResult
                {
                    IsAllowed = false,
                    RuleId = rule.Id,
                    Reason = "超过固定窗口限流阈值",
                    RemainingQuota = 0,
                    ResetTime = windowStart.AddSeconds(rule.TimeWindowSeconds),
                    RetryAfterSeconds = (int)(windowStart.AddSeconds(rule.TimeWindowSeconds) - now).TotalSeconds,
                    LimitType = rule.LimitType
                };
            }

            return new RateLimitResult
            {
                IsAllowed = true,
                RuleId = rule.Id,
                RemainingQuota = rule.LimitThreshold - counter.Count,
                ResetTime = windowStart.AddSeconds(rule.TimeWindowSeconds),
                LimitType = rule.LimitType
            };
        }

        /// <summary>
        /// 检查滑动窗口限流（简化实现）
        /// </summary>
        private async Task<RateLimitResult> CheckSlidingWindowAsync(RateLimitRule rule, string clientIdentifier)
        {
            // 简化实现，实际应该使用更精确的滑动窗口算法
            return await CheckFixedWindowAsync(rule, clientIdentifier);
        }

        /// <summary>
        /// 检查令牌桶限流（简化实现）
        /// </summary>
        private async Task<RateLimitResult> CheckTokenBucketAsync(RateLimitRule rule, string clientIdentifier)
        {
            // 简化实现，实际应该实现令牌桶算法
            return await CheckFixedWindowAsync(rule, clientIdentifier);
        }

        /// <summary>
        /// 检查漏桶限流（简化实现）
        /// </summary>
        private async Task<RateLimitResult> CheckLeakyBucketAsync(RateLimitRule rule, string clientIdentifier)
        {
            // 简化实现，实际应该实现漏桶算法
            return await CheckFixedWindowAsync(rule, clientIdentifier);
        }

        /// <summary>
        /// 获取或创建计数器
        /// </summary>
        private async Task<RateLimitCounter> GetOrCreateCounterAsync(RateLimitRule rule, string clientIdentifier, DateTime windowStart)
        {
            var counterId = $"{rule.Id}_{clientIdentifier}_{windowStart:yyyyMMddHHmm}";
            var filter = Builders<RateLimitCounter>.Filter.Eq(x => x.Id, counterId);
            
            var counter = await _mongoRepository.FindOneAsync<RateLimitCounter>(RateLimitCounterCollectionName, filter);
            if (counter == null)
            {
                counter = new RateLimitCounter
                {
                    Id = counterId,
                    RuleId = rule.Id,
                    ClientIdentifier = clientIdentifier,
                    Count = 0,
                    WindowStart = windowStart,
                    WindowEnd = windowStart.AddSeconds(rule.TimeWindowSeconds),
                    CreatedAt = DateTime.UtcNow
                };
                await _mongoRepository.InsertOneAsync(RateLimitCounterCollectionName, counter);
            }

            return counter;
        }

        /// <summary>
        /// 获取计数器
        /// </summary>
        private async Task<RateLimitCounter> GetCounterAsync(RateLimitRule rule, string clientIdentifier)
        {
            var now = DateTime.UtcNow;
            var windowStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute / (rule.TimeWindowSeconds / 60) * (rule.TimeWindowSeconds / 60), 0);
            var counterId = $"{rule.Id}_{clientIdentifier}_{windowStart:yyyyMMddHHmm}";
            
            var filter = Builders<RateLimitCounter>.Filter.Eq(x => x.Id, counterId);
            return await _mongoRepository.FindOneAsync<RateLimitCounter>(RateLimitCounterCollectionName, filter);
        }

        /// <summary>
        /// 更新计数器
        /// </summary>
        private async Task UpdateCounterAsync(RateLimitRule rule, string clientIdentifier, bool isSuccess)
        {
            var now = DateTime.UtcNow;
            var windowStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute / (rule.TimeWindowSeconds / 60) * (rule.TimeWindowSeconds / 60), 0);
            var counter = await GetOrCreateCounterAsync(rule, clientIdentifier, windowStart);

            var filter = Builders<RateLimitCounter>.Filter.Eq(x => x.Id, counter.Id);
            var update = Builders<RateLimitCounter>.Update
                .Inc(x => x.Count, 1)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            await _mongoRepository.UpdateOneAsync(RateLimitCounterCollectionName, filter, update);
        }

        // 统计相关的辅助方法
        private async Task<int> GetTotalRulesCountAsync(string apiConfigId) => 0; // 实现统计逻辑
        private async Task<int> GetActiveRulesCountAsync(string apiConfigId) => 0;
        private async Task<int> GetTotalRequestsCountAsync(string apiConfigId, DateTime? startTime, DateTime? endTime) => 0;
        private async Task<int> GetBlockedRequestsCountAsync(string apiConfigId, DateTime? startTime, DateTime? endTime) => 0;
        private async Task<List<object>> GetTopBlockedClientsAsync(string apiConfigId, DateTime? startTime, DateTime? endTime) => new List<object>();

        #endregion
    }

    /// <summary>
    /// 限流计数器
    /// </summary>
    public class RateLimitCounter
    {
        public string Id { get; set; }
        public string RuleId { get; set; }
        public string ClientIdentifier { get; set; }
        public int Count { get; set; }
        public DateTime WindowStart { get; set; }
        public DateTime WindowEnd { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
