﻿using Docpark.ThirdMaster.Services.EmailCollection;
using Docpark.ThirdMaster.Services.Models;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.Controllers
{
    public class CaseNoController : BaseController
    {
        private readonly ICaseNoService caseNoService;

        public CaseNoController(ICaseNoService caseNoService)
        {
            this.caseNoService = caseNoService;
        }

        [HttpGet]
        public async Task<ResultDataDto> Get(string code)
        {
            try
            {
                var number = await caseNoService.Get(code);
                return OnSuccess(number);
            }
            catch (System.Exception ex)
            {
                return OnError("获取失败", ex);
            }
        }
    }
}
