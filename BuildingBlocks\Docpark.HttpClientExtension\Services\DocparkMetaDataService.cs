﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class DocparkMetaDataService : IDocparkMetaDataService
    {
        public DocparkMetaDataService(IDocparkHttpClient httpClient, IDGrpc grpc)
        {
            _httpClient = httpClient;
            BaseAddress = grpc.GetStringAsync(ConstSettings.MetaDataApi).Result;
        }

        public IDocparkHttpClient _httpClient { get; }
        private string BaseAddress { get; set; }

        public Task<List<DocumentTypeDto>> GetDocumentTypes()
        {
            var url = ConstApi.DocumentType_GetAll.ToUrl(BaseAddress);
            var documentTypes = _httpClient.GetAsync<List<DocumentTypeDto>>(url);
            return documentTypes;
        }

        public async Task<DocumentTypeDto> GetDocumentType(string identity)
        {
            var url = ConstApi.DocumentType_Get_get_by_identity.ToUrl(BaseAddress)+ "?identity="+identity;
            var documentType =await _httpClient.GetAsync<DocumentTypeDto>(url);
            return documentType;
        }
    }
}
