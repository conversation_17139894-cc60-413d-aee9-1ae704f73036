﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Expense_details
    {
        /// <summary>
        /// 所属单据id
        /// </summary>
        public string third_bill_guid  { get; set; }
        /// <summary>
        /// 费用明细id
        /// </summary>
        public string expense_details_guid { get; set; }
        /// <summary>
        /// 费用类型名称
        /// </summary>
        public string expense_type_name { get; set; }
        /// <summary>
        /// 费用类型编码
        /// </summary>
        public string expense_type_code { get; set; }
        /// <summary>
        /// 申请金额
        /// </summary>
        public Double apply_amount { get; set; }
        /// <summary>
        /// 财务核定金额
        /// </summary>
        public Double approved_amount { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string expense_remark { get; set; }
    }
}
