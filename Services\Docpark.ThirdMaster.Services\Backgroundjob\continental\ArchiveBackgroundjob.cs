﻿using Docpark.ThirdMaster.Services.Utils;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using Polly.Utilities;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.Backgroundjob.continental
{
    [MasterServiceType("continental")]
    public class ArchiveBackgroundjob : BackgroundService, IThirdMasterBackgroundJob
    {
        private readonly ILogger<ArchiveBackgroundjob> _logger;
        private readonly IObjectQuery objectQuery;
        private readonly IConfiguration configuration;
        private readonly IDocumentWorkflowService documentWorkflowService;
        private readonly IEventBus eventBus;
        protected string configXml = "Archive.xml";


        public ArchiveBackgroundjob(ILogger<ArchiveBackgroundjob> logger,
            IObjectQuery objectQuery, IConfiguration configuration,
            IDocumentWorkflowService documentWorkflowService, IEventBus eventBus)
        {
            _logger = logger;
            this.objectQuery = objectQuery;
            this.configuration = configuration;
            this.documentWorkflowService = documentWorkflowService;
            this.eventBus = eventBus;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            Console.WriteLine("档案归档执行间隔：" + configuration["ArchiveTimeInterval"]);
            await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["ArchiveTimeInterval"])), stoppingToken);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await RunAsync();

                }
                catch (TaskCanceledException)
                {
                    // When the stopping token is canceled, for example, a call made from services.msc,
                    // we shouldn't exit with a non-zero exit code. In other words, this is expected...
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "{Message}", ex.Message);
                    Console.WriteLine("档案归档外层异常：" + ex.Message);
                }
                await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["ArchiveTimeInterval"])), stoppingToken);
            }
        }
        protected async Task RunAsync()
        {
            Console.WriteLine("档案归档");
            StringBuilder strMessage = new StringBuilder();
            strMessage.AppendLine("[档案归档]");
            string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');
            string StepName = "";
            string businessTypeId = "";
            int Wdays = 0;
            try
            {
                foreach (string flowname in flownames)
                {
                    StepName = EncryptionHelper.GetDataXMLByName(configXml, "" + flowname + "/Stepname");
                    businessTypeId = EncryptionHelper.GetDataXMLByName(configXml, "" + flowname + "/BusinessTypeId");
                    Wdays = int.Parse(EncryptionHelper.GetDataXMLByName(configXml, "" + flowname + "/Waitdays"));


                    DateTime QueryData = DateTime.Now.AddDays(0 - Wdays);

                    List<Filter> filters = new List<Filter>();
                    filters.Add(new Filter()
                    {
                        Field = "StepName",
                        Method = "eq",
                        Mode = "eq",
                        Values = new string[] { StepName }
                    });
                    filters.Add(new Filter()
                    {
                        Field = "businesstypeid",
                        Method = "eq",
                        Mode = "eq",
                        Values = new string[] { businessTypeId }
                    });

                    filters.Add(new Filter()
                    {
                        Field = "LastModificationTime",
                        Method = "lte",
                        Mode = "lte",
                        Type = "SysDate",
                        DisplayType = "时间戳",
                        Values = new string[] { QueryData.ToString() }
                    });

                    var invoiceResult = await objectQuery.GetList<InvoiceInfo>(configuration["DocumentType:Invoice"], filters, new List<Sorter>(), 0, 100);


                    foreach (var invoice in invoiceResult.data)
                    {
                        var documentTask = await documentWorkflowService.GetLasterActiveDocumentTask(invoice.MstId);
                        if (documentTask == null)
                        {
                            strMessage.AppendLine($"未找到任务{JsonConvert.SerializeObject(invoice)}");
                            continue;
                        }

                        var message = new WorkflowMQContent()
                        {
                            RequestId = Guid.NewGuid().ToString("N").ToUpper(),
                            MessageType = documentTask.FileType,
                            DocumentID = documentTask.DocumentIdentity,
                            DocumentType = documentTask.DocumentTypeIdentity,
                            MstId = documentTask.MstId,
                            BusinessType = documentTask.BusinessType,
                            DocumentTaskID = documentTask.Id.ToString(),
                            DocumentTaskActionID = documentTask.CurrentTaskActionIdentity,
                            ScanUser = documentTask.ScanUser,
                            OwnUser = documentTask.OwnUser,
                            Name = documentTask.TriggerName,
                            Code = documentTask.TriggerCode,
                            WorkflowId = documentTask.WorkflowIdentity,
                            TriggerMessage = "",
                            TriggerStatus = true,
                            Data = null
                        };

                        string publishMessage = JsonConvert.SerializeObject(message);
                        DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(publishMessage);
                        eventBus.Publish(publishEventData);
                    }
                }
                Console.WriteLine("档案归档执行完成");
                _logger.LogInformation(strMessage.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine("档案归档失败：" + ex.Message);
                strMessage.AppendLine(ex.Message);
                strMessage.AppendLine(ex.StackTrace);
                _logger.LogError(strMessage.ToString(), ex);
            }
        }
        public void SetItemState(ref string ItemJson, string Key, string value)
        {
            SortedList<string, object> Docitems = JsonConvert.DeserializeObject<SortedList<string, object>>(ItemJson);
            if (Docitems.ContainsKey(Key))
            {
                Docitems[Key] = value;
            }

            ItemJson = JsonConvert.SerializeObject(Docitems);
        }
    }
}
