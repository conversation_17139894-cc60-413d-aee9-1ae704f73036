using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 监控服务接口
    /// </summary>
    public interface IMonitoringService
    {
        /// <summary>
        /// 执行健康检查
        /// </summary>
        Task<HealthCheck> PerformHealthCheckAsync(string apiConfigId);

        /// <summary>
        /// 批量执行健康检查
        /// </summary>
        Task<List<HealthCheck>> PerformBatchHealthCheckAsync(List<string> apiConfigIds);

        /// <summary>
        /// 获取健康检查历史
        /// </summary>
        Task<List<HealthCheck>> GetHealthCheckHistoryAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null, int limit = 100);

        /// <summary>
        /// 获取系统健康状态概览
        /// </summary>
        Task<object> GetSystemHealthOverviewAsync();

        /// <summary>
        /// 记录性能指标
        /// </summary>
        Task RecordPerformanceMetricAsync(PerformanceMetric metric);

        /// <summary>
        /// 批量记录性能指标
        /// </summary>
        Task RecordPerformanceMetricsAsync(List<PerformanceMetric> metrics);

        /// <summary>
        /// 获取性能指标
        /// </summary>
        Task<List<PerformanceMetric>> GetPerformanceMetricsAsync(string apiConfigId, string metricName = null, DateTime? startTime = null, DateTime? endTime = null, int limit = 1000);

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        Task<object> GetPerformanceStatisticsAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 清理过期的监控数据
        /// </summary>
        Task<int> CleanupExpiredMonitoringDataAsync(int olderThanDays = 30);

        /// <summary>
        /// 获取API可用性统计
        /// </summary>
        Task<object> GetApiAvailabilityStatisticsAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 获取响应时间趋势
        /// </summary>
        Task<object> GetResponseTimeTrendAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null, string interval = "hour");
    }
}
