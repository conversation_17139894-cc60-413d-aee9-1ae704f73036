using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 监控服务实现
    /// </summary>
    public class MonitoringService : IMonitoringService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly IApiConfigurationService _apiConfigService;
        private readonly IAuthenticationService _authService;
        private readonly HttpClient _httpClient;
        private readonly ILogger<MonitoringService> _logger;

        private const string HealthCheckCollectionName = "health_checks";
        private const string PerformanceMetricCollectionName = "performance_metrics";

        public MonitoringService(
            IMongoRepository mongoRepository,
            IApiConfigurationService apiConfigService,
            IAuthenticationService authService,
            HttpClient httpClient,
            ILogger<MonitoringService> logger)
        {
            _mongoRepository = mongoRepository;
            _apiConfigService = apiConfigService;
            _authService = authService;
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// 执行健康检查
        /// </summary>
        public async Task<HealthCheck> PerformHealthCheckAsync(string apiConfigId)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthCheck = new HealthCheck
            {
                ApiConfigId = apiConfigId,
                CheckTime = DateTime.UtcNow,
                CheckType = "API"
            };

            try
            {
                // 获取API配置
                var apiConfig = await _apiConfigService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    healthCheck.Status = HealthStatus.Unknown;
                    healthCheck.ErrorMessage = "API配置不存在";
                    return healthCheck;
                }

                healthCheck.ApiName = apiConfig.Name;

                // 获取认证配置
                var authConfig = await _authService.GetByIdAsync(apiConfig.AuthenticationConfigId);

                // 构建健康检查请求
                var request = new HttpRequestMessage(HttpMethod.Get, $"{apiConfig.BaseUrl.TrimEnd('/')}/health");
                
                // 如果没有专门的健康检查端点，使用主端点
                if (string.IsNullOrEmpty(apiConfig.HealthCheckEndpoint))
                {
                    request.RequestUri = new Uri($"{apiConfig.BaseUrl.TrimEnd('/')}{apiConfig.Endpoint}");
                    request.Method = new HttpMethod(apiConfig.Method);
                }
                else
                {
                    request.RequestUri = new Uri($"{apiConfig.BaseUrl.TrimEnd('/')}{apiConfig.HealthCheckEndpoint}");
                }

                // 添加认证
                if (authConfig != null)
                {
                    var authResult = await _authService.AuthenticateAsync(authConfig);
                    if (authResult.IsSuccess && authResult.Headers != null)
                    {
                        foreach (var header in authResult.Headers)
                        {
                            request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                        }
                    }
                }

                // 执行请求
                var response = await _httpClient.SendAsync(request);
                stopwatch.Stop();

                healthCheck.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
                healthCheck.StatusCode = (int)response.StatusCode;

                // 判断健康状态
                if (response.IsSuccessStatusCode)
                {
                    if (healthCheck.ResponseTimeMs < 1000)
                    {
                        healthCheck.Status = HealthStatus.Healthy;
                    }
                    else if (healthCheck.ResponseTimeMs < 5000)
                    {
                        healthCheck.Status = HealthStatus.Warning;
                    }
                    else
                    {
                        healthCheck.Status = HealthStatus.Degraded;
                    }
                }
                else
                {
                    healthCheck.Status = HealthStatus.Unhealthy;
                    healthCheck.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                }

                // 记录性能指标
                await RecordPerformanceMetricAsync(new PerformanceMetric
                {
                    ApiConfigId = apiConfigId,
                    ApiName = apiConfig.Name,
                    MetricName = "response_time",
                    Value = healthCheck.ResponseTimeMs,
                    Unit = "ms",
                    MetricType = "latency",
                    Timestamp = DateTime.UtcNow
                });

                await RecordPerformanceMetricAsync(new PerformanceMetric
                {
                    ApiConfigId = apiConfigId,
                    ApiName = apiConfig.Name,
                    MetricName = "availability",
                    Value = response.IsSuccessStatusCode ? 1 : 0,
                    Unit = "boolean",
                    MetricType = "availability",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (TaskCanceledException ex)
            {
                stopwatch.Stop();
                healthCheck.Status = HealthStatus.Unhealthy;
                healthCheck.ErrorMessage = "请求超时";
                healthCheck.ErrorDetails = ex.Message;
                healthCheck.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthCheck.Status = HealthStatus.Unhealthy;
                healthCheck.ErrorMessage = "健康检查失败";
                healthCheck.ErrorDetails = ex.Message;
                healthCheck.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
                _logger.LogError(ex, "健康检查失败，API配置ID: {ApiConfigId}", apiConfigId);
            }

            // 保存健康检查结果
            await _mongoRepository.InsertOneAsync(HealthCheckCollectionName, healthCheck);
            _logger.LogInformation("健康检查完成，API配置ID: {ApiConfigId}, 状态: {Status}, 响应时间: {ResponseTime}ms", 
                apiConfigId, healthCheck.Status, healthCheck.ResponseTimeMs);

            return healthCheck;
        }

        /// <summary>
        /// 批量执行健康检查
        /// </summary>
        public async Task<List<HealthCheck>> PerformBatchHealthCheckAsync(List<string> apiConfigIds)
        {
            var tasks = apiConfigIds.Select(PerformHealthCheckAsync);
            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }

        /// <summary>
        /// 获取健康检查历史
        /// </summary>
        public async Task<List<HealthCheck>> GetHealthCheckHistoryAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null, int limit = 100)
        {
            try
            {
                var filterBuilder = Builders<HealthCheck>.Filter;
                var filter = filterBuilder.Eq(x => x.ApiConfigId, apiConfigId);

                if (startTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Gte(x => x.CheckTime, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Lte(x => x.CheckTime, endTime.Value));
                }

                var sort = Builders<HealthCheck>.Sort.Descending(x => x.CheckTime);

                var healthChecks = await _mongoRepository.GetCollection<HealthCheck>(HealthCheckCollectionName)
                    .Find(filter)
                    .Sort(sort)
                    .Limit(limit)
                    .ToListAsync();

                return healthChecks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取健康检查历史失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 获取系统健康状态概览
        /// </summary>
        public async Task<object> GetSystemHealthOverviewAsync()
        {
            try
            {
                var collection = _mongoRepository.GetCollection<HealthCheck>(HealthCheckCollectionName);
                var cutoffTime = DateTime.UtcNow.AddMinutes(-30); // 最近30分钟的数据

                // 获取最新的健康检查结果
                var pipeline = new[]
                {
                    new BsonDocument("$match", new BsonDocument("CheckTime", new BsonDocument("$gte", cutoffTime))),
                    new BsonDocument("$sort", new BsonDocument("CheckTime", -1)),
                    new BsonDocument("$group", new BsonDocument
                    {
                        { "_id", "$ApiConfigId" },
                        { "latestCheck", new BsonDocument("$first", "$$ROOT") }
                    }),
                    new BsonDocument("$replaceRoot", new BsonDocument("newRoot", "$latestCheck"))
                };

                var latestChecks = await collection.Aggregate<HealthCheck>(pipeline).ToListAsync();

                var overview = new
                {
                    TotalApis = latestChecks.Count,
                    HealthyApis = latestChecks.Count(c => c.Status == HealthStatus.Healthy),
                    WarningApis = latestChecks.Count(c => c.Status == HealthStatus.Warning),
                    UnhealthyApis = latestChecks.Count(c => c.Status == HealthStatus.Unhealthy),
                    DegradedApis = latestChecks.Count(c => c.Status == HealthStatus.Degraded),
                    UnknownApis = latestChecks.Count(c => c.Status == HealthStatus.Unknown),
                    AverageResponseTime = latestChecks.Where(c => c.ResponseTimeMs > 0).Average(c => c.ResponseTimeMs),
                    OverallHealthPercentage = latestChecks.Count > 0 ? 
                        (double)latestChecks.Count(c => c.Status == HealthStatus.Healthy) / latestChecks.Count * 100 : 0,
                    LastUpdateTime = DateTime.UtcNow
                };

                return overview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统健康状态概览失败");
                throw;
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        public async Task RecordPerformanceMetricAsync(PerformanceMetric metric)
        {
            try
            {
                metric.CreatedAt = DateTime.UtcNow;
                await _mongoRepository.InsertOneAsync(PerformanceMetricCollectionName, metric);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录性能指标失败，API配置ID: {ApiConfigId}, 指标: {MetricName}", 
                    metric.ApiConfigId, metric.MetricName);
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 批量记录性能指标
        /// </summary>
        public async Task RecordPerformanceMetricsAsync(List<PerformanceMetric> metrics)
        {
            try
            {
                foreach (var metric in metrics)
                {
                    metric.CreatedAt = DateTime.UtcNow;
                }
                foreach (var metric in metrics)
                {
                    await _mongoRepository.InsertOneAsync(PerformanceMetricCollectionName, metric);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量记录性能指标失败");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        public async Task<List<PerformanceMetric>> GetPerformanceMetricsAsync(string apiConfigId, string metricName = null, DateTime? startTime = null, DateTime? endTime = null, int limit = 1000)
        {
            try
            {
                var filterBuilder = Builders<PerformanceMetric>.Filter;
                var filter = filterBuilder.Eq(x => x.ApiConfigId, apiConfigId);

                if (!string.IsNullOrEmpty(metricName))
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.MetricName, metricName));
                }

                if (startTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Gte(x => x.Timestamp, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Lte(x => x.Timestamp, endTime.Value));
                }

                var sort = Builders<PerformanceMetric>.Sort.Descending(x => x.Timestamp);

                var metrics = await _mongoRepository.GetCollection<PerformanceMetric>(PerformanceMetricCollectionName)
                    .Find(filter)
                    .Sort(sort)
                    .Limit(limit)
                    .ToListAsync();

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        public async Task<object> GetPerformanceStatisticsAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var responseTimeMetrics = await GetPerformanceMetricsAsync(apiConfigId, "response_time", startTime, endTime);
                var availabilityMetrics = await GetPerformanceMetricsAsync(apiConfigId, "availability", startTime, endTime);

                var statistics = new
                {
                    ResponseTime = new
                    {
                        Average = responseTimeMetrics.Any() ? responseTimeMetrics.Average(m => m.Value) : 0,
                        Min = responseTimeMetrics.Any() ? responseTimeMetrics.Min(m => m.Value) : 0,
                        Max = responseTimeMetrics.Any() ? responseTimeMetrics.Max(m => m.Value) : 0,
                        P95 = responseTimeMetrics.Any() ? CalculatePercentile(responseTimeMetrics.Select(m => m.Value).ToList(), 0.95) : 0,
                        P99 = responseTimeMetrics.Any() ? CalculatePercentile(responseTimeMetrics.Select(m => m.Value).ToList(), 0.99) : 0
                    },
                    Availability = new
                    {
                        Percentage = availabilityMetrics.Any() ? availabilityMetrics.Average(m => m.Value) * 100 : 0,
                        TotalRequests = availabilityMetrics.Count,
                        SuccessfulRequests = availabilityMetrics.Count(m => m.Value == 1),
                        FailedRequests = availabilityMetrics.Count(m => m.Value == 0)
                    },
                    Period = new
                    {
                        StartTime = startTime ?? (responseTimeMetrics.Any() ? responseTimeMetrics.Min(m => m.Timestamp) : DateTime.UtcNow),
                        EndTime = endTime ?? (responseTimeMetrics.Any() ? responseTimeMetrics.Max(m => m.Timestamp) : DateTime.UtcNow)
                    }
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能统计信息失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 清理过期的监控数据
        /// </summary>
        public async Task<int> CleanupExpiredMonitoringDataAsync(int olderThanDays = 30)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                var totalDeleted = 0;

                // 清理过期的健康检查数据
                var healthCheckFilter = Builders<HealthCheck>.Filter.Lt(x => x.CreatedAt, cutoffDate);
                var healthCheckResult = await _mongoRepository.DeleteManyAsync<HealthCheck>(HealthCheckCollectionName, healthCheckFilter);
                totalDeleted += (int)healthCheckResult.DeletedCount;

                // 清理过期的性能指标数据
                var metricFilter = Builders<PerformanceMetric>.Filter.Lt(x => x.CreatedAt, cutoffDate);
                var metricResult = await _mongoRepository.DeleteManyAsync<PerformanceMetric>(PerformanceMetricCollectionName, metricFilter);
                totalDeleted += (int)metricResult.DeletedCount;

                _logger.LogInformation("清理过期监控数据完成，删除记录数: {TotalDeleted}", totalDeleted);
                return totalDeleted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期监控数据失败");
                throw;
            }
        }

        /// <summary>
        /// 获取API可用性统计
        /// </summary>
        public async Task<object> GetApiAvailabilityStatisticsAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var healthChecks = await GetHealthCheckHistoryAsync(apiConfigId, startTime, endTime, 10000);

                var availability = new
                {
                    TotalChecks = healthChecks.Count,
                    HealthyChecks = healthChecks.Count(c => c.Status == HealthStatus.Healthy),
                    UnhealthyChecks = healthChecks.Count(c => c.Status == HealthStatus.Unhealthy),
                    WarningChecks = healthChecks.Count(c => c.Status == HealthStatus.Warning),
                    DegradedChecks = healthChecks.Count(c => c.Status == HealthStatus.Degraded),
                    AvailabilityPercentage = healthChecks.Count > 0 ? 
                        (double)healthChecks.Count(c => c.Status == HealthStatus.Healthy) / healthChecks.Count * 100 : 0,
                    UptimePercentage = healthChecks.Count > 0 ? 
                        (double)healthChecks.Count(c => c.Status != HealthStatus.Unhealthy) / healthChecks.Count * 100 : 0
                };

                return availability;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取API可用性统计失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 获取响应时间趋势
        /// </summary>
        public async Task<object> GetResponseTimeTrendAsync(string apiConfigId, DateTime? startTime = null, DateTime? endTime = null, string interval = "hour")
        {
            try
            {
                var healthChecks = await GetHealthCheckHistoryAsync(apiConfigId, startTime, endTime, 10000);

                // 根据间隔分组数据
                IEnumerable<IGrouping<object, HealthCheck>> groupedData;
                switch (interval.ToLower())
                {
                    case "minute":
                        groupedData = healthChecks.GroupBy(c => new { c.CheckTime.Year, c.CheckTime.Month, c.CheckTime.Day, c.CheckTime.Hour, c.CheckTime.Minute }).Cast<IGrouping<object, HealthCheck>>();
                        break;
                    case "hour":
                        groupedData = healthChecks.GroupBy(c => new { c.CheckTime.Year, c.CheckTime.Month, c.CheckTime.Day, c.CheckTime.Hour }).Cast<IGrouping<object, HealthCheck>>();
                        break;
                    case "day":
                        groupedData = healthChecks.GroupBy(c => new { c.CheckTime.Year, c.CheckTime.Month, c.CheckTime.Day }).Cast<IGrouping<object, HealthCheck>>();
                        break;
                    default:
                        groupedData = healthChecks.GroupBy(c => new { c.CheckTime.Year, c.CheckTime.Month, c.CheckTime.Day, c.CheckTime.Hour }).Cast<IGrouping<object, HealthCheck>>();
                        break;
                }

                var trend = groupedData.Select(g => new
                {
                    Timestamp = g.First().CheckTime,
                    AverageResponseTime = g.Average(c => c.ResponseTimeMs),
                    MinResponseTime = g.Min(c => c.ResponseTimeMs),
                    MaxResponseTime = g.Max(c => c.ResponseTimeMs),
                    CheckCount = g.Count()
                }).OrderBy(t => t.Timestamp).ToList();

                return trend;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取响应时间趋势失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 计算百分位数
        /// </summary>
        private double CalculatePercentile(List<double> values, double percentile)
        {
            if (!values.Any()) return 0;

            values.Sort();
            var index = (int)Math.Ceiling(values.Count * percentile) - 1;
            return values[Math.Max(0, Math.Min(index, values.Count - 1))];
        }

        #endregion
    }
}
