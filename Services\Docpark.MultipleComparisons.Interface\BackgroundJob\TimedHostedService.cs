﻿using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Comm;
using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Models.Entity;
using Docpark.MultipleComparisons.Interface.Services;
using DocPark.MongoDb;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NuonuoSDK;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.BackgroundJob
{
    public class TimedHostedService : BackgroundService
    {
        private readonly ILogger<TimedHostedService> _logger;
        private readonly IConfiguration configuration;
        private readonly IComHttpClient _comHttpClient;
        private readonly IMongodbManager mongodbManager;
        private readonly IObjectQuery objectQuery;
        private readonly IIdentiFicationService _identiFicationService;
        private readonly IThirdPartyMongoDbService callLogService;
        private readonly IMultipComparisonsService multipComparisonsService;
        private readonly IThirdpartyOrganizationService organizationService;

        public TimedHostedService(ILogger<TimedHostedService> logger, IMongodbManager mongodbManager, IObjectQuery objectQuery, IComHttpClient _comHttpClient, IThirdPartyMongoDbService callLogService, IConfiguration configuration, IMultipComparisonsService multipComparisonsService, IIdentiFicationService identiFicationService, IThirdpartyOrganizationService organizationService)
        {
            _logger = logger;
            this.mongodbManager = mongodbManager;
            this.objectQuery = objectQuery;
            this._comHttpClient = _comHttpClient;
            this.callLogService = callLogService;
            this.configuration = configuration;
            this.multipComparisonsService = multipComparisonsService;
            this._identiFicationService = identiFicationService;
            this.organizationService = organizationService;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            #region 执行触发器业务
            try
            {
                DateTime beginTimeStamp = new DateTime(1949, 10, 1);
                int setTimeCycle = 2; // 定时周期, 默认5分钟
                try
                {
                    setTimeCycle = this.configuration.GetValue<int>("timeCycle");
                }
                catch { }
                int timeInterval = 1000 * 60 * setTimeCycle;
                stoppingToken.Register(() => Console.WriteLine("* Background job task is stopping."));

                //定时间隔采用延时的方式实现
                await Task.Delay(timeInterval, stoppingToken);
                while (!stoppingToken.IsCancellationRequested)
                {
                    //await Task.Run(() =>
                    //{
                    //    try
                    //    {
                    //        this.SuccessData();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        throw ex;
                    //    }
                    //});

                    //await Task.Run(() =>
                    //{
                    //    try
                    //    {
                    //        this.BackData();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        throw ex;
                    //    }
                    //});
                    //await Task.Run(() =>
                    //{
                    //    try
                    //    {
                    //        this.ArchiveData();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        throw ex;
                    //    }
                    //});
                    //await Task.Run(() =>
                    //{
                    //    try
                    //    {
                    //        this.SendToUpdateInvoice();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        throw ex;
                    //    }
                    //});
                    await Task.Run(() =>
                    {
                        try
                        {
                            this.SuccessDataToReim();
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                    });
                    //await Task.Run(() =>
                    //{
                    //    try
                    //    {
                    //        this.SyncThirdpartyOrganization(beginTimeStamp.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    //        //beginTimeStamp = DateTime.Now; // 更改同步时间
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        throw ex;
                    //    }
                    //});

                    //定时间隔采用延时的方式实现  
                    await Task.Delay(timeInterval, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            #endregion


            await Task.CompletedTask;
        }

        /// <summary>
        /// 单据比对成功通知第三方业务系统API
        /// </summary>
        public async void SuccessData()
        {
            try
            {
                _logger.LogError("进入单据比对成功通知第三方业务系统API方法：SuccessData");
                int ispost = 0;
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "business_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "审批通过" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "03" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { ispost.ToString() }
                });
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);


                var thirdparty_bill_callback_apis = configuration.GetValue<string>("thirdparty_bill_callback_apis");

                if (selectMasterList.totalCount > 0)
                {
                    foreach (var item in selectMasterList.data)
                    {
                        Dictionary<string, object> parameters = new Dictionary<string, object>
                        {
                            ["barcode"] = item.bill_no,
                            ["type"] = "0"
                        };
                        var request = _comHttpClient.Post(thirdparty_bill_callback_apis, parameters);
                        if (!string.IsNullOrEmpty(request))
                        {
                            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(request);
                            var result = jsonResult.getString("result");
                            var msg = jsonResult.getString("errormsg");
                            if (result == "0")//成功
                            {
                                Dictionary<string, object> saveData = new Dictionary<string, object>();
                                saveData["ispost"] = 1;
                                await this.objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", item.mstId, saveData);
                            }
                            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                            {
                                ApiName = thirdparty_bill_callback_apis,
                                ApiDesc = "单据比对成功通知第三方业务系统API",
                                CallTime = DateTime.Now,
                                CallResult = result == "0" ? true : false,
                                CallResultData = "返回结果result:" + result + "返回信息errormsg:" + msg,
                                CallResultMsg = msg,
                                RequestData = "单据编号:" + item.bill_no + "单据mstId：" + item.mstId + "单据类别：(0为通过，1为驳回)" + 0,
                                Remark = ""
                            });
                        }
                    }
                }
                _logger.LogError("单据比对成功通知第三方业务系统API:SuccessData   执行成功");
            }
            catch (Exception ex)
            {
                _logger.LogError("单据比对成功通知第三方业务系统API，执行异常，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 单据扫描岗回退通知第三方业务系统API
        /// </summary>
        public async void BackData()
        {
            try
            {
                _logger.LogError("进入单据扫描岗回退通知第三方业务系统API方法：BackData");
                int ispost = 0;
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "04" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "ispost",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { ispost.ToString() }
                });
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);


                //获取请求的api地址
                var thirdparty_bill_callback_apis = configuration.GetValue<string>("thirdparty_bill_callback_apis");

                if (selectMasterList.totalCount > 0)
                {
                    foreach (var item in selectMasterList.data)
                    {
                        Dictionary<string, object> parameters = new Dictionary<string, object>
                        {
                            ["barcode"] = item.bill_no,
                            ["type"] = "1"
                        };
                        var request = _comHttpClient.Post(thirdparty_bill_callback_apis, parameters);
                        if (!string.IsNullOrEmpty(request))
                        {
                            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(request);
                            var result = jsonResult.getString("result");
                            var msg = jsonResult.getString("errormsg");
                            if (result == "0")
                            {
                                Dictionary<string, object> saveData = new Dictionary<string, object>();
                                saveData["ispost"] = 1;
                                await this.objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_MASTER_DATA", item.mstId, saveData);
                            }
                            await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                            {
                                ApiName = "BILL_SCAN_FALLBACK_NOTICE",
                                ApiDesc = "单据扫描岗回退通知第三方业务系统API",
                                CallTime = DateTime.Now,
                                CallResult = result == "0" ? true : false,
                                CallResultData = "返回结果result:" + result + "返回信息errormsg:" + msg,
                                CallResultMsg = msg,
                                RequestData = "单据编号:" + item.bill_no + "单据mstId：" + item.mstId + "单据类别：(0为通过，1为驳回)" + 1,
                                Remark = "调用API: " + thirdparty_bill_callback_apis
                            });
                        }

                    }
                }
                _logger.LogError("单据扫描岗回退通知第三方业务系统API:BackData    执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("单据比对成功通知第三方业务系统API，执行异常，原因：" + ex.Message);
            }

        }

        /// <summary>
        /// 归档数据推送API
        /// </summary>
        public async void ArchiveData()
        {
            try
            {
                _logger.LogError("归档数据推送API：ArchiveData");
                #region 获取电子档案的Token值
                var eleLoginToken = this.multipComparisonsService.GetEleArchivesLoginToken();
                _logger.LogError("获取电子档案的Token值：GetEleArchivesLoginToken");
                if (string.IsNullOrEmpty(eleLoginToken))
                {
                    throw new Exception("获取第三方电子档案登录接口获取Token值失败。");
                }
                #endregion

                // 推送OA单据数据
                await this.multipComparisonsService.Push_OA_Bill_Archives_Data(eleLoginToken);
                _logger.LogError("推送OA单据数据：Push_OA_Bill_Archives_Data");
                // 推送OA单据发票数据
                await this.multipComparisonsService.Push_OA_Invoice_Archives_Data(eleLoginToken);
                _logger.LogError("推送OA单据发票数据：Push_OA_Invoice_Archives_Data");
                // 推送凭证数据
                await this.multipComparisonsService.Push_Voucher_Archives_Data(eleLoginToken);
                _logger.LogError("推送凭证数据：Push_Voucher_Archives_Data");
                // 推送电子回单数据
                await this.multipComparisonsService.Push_Ele_Receipt_Archives_Data(eleLoginToken);
                _logger.LogError("推送电子回单数据：Push_Ele_Receipt_Archives_Data");
                //实现推送 凭证和单据关系接口API
                await this.multipComparisonsService.SendOaMaster(eleLoginToken);
                _logger.LogError("实现推送 凭证和单据关系接口API：SendOaMaster");
                //实现实现推送 凭证和发票关系接口API
                await this.multipComparisonsService.SendInvoice(eleLoginToken);
                _logger.LogError("实现实现推送 凭证和发票关系接口API：SendInvoice");
                //实现推送 凭证和流水关系接口API
                await this.multipComparisonsService.Send_Archives_Data(eleLoginToken);
                _logger.LogError("实现推送 凭证和流水关系接口API：Send_Archives_Data");
            }
            catch (Exception ex)
            {
                _logger.LogError("归档数据推送给第三方电子档案API，执行异常，原因：" + ex.Message);
            }

        }

        /// <summary>
        /// 单据比对成功修改报销单状态
        /// </summary>
        public async void SuccessDataToReim()
        {
            bool result = false;
            try
            {
                var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "business_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "审批通过" }
                });
                filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                {
                    DisplayType = "文本",
                    Field = "sys_status",
                    Method = "eq",
                    Mode = "eq",
                    Type = "Text",
                    Values = new string[] { "03" }
                });
                var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
                var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 50);
                if (selectMasterList.totalCount > 0 && selectMasterList.data != null)
                {
                    //筛选出bill_no
                    var bill_no_Array = selectMasterList.data.Select(s => s.bill_no).ToArray();

                    var filter = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                    filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "ArchivesKey",
                        Method = "in",
                        Mode = "in",
                        Type = "Text",
                        Values = bill_no_Array
                    });
                    filter.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "state",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { "审批通过" }
                    });
                    var selectReimList = await this.objectQuery.GetList<Reimbursement>(filter, sorters, 0, 50);

                    if (selectReimList.totalCount > 0 && selectReimList.data != null)
                    {
                        for (int i = 0; i < selectReimList.totalCount; i++)
                        {
                            selectReimList.data[i].state = "比对完成"; // 更改凭证是否发送的属性为: 比对完成
                        }
                        result = await this.objectQuery.BulkCreateOrUpdate<Reimbursement>(selectReimList.data);
                    }
                    await this.callLogService.AddCallLog(new MONGODB_THIRDPARTY_CALL_LOG()
                    {
                        ApiName = "报销单",
                        ApiDesc = "单据比对成功通知第三方业务系统API",
                        CallTime = DateTime.Now,
                        CallResult = result,
                        CallResultData = "返回结果result:" + result,
                        CallResultMsg = result ? "成功" : "失败",
                        RequestData = "单据编号:" + selectMasterList.data[0].bill_no + "单据类别：(0为通过，1为驳回)" + 0,
                        Remark = ""
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("单据比对成功通知第三方业务系统API，执行异常，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 定时同步第三方人员组织架构服务
        /// </summary>
        public void SyncThirdpartyOrganization(string beginTimeStamp)
        {
            try
            {
                string errMsg = "";
                _logger.LogInformation($"1. [开始]同步第三方组织机构信息");
                var resOrg = this.organizationService.SyncThirdpartyOrganization(new Dictionary<string, object>()
                {
                    ["returnOrgType"] = "[{\"type\":\"org\"},{\"type\":\"dept\"}]",
                    ["count"] = 999,
                    ["beginTimeStamp"] = beginTimeStamp
                }, out errMsg);

                _logger.LogInformation($"1. [结束]同步第三方组织机构信息，执行结果({resOrg}), 返回消息：{errMsg}");

                int personCount = 999;
                try
                {
                    personCount = configuration.GetValue<int>("thirdparty_org:rec_person_count");
                }
                catch { }

                _logger.LogInformation($"2. [开始]同步第三方组织机构人员信息");
                var resUser = this.organizationService.SyncThirdpartyUser(new Dictionary<string, object>()
                {
                    ["returnOrgType"] = "[{\"type\":\"person\"}]",
                    ["count"] = 999,
                    ["beginTimeStamp"] = beginTimeStamp
                }, out errMsg);

                _logger.LogInformation($"2. [结束]同步第三方组织机构人员信息，执行结果({resUser}), 返回消息：{errMsg}");
            }
            catch (Exception ex)
            {
                _logger.LogError("定时同步第三方组织架构服务，执行异常，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 单据发票验真
        /// </summary>
        public async void SendToUpdateInvoice()
        {
            _logger.LogError("进入单据发票验真方法：SendToUpdateInvoice");
            #region   查询已签收的状态的发票
            var filters = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
            filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
            {
                DisplayType = "文本",
                Field = "business_status",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { "审批通过" }
            });
            filters.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
            {
                DisplayType = "文本",
                Field = "sys_status",
                Method = "eq",
                Mode = "eq",
                Type = "Text",
                Values = new string[] { "03" }
            });
            var sorters = new List<Docpark.HttpClientExtension.IServices.Dto.Sorter>();
            sorters.Add(new Sorter()
            {
                Field = "CreationTime",
                Order = "descend"
            });
            var selectMasterList = await this.objectQuery.GetList<MC_BILL_MASTER_DATA>(filters, sorters, 0, 10);

            if (selectMasterList.totalCount > 0 && selectMasterList.data != null)
            {
                foreach (var item in selectMasterList.data)
                {
                    var filterinvoice = new List<Docpark.HttpClientExtension.IServices.Dto.Filter>();
                    filterinvoice.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "bill_no",
                        Method = "eq",
                        Mode = "eq",
                        Type = "Text",
                        Values = new string[] { item.bill_no }
                    });
                    filterinvoice.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                    {
                        DisplayType = "文本",
                        Field = "verifiy_result",
                        Method = "empty",
                        Mode = "empty",
                        Type = "Text",
                        Values = new string[] { "" }
                    });
                    //获取与主数据信息对应的发票信息
                    var selectInvoiceList = await this.objectQuery.GetList<MC_BILL_INVOICE_DATA>(filterinvoice, sorters, 0, 10);

                    string errmsg = "";

                    if (selectInvoiceList.totalCount > 0 && selectInvoiceList.data != null)
                    {
                        foreach (var itemInvoice in selectInvoiceList.data)
                        {
                            //var Identification_url = configuration.GetValue<string>("Identification_url");
                            //var method_url = configuration.GetValue<string>("method_url");
                            //string senid = Guid.NewGuid().ToString("N").ToUpper(); // 唯一标识，由企业自己生成32位随机码


                            var companyName = itemInvoice.buyer;

                            if (!string.IsNullOrEmpty(companyName))
                            {
                                //获取远东企业信息
                                var filterYd = new List<Filter>();

                                filterYd.Add(new Docpark.HttpClientExtension.IServices.Dto.Filter()
                                {
                                    DisplayType = "文本",
                                    Field = "CompanyName",
                                    Method = "eq",
                                    Mode = "eq",
                                    Type = "Text",
                                    Values = new string[] { companyName }
                                });
                                var selectYdDataList = await this.objectQuery.GetList<YDCompanyFormData>(filterYd, sorters, 0, 1);

                                if (selectYdDataList.totalCount > 0 && selectYdDataList.data != null)
                                {
                                    var invoiceDate = Convert.ToDateTime(itemInvoice.date).ToString("yyyy-MM-dd");
                                    //判断发票类型    若为二手车发票，传参为车价合计  否则为校验码的后6位
                                    var optionField = itemInvoice.type == "10105" ? itemInvoice.total.ToString() : itemInvoice.check_code.Length > 6 ? itemInvoice.check_code.Substring(itemInvoice.check_code.Length - 6) : itemInvoice.check_code;

                                    string verify_result = "";
                                    if (!string.IsNullOrEmpty(selectYdDataList.data[0].CompanyCode) && !string.IsNullOrEmpty(itemInvoice.code) && !string.IsNullOrEmpty(itemInvoice.number) && !string.IsNullOrEmpty(invoiceDate) && !string.IsNullOrEmpty(optionField))
                                    {
                                        verify_result = this._identiFicationService.VerifyResult(selectYdDataList.data[0].CompanyCode, itemInvoice.code, itemInvoice.number, invoiceDate, optionField);
                                    }
                                    var dic = new Dictionary<string, object>();
                                    if (!string.IsNullOrEmpty(verify_result))
                                    {
                                        JObject jsonResult = JsonConvert.DeserializeObject<JObject>(verify_result);
                                        var code = jsonResult.getString("code");
                                        // 0000   验真成功
                                        if (code == "0000")
                                        {
                                            dic["verifiy_result"] = "01"; //验真成功
                                        }
                                        else
                                        {
                                            dic["verifiy_result"] = "00";//验真失败
                                        }
                                    }
                                    else
                                    {
                                        dic["verifiy_result"] = "00";//验真失败
                                    }
                                    await this.objectQuery.CreateOrUpdate<Dictionary<string, object>>("MC_BILL_INVOICE_DATA", itemInvoice.MstId, dic);
                                }
                            }
                        }
                    }
                }
            }
            _logger.LogError("进入单据发票验真方法：SendToUpdateInvoice     执行完成");


            #endregion
        }

    }
}
