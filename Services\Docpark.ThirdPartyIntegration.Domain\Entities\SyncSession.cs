using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 同步会话实体
    /// </summary>
    public class SyncSession
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 会话ID（用于关联多个同步操作）
        /// </summary>
        [Required]
        public string SessionId { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 同步配置ID
        /// </summary>
        [Required]
        public string SyncConfigId { get; set; }

        /// <summary>
        /// 同步策略
        /// </summary>
        [Required]
        public SyncStrategy Strategy { get; set; }

        /// <summary>
        /// 同步状态
        /// </summary>
        [Required]
        public SyncStatus Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 持续时间（毫秒）
        /// </summary>
        public long? DurationMs { get; set; }

        /// <summary>
        /// 同步起始时间戳
        /// </summary>
        public DateTime? SyncStartTimestamp { get; set; }

        /// <summary>
        /// 同步结束时间戳
        /// </summary>
        public DateTime? SyncEndTimestamp { get; set; }

        /// <summary>
        /// 检查点值
        /// </summary>
        public string CheckpointValue { get; set; }

        /// <summary>
        /// 数据版本号
        /// </summary>
        public string DataVersion { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 处理记录数
        /// </summary>
        public int ProcessedRecords { get; set; }

        /// <summary>
        /// 成功记录数
        /// </summary>
        public int SuccessRecords { get; set; }

        /// <summary>
        /// 失败记录数
        /// </summary>
        public int FailedRecords { get; set; }

        /// <summary>
        /// 跳过记录数
        /// </summary>
        public int SkippedRecords { get; set; }

        /// <summary>
        /// 冲突记录数
        /// </summary>
        public int ConflictRecords { get; set; }

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public long DataSize { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public string ErrorDetails { get; set; }

        /// <summary>
        /// 同步统计信息
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument Statistics { get; set; }

        /// <summary>
        /// 同步元数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument Metadata { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
