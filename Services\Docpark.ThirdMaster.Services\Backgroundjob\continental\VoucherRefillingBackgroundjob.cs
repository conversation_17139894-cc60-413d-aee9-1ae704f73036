﻿using Docpark.ThirdMaster.Services.Utils;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.HttpClientExtension.Services;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using Microsoft.JSInterop.Infrastructure;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Spreadsheet;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using Polly.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.Backgroundjob.continental
{
    [MasterServiceType("continental")]
    /// <summary>
    /// 凭证返填
    /// </summary>
    public class VoucherRefillingBackgroundjob : BackgroundService,IThirdMasterBackgroundJob
    {


        private readonly ILogger<VoucherRefillingBackgroundjob> _logger;
        private readonly IObjectQuery objectQuery;
        private readonly IConfiguration configuration;
        private readonly IDocumentWorkflowService documentWorkflowService;
        private readonly IEventBus eventBus;
        protected string configXml = "VoucherRefilling.xml";
        /// <summary>
        /// 国内发票类型
        /// </summary>
        private readonly string[] types = new string[] { "10100", "10101", "10102", "10103", "10106", "10107", "10108" };
        public VoucherRefillingBackgroundjob(ILogger<VoucherRefillingBackgroundjob> logger,
            IObjectQuery objectQuery, IConfiguration configuration,
            IDocumentWorkflowService documentWorkflowService, IEventBus eventBus)
        {
            _logger = logger;
            this.objectQuery = objectQuery;
            this.configuration = configuration;
            this.documentWorkflowService = documentWorkflowService;
            this.eventBus = eventBus;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["TimeInterval"])), stoppingToken);
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await RunAsync();
                }
                catch (TaskCanceledException)
                {
                    // When the stopping token is canceled, for example, a call made from services.msc,
                    // we shouldn't exit with a non-zero exit code. In other words, this is expected...
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "{Message}", ex.Message);
                }
                await Task.Delay(TimeSpan.FromMinutes(int.Parse(configuration["TimeInterval"])), stoppingToken);
            }
        }

        protected async Task RunAsync()
        {
            StringBuilder strMessage = new StringBuilder();
            strMessage.AppendLine("[凭证返填]=====================================================================");
            bool flag = true;
            string[] flownames = EncryptionHelper.GetDataXMLByName(configXml, "flowset/FlowName").Split(',');
            NPOIHelper eh = new NPOIHelper();

            DataSet ds = new DataSet();
            DataTable dt = new DataTable();
            string StepName = "";
            string businessTypeId = "";

            //公司列表
            var companyList = await objectQuery
                .GetList<CompanyInfo>(configuration["DocumentType:Company"], new List<Filter>() { }, new List<Sorter>(), 0, 1);

            StringBuilder strErrorMessage = new StringBuilder();
            var strErrorMessageFormatter = "{0} 第{1}行   发票号码:{2}    发票日期:{3}    公司编号:{4}    错误消息:{5}";


            string DateFileName = DateTime.Now.ToString("yyyyMMddHHmmssfff ");

            foreach (string flowname in flownames)
            {
                strMessage.AppendLine($"开始执行>:{flowname}");
                List<FileInfo> FileList = GetReadExcelInfo(flowname, strMessage);
                strMessage.AppendLine($"获取文件个数:{FileList.Count}");

                var filterColumns = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/FilterColumns").Split(",");
                StepName = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/Stepname");
                businessTypeId = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/BusinessTypeId");

                strMessage.AppendLine("filterColumns:" + string.Join(",", filterColumns.ToArray()));
                strMessage.AppendLine("StepName:" + StepName);


                foreach (FileInfo fi in FileList)
                {
                    List<string> missingField = new List<string>();

                    bool isok = true; //是否可以执行返填操作
                    try
                    {
                        strMessage.AppendLine($"处理文件:{fi.FullName}");


                        dt = eh.ImportExceltoDt(fi.FullName);
                        if (dt.Rows.Count == 0)
                        {
                            isok = false;
                            strMessage.AppendLine(flowname + "   读取Excel异常，可稍后重试");
                            continue;
                        }

                        int icount = 1;
                        if (dt != null)
                        {
                            strMessage.AppendLine("读取到记录数:" + dt.Rows.Count);

                            var excelColumns = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/ExcelColumns");
                            var model = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/Mode");
                            var amountField = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/AmountField");
                            var columns = excelColumns.Split(',');
                            foreach (var column in columns)
                            {
                                ///Remark字段不需要必填
                                if (!dt.Columns.Contains(column) && column != "Remark")
                                {
                                    missingField.Add(column);
                                    //列名不存在
                                    isok = false;
                                    strMessage.AppendLine("  错误消息:" + column + "列不存在\r\n");
                                }
                            }
                            if (isok)
                            {
                                foreach (DataRow dr in dt.Rows)
                                {
                                    icount++;
                                    string fphm = "";//发票号码
                                    string fprq = "";//发票日期
                                    string companyCode = "";//公司编号

                                    bool isContinue = true;
                                    bool isNullRow = true; //是否是一个空行,如果是空行则不执行
                                    foreach (var field in filterColumns)
                                    {
                                        if (dr[field].ToString().Trim() == "")
                                        {
                                            isContinue = false;
                                        }
                                        else
                                        {
                                            var column = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/FilterMapping/" + field);
                                            if (column == "number")
                                            {
                                                fphm = dr[field].ToString().Trim();
                                            }
                                            else if (column == "date")
                                            {
                                                fprq = dr[field].ToString().Trim();
                                            }
                                            else if (column == "CompanyCode")
                                            {
                                                companyCode = dr[field].ToString().Trim();
                                            }
                                            isNullRow = false;
                                        }

                                    }

                                    if (isNullRow)
                                    {
                                        strMessage.AppendLine(flowname + "   第" + icount.ToString().PadLeft(4, ' ') + "行是一个空行");
                                        continue;
                                    }
                                    if (!isContinue && fphm.Length < 15)
                                    {
                                        isok = false;
                                        strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, "值不能为空"));
                                        continue;
                                    }

                                    #region    发票号码或者发票代码都不为空

                                    var filters = new List<Filter>() {
                                               new Filter(){
                                                Field="businesstypeid",
                                                Method="eq",
                                                 Mode="eq",
                                                 Values=new string[]{ businessTypeId }
                                               },
                                        };
                                    foreach (var field in filterColumns)
                                    {
                                        var column = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/FilterMapping/" + field);
                                        var mode = "eq";
                                        if (column == "number")
                                        {
                                            mode = "like";
                                        }
                                        string value = dr[field].ToString().Trim();
                                        if (!string.IsNullOrEmpty(value))
                                        {
                                            filters.Add(new Filter()
                                            {
                                                Field = column,
                                                Method = mode,
                                                Mode = mode,
                                                Values = new string[] { dr[field].ToString().Trim() }
                                            });
                                        }
                                    }
                                    ///查询发票是否存在
                                    var invoiceResult = await objectQuery.GetList<InvoiceInfo>(configuration["DocumentType:Invoice"],
                                                filters, new List<Sorter>(), 0, 10);

                                    bool issubmit = true;
                                    bool isComplated = true;//索赔发票是否完成
                                    if (invoiceResult.totalCount > 0)
                                    {
                                        var invoice = invoiceResult.data.First();
                                        if (invoice.StepName == StepName)
                                        {
                                            if (model != "Single")
                                            {
                                                //索赔发票判断改凭证号是否已经被返填了,如果已返填,则不在执行
                                                var keyFields = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/KeyField").Split(',').ToList();
                                                int sameCount = 0;
                                                strMessage.AppendLine($"keyFields:{string.Join(",", keyFields)}");
                                                foreach (var keyField in keyFields)
                                                {
                                                    var dbKeyfield = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/Mapping/" + keyField.Replace(" ", ""));
                                                    if (!dt.Columns.Contains(keyField))
                                                    {
                                                        ///缺失也需要继续执行
                                                        ///
                                                        strMessage.AppendLine($"{keyField}关键字段为空");
                                                        continue;
                                                    }
                                                    if (string.IsNullOrEmpty(dr[keyField].ToString()))
                                                    {
                                                        strMessage.AppendLine(keyField + "字段值不能为空");
                                                        continue;
                                                    }
                                                    var excelValue = dr[keyField].ToString();
                                                    var property = invoice.GetType().GetProperty(dbKeyfield);
                                                    if (property != null)
                                                    {
                                                        var value = property.GetValue(invoice) == null ? new List<string>()
                                                            : property.GetValue(invoice).ToString().Split(',').ToList();
                                                        strMessage.AppendLine($"{keyField}的值比对:[{excelValue}]与[{string.Join(",", value)}]");
                                                        if (value.Contains(excelValue))
                                                        {
                                                            sameCount = sameCount + 1;
                                                            strMessage.AppendLine($"该{keyField}:({excelValue})已经存在,系统中的值:({property.GetValue(invoice).ToString()})");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        strMessage.AppendLine(dbKeyfield + "数据库字段值不存在");
                                                        continue;
                                                    }
                                                }
                                                if (sameCount == keyFields.Count)
                                                {
                                                    strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, $"该凭证号已经返填"));
                                                    isok = false;
                                                    continue;
                                                }
                                            }

                                            foreach (var column in columns)
                                            {
                                                if (!dt.Columns.Contains(column))
                                                {
                                                    ///缺失也需要继续执行
                                                    continue;
                                                }
                                                if (string.IsNullOrEmpty(dr[column].ToString()) && column != "Remark")
                                                {
                                                    isok = false;
                                                    issubmit = false;
                                                    strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, column + "字段值不能为空"));
                                                    continue;
                                                }
                                                var field = EncryptionHelper.GetDataXMLByName(configXml, flowname + "/Mapping/" + column.Replace(" ", ""));
                                                if (string.IsNullOrEmpty(field))
                                                {
                                                    isok = false;
                                                    issubmit = false;
                                                    strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, column + "未配置匹配字段"));
                                                    continue;
                                                }

                                                var property = invoice.GetType().GetProperty(field);
                                                //excel数据转换到表单中
                                                if (model == "Single")
                                                {
                                                    property.SetValue(invoice, dr[column].ToString());
                                                }
                                                else
                                                {

                                                    //索赔发票的凭证返填
                                                    if (field == amountField)
                                                    {
                                                        if (string.IsNullOrEmpty(dr[column].ToString()))
                                                        {
                                                            strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, column + "金额不能为空"));
                                                            continue;
                                                        }

                                                        decimal amount = 0;
                                                        decimal newAmount = decimal.Parse(dr[column].ToString());

                                                        var total = Math.Round(amount + newAmount, 2);


                                                        if (property.GetValue(invoice) == null || property.GetValue(invoice).ToString() == "")
                                                        {
                                                            property.SetValue(invoice, total.ToString());
                                                        }
                                                        else
                                                        {
                                                            amount = decimal.Parse(property.GetValue(invoice).ToString());
                                                            total = Math.Round(amount + newAmount, 2);
                                                            property.SetValue(invoice, total.ToString());
                                                        }

                                                        //比对金额
                                                        //负数发票、外币/收据在反填时，凭证金额加总之后与客户自填的金额字段的值去比对
                                                        if (!types.Contains(invoice.type))
                                                        {
                                                            //外币
                                                            if (invoice.foreign_currency_amount > total)
                                                            {
                                                                strMessage.AppendLine($"外币金额:{invoice.foreign_currency_amount};已入账金额:{total}");
                                                                isComplated = false;//不提交流程
                                                            }
                                                        }
                                                        else if (companyList.data.Any(w => w.TaxNo == invoice.seller_tax_id))
                                                        {
                                                            //负数发票
                                                            if (invoice.SPJE > total)
                                                            {
                                                                strMessage.AppendLine($"负数发票金额:{invoice.SPJE};已入账金额:{total}");
                                                                isComplated = false;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (decimal.Parse(invoice.total) > total)
                                                            {
                                                                strMessage.AppendLine($"发票金额:{invoice.total};已入账金额:{total}");
                                                                isComplated = false;//不提交流程
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (property == null)
                                                        {
                                                            strMessage.AppendLine("[" + field + "]字段不存在\r\n");
                                                        }
                                                        else
                                                        {
                                                            var values = property.GetValue(invoice) == null ||
                                                                property.GetValue(invoice).ToString() == "" ? new List<string>()
                                                                : property.GetValue(invoice).ToString().Split(',').ToList();
                                                            values.Add(dr[column].ToString());
                                                            property.SetValue(invoice, string.Join(",", values));
                                                        }
                                                    }
                                                }
                                            }
                                            if (issubmit)
                                            {
                                                var documentTask = await documentWorkflowService.GetLasterActiveDocumentTask(invoice.MstId);
                                                if (documentTask == null)
                                                {
                                                    strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, "没有找到文档工作流程任务"));
                                                    continue;
                                                }
                                                invoice.BackfillDate = DateTime.Now.ToString();
                                                await objectQuery.CreateOrUpdate(configuration["DocumentType:Invoice"], new Guid(invoice.MstId), invoice);

                                                if (isComplated)
                                                {
                                                    var message = new WorkflowMQContent()
                                                    {
                                                        RequestId = Guid.NewGuid().ToString("N").ToUpper(),
                                                        MessageType = documentTask.FileType,
                                                        DocumentID = documentTask.DocumentIdentity,
                                                        DocumentType = documentTask.DocumentTypeIdentity,
                                                        MstId = documentTask.MstId,
                                                        BusinessType = documentTask.BusinessType,
                                                        DocumentTaskID = documentTask.Id.ToString(),
                                                        DocumentTaskActionID = documentTask.CurrentTaskActionIdentity,
                                                        ScanUser = documentTask.ScanUser,
                                                        OwnUser = documentTask.OwnUser,
                                                        Name = documentTask.TriggerName,
                                                        Code = documentTask.TriggerCode,
                                                        WorkflowId = documentTask.WorkflowIdentity,
                                                        TriggerMessage = "",
                                                        TriggerStatus = true,
                                                        Data = null
                                                    };

                                                    string publishMessage = JsonConvert.SerializeObject(message);

                                                    DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(publishMessage);
                                                    eventBus.Publish(publishEventData);
                                                    strMessage.AppendLine(string.Format("{0} 第{1}行   发票号码:{2}    发票日期:{3}    公司编号:{4}    消息:{5}", flowname, icount, fphm, fprq, companyCode, "提交成功"));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (string.IsNullOrEmpty(invoice.dlpzhm))
                                            {
                                                isok = false;
                                                strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, "没找到相应的流程"));
                                            }
                                            else
                                            {
                                                //已经返填
                                            }
                                        }
                                    }
                                    else
                                    {
                                        strErrorMessage.AppendLine(string.Format(strErrorMessageFormatter, flowname, icount, fphm, fprq, companyCode, "没找到相应的流程"));
                                        isok = false;
                                    }
                                    #endregion
                                }
                            }
                            else
                            {
                                strErrorMessage.AppendLine($"文件名称:{fi.Name} 错误消息:字段不全,缺失字段:{string.Join(',', missingField)}");
                            }
                        }

                        string SubFname = "finish";

                        if (!isok)
                        {
                            SubFname = "failed";
                            flag = false;
                            strMessage.Append("处理失败");
                        }
                        if (!Directory.Exists(fi.Directory.FullName + "/" + SubFname + "/"))
                        {
                            Directory.CreateDirectory(fi.Directory.FullName + "/" + SubFname + "/");
                        }

                        if (File.Exists(fi.Directory.FullName + "/" + SubFname + "/" + fi.Name))
                        {
                            File.Delete(fi.Directory.FullName + "/" + SubFname + "/" + fi.Name);
                        }
                        File.Move(fi.FullName, fi.Directory.FullName + "/" + SubFname + "/" + fi.Name.Split('.')[0] + "_" + DateFileName + fi.Extension);
                        strMessage.AppendLine("处理成功");
                    }
                    catch (Exception ex)
                    {
                        flag = false;
                        strMessage.AppendLine(ex.Message);
                        strMessage.AppendLine(ex.StackTrace);
                    }
                    finally
                    {
                        if (!Directory.Exists(fi.Directory.FullName + "/failed/"))
                        {
                            Directory.CreateDirectory(fi.Directory.FullName + "/failed/");
                        }
                        if (strErrorMessage.ToString().Length > 0)
                        {
                            string tempFile = fi.Name.Split('.')[0] + "_" + DateFileName + ".txt";
                            File.WriteAllText(tempFile, strErrorMessage.ToString());
                            //先写本地，在拷贝过去，避免无权限写入
                            File.Copy(tempFile, fi.Directory.FullName + "/failed/" + tempFile, true);
                        }
                    }

                    strMessage.AppendLine($"结束>:{flowname}");
                }
            }
            strMessage.AppendLine("==========完成=========================================================");
            if (flag)
            {
                _logger.LogInformation(strMessage.ToString());
            }
            else
            {
                _logger.LogError(strMessage.ToString());
            }
        }

        public List<FileInfo> GetReadExcelInfo(string flowName, StringBuilder resMessage)
        {
            List<FileInfo> FileList = new List<FileInfo>();

            string folderName = EncryptionHelper.GetDataXMLByName(configXml, flowName + "/ReadFlord");

            if (!Directory.Exists(folderName))
            {
                Directory.CreateDirectory(folderName);
            }
            resMessage.AppendLine("文件路径:" + folderName);
            DirectoryInfo TheFolder = new DirectoryInfo(folderName);
            //遍历文件
            foreach (FileInfo NextFile in TheFolder.GetFiles())
            {
                if (NextFile.Extension.ToLower() == ".xlsx" || NextFile.Extension.ToLower() == ".xls")
                {
                    FileList.Add(NextFile);
                }

            }
            return FileList;
        }
    }
}
