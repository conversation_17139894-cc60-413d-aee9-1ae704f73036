﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class ResponseVoucherModel
    {
        public Guid MstId { get; set; }

        /// <summary>
        /// 编制单位
        /// </summary>
        public string PreparationUnit { get; set; }

        /// <summary>
        /// 核算单位
        /// </summary>
        public string AccountingUnit { get; set; }

        /// <summary>
        /// 公司编码
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 会计年度
        /// </summary>
        public int AccountingYear { get; set; }

        /// <summary>
        /// 会计期间
        /// </summary>
        public int AccountingMonth { get; set; }

        /// <summary>
        /// 凭证号
        /// </summary>
        public string VoucherNumber { get; set; }

        /// <summary>
        /// 记账
        /// </summary>
        public string Bookkeeping { get; set; }

        /// <summary>
        /// 复核
        /// </summary>
        public string Reviewer { get; set; }

        /// <summary>
        /// 制单人
        /// </summary>
        public string Maker { get; set; }

        /// <summary>
        /// 明细列表
        /// </summary>
        public List<ResponseVoucherDetailModel> details { get; set; }
    }

    /// <summary>
    /// 凭证明细
    /// </summary>
    public class ResponseVoucherDetailModel 
    {
        /// <summary>
        /// 摘要
        /// </summary>
        public string Summary { get; set; }

        /// <summary>
        /// 会计科目
        /// </summary>
        public string AccountingSubjects { get; set; }

        /// <summary>
        /// 借方
        /// </summary>
        public string Debit { get; set; }

        /// <summary>
        /// 贷方
        /// </summary>
        public string Lender { get; set; }
    }
}