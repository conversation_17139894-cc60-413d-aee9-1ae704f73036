﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Text;
using System.Text.Json;

namespace DocPark.Commons
{
    public static class ExtendObjectMehtod
    {
        public static bool IsNullOrWhiteSpace(this string str)
        {
            return string.IsNullOrWhiteSpace(str);
        }

        public static bool IsNullOrEmpty(this string str)
        {
            return string.IsNullOrEmpty(str);
        }

        /// <summary>
        /// Converts given object to a value type using <see cref="Convert.ChangeType(object,System.Type)"/> method.
        /// </summary>
        /// <param name="obj">Object to be converted</param>
        /// <typeparam name="T">Type of the target object</typeparam>
        /// <returns>Converted object</returns>
        public static T To<T>(this object obj)
            where T : struct
        {
            return (T)Convert.ChangeType(obj, typeof(T), CultureInfo.InvariantCulture);
        }

        public static string getString(this JObject jObject, string key)
        {
            try
            {
                return jObject.ContainsKey(key) ? jObject[key].ToString() : "";
            }
            catch
            {
                return "";
            }
        }

        public static string getString(this Dictionary<string,object> jObject, string key)
        {
            try
            {
                return jObject.ContainsKey(key) && jObject[key]!=null ? jObject[key].ToString() : "";
            }
            catch
            {
                return "";
            }
        }


        public static int getInt(this JObject jObject, string key, int defaultValue = 0)
        {
            try
            {
                string value = jObject.getString(key);
                return string.IsNullOrEmpty(value) ? defaultValue : int.Parse(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        public static int getInt(this Dictionary<string, object> jObject, string key, int defaultValue = 0)
        {
            try
            {
                string value = jObject.getString(key);
                return string.IsNullOrEmpty(value) ? defaultValue : int.Parse(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        public static string ToJsonString(this Dictionary<string, object> jObject)
        {
            try
            {
                var options = new JsonSerializerOptions { WriteIndented = false };
                return System.Text.Json.JsonSerializer.Serialize(jObject, options);
            }
            catch
            {
                return "";
            }
        }
        public static JObject getJObject(this JObject jObject, string key)
        {
            try
            {
                return (JObject)jObject[key];
            }
            catch
            {
                return null;
            }
        }

        public static JObject getJObject(this Dictionary<string,object> jObject, string key)
        {
            try
            {
                return JsonConvert.DeserializeObject<JObject>(jObject[key].ToString());
            }
            catch
            {
                return null;
            }
        }

        public static JArray getJArray(this JObject jObject, string key)
        {
            try
            {
                return (JArray)jObject[key];
            }
            catch
            {
                return null;
            }
        }

        public static JArray getJArray(this Dictionary<string,object> jObject, string key)
        {
            try
            {
                return JsonConvert.DeserializeObject<JArray>(jObject[key].ToString());
            }
            catch
            {
                return null;
            }
        }
        public static string getStrDateTime(this JObject jObject, string key, string format = "yyyy-MM-dd")
        {
            try
            {
                string value = jObject.getString(key);
                if (string.IsNullOrEmpty(value))
                    return "";
                if (value.Contains("年"))
                {
                    string _format = "yyyy年MM月dd日";
                    DateTime date = DateTime.ParseExact(value, _format, CultureInfo.CreateSpecificCulture("zh-CN"));
                    return date.ToString(format);
                }
                else
                {
                    DateTime dtStart = new DateTime(1970, 1, 1, 0, 0, 0);
                    long lTime = long.Parse(value + "0000000");
                    TimeSpan toNow = new TimeSpan(lTime);
                    return dtStart.Add(toNow).ToString(format);
                }
            }
            catch
            {
                return "";
            }
        }

        public static string getStrWXAmount(this JObject jObject, string key)
        {
            try
            {
                string value = jObject.getString(key);
                if (string.IsNullOrEmpty(value))
                    return "0.00";
                if (value.Length >= 3)
                {
                    return value.Insert(value.Length - 2, ".");
                }
                return value;
            }
            catch
            {
                return "0.00";
            }
        }
    }
}
