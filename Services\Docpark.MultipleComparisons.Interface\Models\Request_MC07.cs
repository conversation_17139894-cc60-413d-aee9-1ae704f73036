﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_MC07
    {
        /// <summary>
        /// 发票影像标识集合
        /// </summary>
        /// <remarks>
        /// 发票报销时从票池中选择的发票标识
        /// </remarks>
        [Required(ErrorMessage = "发票影像标识集合必填")]
        public List<string> image_mstids { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        /// <remarks>
        /// 发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销
        /// </remarks>
        [Required(ErrorMessage = "发票状态必填")]
        public string status { get; set; }
    }
}
