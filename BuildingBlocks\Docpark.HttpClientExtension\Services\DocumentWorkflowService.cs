﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.HttpClientExtions;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class DocumentWorkflowService : IDocumentWorkflowService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IDocparkHttpClient docparkHttpClient;
        private readonly IDGrpc grpc;
        private readonly IConfiguration configuration;
        private readonly IHttpContextAccessor httpContextAccessor;

        private string BaseAddress
        {
            get; set;
        }

        public DocumentWorkflowService(IHttpClientFactory httpClientFactory, IDocparkHttpClient docparkHttpClient, IDGrpc grpc, IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor) {
            this.httpClientFactory = httpClientFactory;
            this.docparkHttpClient = docparkHttpClient;
            this.grpc = grpc;
            this.configuration = configuration;
            this.httpContextAccessor = httpContextAccessor;
            BaseAddress = grpc.GetStringAsync(ConstSettings.DocumentWorkflowApi).Result;
        }
        public async Task<DocumentTask> GetLasterActiveDocumentTask(string mstId)
        {
            try
            {
                var api = $"api/DocumentTask/laster-active-task?mstId=" + mstId;

                using (var httpClient = httpClientFactory.CreateClient())
                {
                    httpClient.AddAuthenticate(grpc, configuration, httpContextAccessor);
                    string strResult = await httpClient.GetStringAsync(api.ToUrl(BaseAddress));

                    var result = JsonConvert.DeserializeObject<DocumentTask>(strResult);

                    return result;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
}
