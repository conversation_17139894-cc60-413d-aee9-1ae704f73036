﻿using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IDocparkWechatService
    {
        /// <summary>
        /// 解锁微信卡包发票状态
        /// </summary>
        /// <param name="card_id"> 发票卡券的 card_id</param>
        /// <param name="encrypt_code">发票卡券的加密 code ，和 card_id 共同构成一张发票卡券的唯一标识</param>
        /// <param name="type">微信查询数据的类型, qy[业务微信]; gzh[公众号]; weapp[微信小程序]</param>
        /// <param name="reimburse_status">发票报销状态, INVOICE_REIMBURSE_INIT[发票初始状态，未锁定]; INVOICE_REIMBURSE_LOCK[发票已锁定]</param>
        /// <returns></returns>
        Task<UnLockWechatInvoiceStatusResultDto> UnLockWechatInvoiceStatus(string card_id, string encrypt_code, string type = "weapp", string reimburse_status = "INVOICE_REIMBURSE_INIT");
    }
}
