﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.HttpClientExtension.IServices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Docpark.HttpClientExtension.Services
{
    public class ComHttpClient : IComHttpClient
    {
        private readonly ILogger logger;
        private readonly IConfiguration configuration;
        private readonly IDGrpc iDGrpc;

        private readonly IHttpClientFactory _httpClientFactory;
        public ComHttpClient(ILogger<ComHttpClient> logger, IConfiguration configuration, IDGrpc iDGrpc, IHttpClientFactory httpClientFactory)
        {
            this.logger = logger;
            this.configuration = configuration;
            this.iDGrpc = iDGrpc;
            _httpClientFactory = httpClientFactory;
        }
        private string BaseServer
        {
            get
            {
                return this.GetApiHost("App.Api.Default.Host");
                //return "https://bate.ele-arch.com/";
            }
        }
        private string BaseServerFTP
        {
            get
            {
                return this.GetApiHost("App.Api.FTP.Host");
            }
        }

        private string ArchivesBaseUrl
        {
            get
            {
                return this.GetApiHost("Reimbursement.ArchivesBaseUrl");
            }
        }
        private string BaseDocumentServerHost
        {
            get
            {
                return this.GetApiHost("App.Api.DocumentService.Host");
            }
        }

        private static string token { get; set; }
        private static int expireInSeconds { get; set; }
        private static DateTime getTokenTime { get; set; }

        /// <summary>
        /// 生成Token
        /// </summary>
        private void Authenticate()
        {
            var data = new Dictionary<string, string>();
            data["UserNameOrEmailAddress"] = iDGrpc.GetStringAsync(ConstSettings.SystemAccount).Result;
            data["Password"] = iDGrpc.GetStringAsync(ConstSettings.SystemPassword).Result;
            //data["UserNameOrEmailAddress"] = "admin";
            //data["Password"] = "123qwe";
            data["RememberClient"] = "false";

            //string strResult = Post(BaseServer + "api/TokenAuth/Authenticate", data);
            var client = this._httpClientFactory.CreateClient();
            var hContent = new StringContent(JsonConvert.SerializeObject(data));
            hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            using var response = client.PostAsync(BaseServer + "api/TokenAuth/Authenticate", hContent).Result;
            var strResult = response.Content.ReadAsStringAsync().Result;

            if (!string.IsNullOrEmpty(strResult))
            {
                var payload = JsonConvert.DeserializeObject<JObject>(strResult);
                if (payload != null)
                {
                    token = ((JObject)((JObject)payload)["result"])["accessToken"].ToString();
                    expireInSeconds = this.GetInt(((JObject)((JObject)payload)["result"])["expireInSeconds"].ToString());
                    getTokenTime = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// 获取Token
        /// </summary>
        public Dictionary<string, object> GetApiToken()
        {
            this.Authenticate();
            return new Dictionary<string, object>()
            {
                ["token"] = token,
                ["expireInSeconds"] = expireInSeconds,
                ["getTokenTime"] = getTokenTime,
            };
        }

        /// <summary>
        /// 获取指定的ApiHost地址
        /// </summary>
        /// <param name="apiName">ApiHost名称</param>
        /// <returns>返回ApiHost地址</returns>
        public string GetApiHost(string apiName)
        {
            string apiHost = "";
            try
            {
                if (apiName == "App.Api.Default.Host" && !string.IsNullOrEmpty(configuration["IdentityUrl"]))
                {
                    return configuration["IdentityUrl"];
                }
                apiHost = iDGrpc.GetStringAsync(apiName).Result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return apiHost;
        }

        public async Task<ReimbursementConfigManagementDto> GetReimbursementConfig()
        {
            var resultData = new ReimbursementConfigManagementDto();
            try
            {
                return new ReimbursementConfigManagementDto()
                {
                    ArchivesBaseUrl = await iDGrpc.GetStringAsync("Reimbursement.ArchivesBaseUrl"),
                    PdfApiBaseUrl = await iDGrpc.GetStringAsync("Reimbursement.PdfApiBaseUrl"),
                    ThirdticketholderUrl = await iDGrpc.GetStringAsync("Reimbursement.ThirdticketholderUrl"),
                    ThirdticketpriviewUrl = await iDGrpc.GetStringAsync("Reimbursement.ThirdticketpriviewUrl")
                };

                //string baseHost = this.configuration["IdentityUrl"];
                //string url = $"{baseHost.TrimEnd('/')}/api/services/app/HostSettings/GetAllSettings";
                //HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
                //client.DefaultRequestHeaders.Accept.TryParseAdd("application/json");
                //using (var response = await client.GetAsync(url))
                //{
                //    response.EnsureSuccessStatusCode();//用来抛异常的
                //    string result = response.Content.ReadAsStringAsync().Result;

                //    var setting = JsonConvert.DeserializeObject<ApiHostModel>(result);
                //    if (setting.result != null)
                //    {
                //        var settingResult = setting.result;
                //        if (settingResult.reimbursementConfigManagement != null)
                //            resultData = settingResult.reimbursementConfigManagement;
                //    }

                //}
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return resultData;
        }


        /// <summary>
        /// 获取AbpUserConfiguration的配置数据
        /// </summary>
        /// <returns>返回abpConfig</returns>
        public JObject GetAbpConfig()
        {
            JObject abpConfig = null;
            try
            {
                string baseHost = this.configuration["IdentityUrl"];
                string url = $"{baseHost.TrimEnd('/')}/AbpUserConfiguration/GetAll";
                HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
                client.DefaultRequestHeaders.Accept.TryParseAdd("application/json");
                using (var response = client.GetAsync(url).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    string result = response.Content.ReadAsStringAsync().Result;

                    abpConfig = JsonConvert.DeserializeObject<JObject>(result);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return abpConfig;
        }

        /// <summary>
        /// 添加token
        /// </summary>
        /// <param name="client"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        private HttpClient AddAuthenticate(HttpClient client, string url)
        {
            if (string.IsNullOrEmpty(token) && !url.Contains("TokenAuth"))
            {
                Authenticate();
            }
            if (!string.IsNullOrEmpty(token))
            {
                if ((DateTime.Now - getTokenTime).TotalSeconds > expireInSeconds - 200 && !url.Contains("TokenAuth"))
                {
                    Authenticate();
                }
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            return client;
        }
        /// <summary>
        /// 使用get方法异步请求
        /// </summary>
        /// <param name="url">目标链接</param>
        /// <returns>返回的字符串</returns>
        public string Get(string url)
        {
            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            client.DefaultRequestHeaders.Accept.TryParseAdd("application/json");
            client = AddAuthenticate(client, url);
            using (var response = client.GetAsync(url).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return response.Content.ReadAsStringAsync().Result;
            }
        }
        /// <summary>
        /// post json
        /// </summary>
        /// <param name="url"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public string Post(string url, object context)
        {
            try
            {
                HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
                var hContent = new StringContent(JsonConvert.SerializeObject(context));
                hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                client = AddAuthenticate(client, url);
                using (var response = client.PostAsync(url, hContent).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return response.Content.ReadAsStringAsync().Result;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("post请求异常：" + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// post json
        /// </summary>
        /// <param name="url"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public string PostByText(string url, object context)
        {
            try
            {
                HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
                var hContent = new StringContent((string)context);
                hContent.Headers.ContentType = new MediaTypeHeaderValue("text/plain");
                client = AddAuthenticate(client, url);
                using (var response = client.PostAsync(url, hContent).Result)
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return response.Content.ReadAsStringAsync().Result;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("post请求异常：" + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// post json by token
        /// </summary>
        /// <param name="url"></param>
        /// <param name="apiName"></param>
        /// <param name="token"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public string PostJson(string url, string apiName, object model, string token = "")
        {
            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            var hContent = new StringContent(JsonConvert.SerializeObject(model));
            hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            if (!string.IsNullOrEmpty(token))
                client.DefaultRequestHeaders.Add("token", token);

            using (HttpResponseMessage response = client.PostAsync(url + "/" + apiName, hContent).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return response.Content.ReadAsStringAsync().Result;
            }
        }
        /// <summary>
        /// post json by token
        /// </summary>
        /// <param name="url"></param>
        /// <param name="token"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public string PostJson(string url, object model, string token = "")
        {
            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            var hContent = new StringContent(JsonConvert.SerializeObject(model));
            hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            if (!string.IsNullOrEmpty(token))
                client.DefaultRequestHeaders.Add("token", token);

            using (HttpResponseMessage response = client.PostAsync(url, hContent).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return response.Content.ReadAsStringAsync().Result;
            }
        }
        /// <summary>
        /// post json by token
        /// </summary>
        /// <returns></returns>
        public string PostJson(string url, object model, string tokenHeadersName, string token = "")
        {
            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            var hContent = new StringContent(JsonConvert.SerializeObject(model));
            hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            if (!string.IsNullOrEmpty(token))
                client.DefaultRequestHeaders.Add(tokenHeadersName, token);

            using (HttpResponseMessage response = client.PostAsync(url, hContent).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return response.Content.ReadAsStringAsync().Result;
            }
        }

        /// <summary>
        /// post文件
        /// </summary>
        /// <param name="url"></param>
        /// <param name="dic"></param>
        /// <param name="bytes"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public string PostFile(string url, Dictionary<string, object> dic, byte[] fileBytes, string fileName = "image_File")
        {
            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            client.DefaultRequestHeaders.Add("accept", "application/json");
            client = AddAuthenticate(client, url);
            //初始化参数
            var content = new MultipartFormDataContent();
            string boundary = string.Format("--{0}", DateTime.Now.Ticks.ToString("x"));
            content.Headers.Add("ContentType", $"multipart/form-data, boundary={boundary}");
            content.Add(new ByteArrayContent(fileBytes), fileName, fileName);

            foreach (var keyValuePair in dic)
            {
                content.Add(new StringContent(keyValuePair.Value.ToString()), keyValuePair.Key);
            }

            using (HttpResponseMessage response = client.PostAsync(url, content).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return response.Content.ReadAsStringAsync().Result;
            }
        }

        /// <summary>
        /// 获取文件消息
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        public string GetDocumentURL(string docId)
        {
            string strResult = Get($"{BaseServerFTP}api/FTP/Viwer/GetDocumentURL/{docId}/1");
            JObject jsonResult = JsonConvert.DeserializeObject<JObject>(strResult);
            return jsonResult["data"].ToString();
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<(string documentId, string fileDownloadUrl, string errMsg)> UploadFile(KeyValuePair<string, byte[]> file)
        {
            var documentId = "";
            var imgFileUrl = "";
            var errMsg = "";
            try
            {
                var ftpHost = BaseServerFTP;
                var url = $"{ftpHost.TrimEnd('/')}/api/ftp/upload/add/file";
                var formData = new KeyValuePair<string, KeyValuePair<string, byte[]>>(file.Key, new KeyValuePair<string, byte[]>("newfile", file.Value));

                var httpResponseMessage = await this.UploadFile(url, formData);
                if (String.IsNullOrWhiteSpace(httpResponseMessage))
                {
                    errMsg = "服务连接异常!";
                }
                else
                {
                    var result = JsonConvert.DeserializeObject<ResultFTPDto>(httpResponseMessage);
                    if (result.State)
                    {
                        documentId = result.Data.DocumentId;

                        imgFileUrl = $"{ftpHost.TrimEnd('/')}/api/ftp/download/file?documentId={documentId}";
                    }
                    else
                    {
                        errMsg = result.Message;
                    }
                }
            }
            catch (Exception ex)
            {
                errMsg = ex.Message;
            }

            return (documentId, imgFileUrl, errMsg);
        }

        /// <summary>
        /// 获取第三方组织架构信息数据
        /// </summary>
        /// <param name="url">请求url</param>
        /// <param name="requestData">请求参数</param>
        /// <returns>返回第三方组织架构信息数据</returns>
        public string GetThirdpartyOrganization(string url, Dictionary<string, object> requestData)
        {
            return this.Post(url, requestData);
        }
        /// <summary>
        /// 获取组织架构树
        /// </summary>
        /// <returns></returns>
        public string GetOrganizationTreeData()
        {
            string url = BaseServer + "api/services/app/OrganizationUnit/GetTreeData?IsGroup=false&IsAll=false";
            return this.Get(url);
        }
        /// <summary>
        /// 获取用户数据集
        /// </summary>
        /// <returns></returns>
        public string GetUsers()
        {
            string url = BaseServer + "api/services/app/User/GetUsers";
            return this.Get(url);
        }
        /// <summary>
        /// 同步第三方机构数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <returns></returns>
        public string SyncThirdpartyOrganization(List<Model_ThirdpartyCreateOrgUnit> requestData)
        {
            string url = BaseServer + "api/services/app/OrganizationUnit/BatchCreateThirdpartyOrganizationUnit";
            return this.Post(url, requestData);
        }
        /// <summary>
        /// 同步第三方人员数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <returns></returns>
        public string SyncThirdpartyUser(List<Model_ThirdpartyCreateUser> requestData)
        {
            var content = JsonConvert.SerializeObject(requestData);
            string url = BaseServer + "api/services/app/User/BatchCreateThirdpartyUser";
            return this.Post(url, requestData);
        }

        /// <summary>
        /// 发送进项税统计数据到统计报表
        /// </summary>
        /// <param name="mstIds"></param>
        /// <returns></returns>
        public string SendInputTaxDataToReport(Dictionary<string, object> mstIds)
        {
            string url = BaseDocumentServerHost + "invoice/document/InsertIncomeTax";
            return this.Post(url, mstIds);
        }

        /// <summary>
        /// 获取API请求接口返回的数据
        /// </summary>
        /// <param name="url">URL连接</param>
        /// <param name="formData">上传文件流</param>
        /// <param name="parmas">参数，默认空</param>
        /// <returns></returns>
        private async Task<String> UploadFile(String url, KeyValuePair<string, KeyValuePair<string, byte[]>> formData, String parmas = null, List<KeyValuePair<string, string>> headers = null)
        {
            using (var client = new HttpClient())
            {
                try
                {
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    if (string.IsNullOrEmpty(token))
                    {
                        Authenticate();
                    }
                    if (!string.IsNullOrEmpty(token))
                    {
                        if ((DateTime.Now - getTokenTime).TotalSeconds > expireInSeconds - 200 && !url.Contains("TokenAuth"))
                        {
                            Authenticate();
                        }
                        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
                    }
                    if (headers != null && headers.Any())
                    {
                        foreach (var keyAndValue in headers)
                        {
                            client.DefaultRequestHeaders.Add(keyAndValue.Key, keyAndValue.Value);
                        }
                    }
                    //初始化参数
                    var content = new MultipartFormDataContent();
                    string boundary = string.Format("--{0}", DateTime.Now.Ticks.ToString("x"));
                    content.Headers.Add("ContentType", $"multipart/form-data, boundary={boundary}");
                    content.Add(new ByteArrayContent(formData.Value.Value), formData.Value.Key, formData.Key);
                    if (parmas != null)
                    {
                        content.Add(new StringContent(parmas));
                    }
                    HttpResponseMessage httpResponseMessage = await client.PostAsync(url, content).ConfigureAwait(false);
                    var result = await httpResponseMessage.Content.ReadAsStringAsync().ConfigureAwait(false);
                    return result;
                }
                catch (Exception ex)
                {
                    return string.Empty;
                }
            }
        }

        private int GetInt(string value)
        {
            try
            {
                return Convert.ToInt32(value);
            }
            catch
            {
                return 0;
            }
        }


        /// <summary>
        /// 使用get下载文件
        /// </summary>
        /// <param name="docId">文档连接</param>
        /// <returns>返回文件流</returns>
        public (byte[], MediaTypeHeaderValue) DowLoadFile(string docId)
        {
            string url = $"{BaseServerFTP}api/ftp/download/file?documentId={docId}";

            HttpClient client = new HttpClient(new HttpClientHandler() { UseCookies = false });
            client = AddAuthenticate(client, url);
            using (var response = client.GetAsync(url).Result)
            {
                response.EnsureSuccessStatusCode();//用来抛异常的
                return (response.Content.ReadAsByteArrayAsync().Result, response.Content.Headers.ContentType);
            }
        }


        #region 异步
        public async Task<string> PostAsync(string url, object context)
        {
            try
            {
                var client = this._httpClientFactory.CreateClient();
                var hContent = new StringContent(JsonConvert.SerializeObject(context));
                hContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                client = AddAuthenticate(client, url);
                using (var response = await client.PostAsync(url, hContent))
                {
                    response.EnsureSuccessStatusCode();//用来抛异常的
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                logger.LogError("post请求异常：" + ex.Message);
                return null;
            }
        }

        public async Task<string> SyncThirdpartyUserAsync(List<Model_ThirdpartyCreateUser> requestData)
        {
            //var content = JsonConvert.SerializeObject(requestData);
            //string url = BaseServer + "​api​/services​/app​/User​/BatchCreateThirdpartyUser";//api/services/app/User/BatchCreateThirdpartyUser
            return await this.PostAsync($"{BaseServer}api/services/app/User/BatchCreateThirdpartyUser", requestData);
        }
        #endregion
    }
}
