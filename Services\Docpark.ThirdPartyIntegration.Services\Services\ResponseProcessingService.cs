using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 响应处理服务实现
    /// </summary>
    public class ResponseProcessingService : IResponseProcessingService
    {
        private readonly ILogger<ResponseProcessingService> _logger;
        private readonly IMongoRepository _mongoRepository;

        public ResponseProcessingService(
            ILogger<ResponseProcessingService> logger,
            IMongoRepository mongoRepository)
        {
            _logger = logger;
            _mongoRepository = mongoRepository;
        }

        /// <summary>
        /// 处理响应数据
        /// </summary>
        public async Task<ResponseProcessingResult> ProcessResponseAsync(object responseData, ResponseProcessingConfig processingConfig, string apiConfigId)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ResponseProcessingResult
            {
                OriginalData = responseData,
                Statistics = new ProcessingStatistics(),
                IsSuccess = true
            };

            try
            {
                if (processingConfig == null || !processingConfig.IsEnabled)
                {
                    result.ProcessedData = responseData;
                    return result;
                }

                _logger.LogInformation($"开始处理响应数据，API配置ID: {apiConfigId}");

                // 将数据转换为列表格式
                var dataList = ConvertToDataList(responseData);
                result.Statistics.OriginalRecordCount = dataList.Count;

                var processedData = dataList;

                // 1. 数据去重
                if (processingConfig.Deduplication?.IsEnabled == true)
                {
                    var dedupeStopwatch = Stopwatch.StartNew();
                    var originalCount = processedData.Count;
                    processedData = await DeduplicateDataAsync(processedData, processingConfig.Deduplication, apiConfigId);
                    dedupeStopwatch.Stop();
                    
                    result.Statistics.DuplicateRecordsRemoved = originalCount - processedData.Count;
                    result.Statistics.StageTimings["Deduplication"] = dedupeStopwatch.ElapsedMilliseconds;
                }

                // 2. 数据过滤
                if (processingConfig.Filter?.IsEnabled == true)
                {
                    var filterStopwatch = Stopwatch.StartNew();
                    var originalCount = processedData.Count;
                    processedData = await FilterDataAsync(processedData, processingConfig.Filter);
                    filterStopwatch.Stop();
                    
                    result.Statistics.FilteredRecordsRemoved = originalCount - processedData.Count;
                    result.Statistics.StageTimings["Filter"] = filterStopwatch.ElapsedMilliseconds;
                }

                // 3. 数据验证
                if (processingConfig.Validation?.IsEnabled == true)
                {
                    var validationStopwatch = Stopwatch.StartNew();
                    var validationResult = await ValidateDataAsync(processedData, processingConfig.Validation);
                    validationStopwatch.Stop();
                    
                    result.Statistics.ValidationFailedRecords = validationResult.InvalidRecordCount;
                    result.Statistics.StageTimings["Validation"] = validationStopwatch.ElapsedMilliseconds;

                    // 根据验证失败策略处理
                    switch (processingConfig.Validation.FailureStrategy)
                    {
                        case ValidationFailureStrategy.SkipRecord:
                            processedData = validationResult.ValidData;
                            break;
                        case ValidationFailureStrategy.StopProcessing:
                            if (!validationResult.IsValid)
                            {
                                throw new InvalidOperationException($"数据验证失败，停止处理。失败记录数: {validationResult.InvalidRecordCount}");
                            }
                            break;
                        case ValidationFailureStrategy.LogAndContinue:
                        default:
                            // 记录验证错误但继续处理
                            foreach (var error in validationResult.ValidationErrors)
                            {
                                result.Warnings.Add($"验证失败: {error.FieldPath} - {error.ErrorMessage}");
                            }
                            break;
                    }
                }

                // 4. 数据聚合
                if (processingConfig.Aggregation?.IsEnabled == true)
                {
                    var aggregationStopwatch = Stopwatch.StartNew();
                    processedData = await AggregateDataAsync(processedData, processingConfig.Aggregation);
                    aggregationStopwatch.Stop();
                    
                    result.Statistics.StageTimings["Aggregation"] = aggregationStopwatch.ElapsedMilliseconds;
                }

                result.ProcessedData = processedData;
                result.Statistics.ProcessedRecordCount = processedData.Count;

                stopwatch.Stop();
                result.Statistics.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

                _logger.LogInformation($"响应数据处理完成，原始记录数: {result.Statistics.OriginalRecordCount}, 处理后记录数: {result.Statistics.ProcessedRecordCount}, 耗时: {result.Statistics.ProcessingTimeMs}ms");
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add(new ProcessingError
                {
                    ErrorType = ProcessingErrorType.Unknown,
                    Message = ex.Message,
                    ExceptionDetails = ex.ToString()
                });

                _logger.LogError(ex, $"响应数据处理失败，API配置ID: {apiConfigId}");
            }

            return result;
        }

        /// <summary>
        /// 数据去重
        /// </summary>
        public async Task<List<object>> DeduplicateDataAsync(List<object> data, DeduplicationConfig deduplicationConfig, string apiConfigId)
        {
            if (data == null || !data.Any() || deduplicationConfig == null || !deduplicationConfig.IsEnabled)
            {
                return data;
            }

            try
            {
                var result = new List<object>();
                var seenHashes = new HashSet<string>();

                foreach (var item in data)
                {
                    string hash;
                    
                    switch (deduplicationConfig.Strategy)
                    {
                        case DeduplicationStrategy.HashBased:
                            hash = await ComputeDataHashAsync(item, deduplicationConfig.HashAlgorithm);
                            break;
                        case DeduplicationStrategy.FieldBased:
                            hash = ComputeFieldBasedHash(item, deduplicationConfig.KeyFields);
                            break;
                        case DeduplicationStrategy.ContentBased:
                            hash = await ComputeDataHashAsync(item, deduplicationConfig.HashAlgorithm);
                            break;
                        default:
                            hash = await ComputeDataHashAsync(item, deduplicationConfig.HashAlgorithm);
                            break;
                    }

                    // 检查是否在历史数据中重复
                    var isDuplicate = await IsDataDuplicateAsync(hash, apiConfigId, deduplicationConfig.ScopeDays);
                    
                    if (!isDuplicate && !seenHashes.Contains(hash))
                    {
                        result.Add(item);
                        seenHashes.Add(hash);
                        
                        // 保存哈希值到数据库
                        await SaveDataHashAsync(hash, apiConfigId);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据去重处理失败");
                return data; // 返回原始数据
            }
        }

        /// <summary>
        /// 数据过滤
        /// </summary>
        public async Task<List<object>> FilterDataAsync(List<object> data, DataFilterConfig filterConfig)
        {
            if (data == null || !data.Any() || filterConfig == null || !filterConfig.IsEnabled)
            {
                return data;
            }

            try
            {
                var result = new List<object>();

                foreach (var item in data)
                {
                    var itemJson = JToken.FromObject(item);
                    bool passesFilter = EvaluateFilterConditions(itemJson, filterConfig.Conditions, filterConfig.LogicalOperator);
                    
                    if (passesFilter)
                    {
                        result.Add(item);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据过滤处理失败");
                return data; // 返回原始数据
            }
        }

        /// <summary>
        /// 数据聚合
        /// </summary>
        public async Task<List<object>> AggregateDataAsync(List<object> data, DataAggregationConfig aggregationConfig)
        {
            if (data == null || !data.Any() || aggregationConfig == null || !aggregationConfig.IsEnabled)
            {
                return data;
            }

            try
            {
                // 转换为JToken以便处理
                var jsonData = data.Select(JToken.FromObject).ToList();

                // 按分组字段分组
                var groups = GroupDataByFields(jsonData, aggregationConfig.GroupByFields);

                var result = new List<object>();

                foreach (var group in groups)
                {
                    var aggregatedItem = new Dictionary<string, object>();

                    // 添加分组字段
                    foreach (var groupField in aggregationConfig.GroupByFields)
                    {
                        var value = group.First().SelectToken(groupField)?.ToObject<object>();
                        aggregatedItem[groupField] = value;
                    }

                    // 执行聚合操作
                    foreach (var operation in aggregationConfig.Operations)
                    {
                        var aggregatedValue = PerformAggregation(group, operation);
                        aggregatedItem[operation.TargetField] = aggregatedValue;
                    }

                    result.Add(aggregatedItem);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据聚合处理失败");
                return data; // 返回原始数据
            }
        }

        /// <summary>
        /// 数据验证
        /// </summary>
        public async Task<DataValidationResult> ValidateDataAsync(List<object> data, DataValidationConfig validationConfig)
        {
            var result = new DataValidationResult
            {
                IsValid = true
            };

            if (data == null || !data.Any() || validationConfig == null || !validationConfig.IsEnabled)
            {
                result.ValidData = data ?? new List<object>();
                result.ValidRecordCount = data?.Count ?? 0;
                return result;
            }

            try
            {
                for (int i = 0; i < data.Count; i++)
                {
                    var item = data[i];
                    var itemJson = JToken.FromObject(item);
                    bool isValid = true;

                    foreach (var rule in validationConfig.Rules)
                    {
                        var validationError = ValidateField(itemJson, rule, i);
                        if (validationError != null)
                        {
                            result.ValidationErrors.Add(validationError);
                            isValid = false;
                            result.IsValid = false;
                        }
                    }

                    if (isValid)
                    {
                        result.ValidData.Add(item);
                        result.ValidRecordCount++;
                    }
                    else
                    {
                        result.InvalidData.Add(item);
                        result.InvalidRecordCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据验证处理失败");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 批处理数据
        /// </summary>
        public async Task<BatchProcessingResult> ProcessInBatchesAsync<T>(List<T> data, BatchProcessingConfig batchConfig, Func<List<T>, Task<bool>> processor)
        {
            var result = new BatchProcessingResult
            {
                IsSuccess = true,
                TotalRecords = data.Count
            };

            if (data == null || !data.Any() || batchConfig == null || !batchConfig.IsEnabled)
            {
                // 如果不启用批处理，直接处理所有数据
                try
                {
                    var success = await processor(data);
                    result.TotalBatches = 1;
                    result.SuccessfulBatches = success ? 1 : 0;
                    result.FailedBatches = success ? 0 : 1;
                    result.SuccessfulRecords = success ? data.Count : 0;
                }
                catch (Exception ex)
                {
                    result.IsSuccess = false;
                    result.Errors.Add(new BatchError
                    {
                        BatchIndex = 0,
                        ErrorMessage = ex.Message,
                        ExceptionDetails = ex.ToString()
                    });
                }
                return result;
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                var batches = CreateBatches(data, batchConfig.BatchSize);
                result.TotalBatches = batches.Count;

                for (int i = 0; i < batches.Count; i++)
                {
                    try
                    {
                        var batch = batches[i];
                        var success = await processor(batch);

                        if (success)
                        {
                            result.SuccessfulBatches++;
                            result.SuccessfulRecords += batch.Count;
                        }
                        else
                        {
                            result.FailedBatches++;
                            result.Errors.Add(new BatchError
                            {
                                BatchIndex = i,
                                ErrorMessage = "批处理返回失败状态"
                            });
                        }

                        // 批次间延迟
                        if (i < batches.Count - 1 && batchConfig.IntervalMs > 0)
                        {
                            await Task.Delay(batchConfig.IntervalMs);
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedBatches++;
                        result.Errors.Add(new BatchError
                        {
                            BatchIndex = i,
                            ErrorMessage = ex.Message,
                            ExceptionDetails = ex.ToString()
                        });

                        _logger.LogError(ex, $"批处理失败，批次索引: {i}");
                    }
                }

                result.IsSuccess = result.FailedBatches == 0;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                _logger.LogError(ex, "批处理过程失败");
            }

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return result;
        }

        /// <summary>
        /// 计算数据哈希值
        /// </summary>
        public async Task<string> ComputeDataHashAsync(object data, string algorithm = "SHA256")
        {
            try
            {
                var json = JsonConvert.SerializeObject(data, Formatting.None);
                var bytes = Encoding.UTF8.GetBytes(json);

                using (var hashAlgorithm = HashAlgorithm.Create(algorithm))
                {
                    var hashBytes = hashAlgorithm.ComputeHash(bytes);
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"计算数据哈希值失败，算法: {algorithm}");
                throw;
            }
        }

        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        public async Task<bool> IsDataDuplicateAsync(string dataHash, string apiConfigId, int scopeDays)
        {
            try
            {
                var collection = _mongoRepository.GetCollection<DataHashRecord>("data_hashes");
                
                var filter = Builders<DataHashRecord>.Filter.And(
                    Builders<DataHashRecord>.Filter.Eq(x => x.Hash, dataHash),
                    Builders<DataHashRecord>.Filter.Eq(x => x.ApiConfigId, apiConfigId)
                );

                if (scopeDays > 0)
                {
                    var cutoffDate = DateTime.UtcNow.AddDays(-scopeDays);
                    filter = Builders<DataHashRecord>.Filter.And(
                        filter,
                        Builders<DataHashRecord>.Filter.Gte(x => x.CreatedAt, cutoffDate)
                    );
                }

                var existingRecord = await collection.Find(filter).FirstOrDefaultAsync();
                return existingRecord != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查数据重复性失败");
                return false; // 出错时假设不重复，避免丢失数据
            }
        }

        #region 私有方法

        private List<object> ConvertToDataList(object responseData)
        {
            if (responseData == null)
            {
                return new List<object>();
            }

            if (responseData is IEnumerable<object> enumerable)
            {
                return enumerable.ToList();
            }

            if (responseData is JArray array)
            {
                return array.Select(x => x.ToObject<object>()).ToList();
            }

            // 如果是单个对象，包装成列表
            return new List<object> { responseData };
        }

        private string ComputeFieldBasedHash(object data, List<string> keyFields)
        {
            try
            {
                var jsonData = JToken.FromObject(data);
                var keyValues = new List<string>();

                foreach (var field in keyFields)
                {
                    var value = jsonData.SelectToken(field)?.ToString() ?? "";
                    keyValues.Add(value);
                }

                var combinedKey = string.Join("|", keyValues);
                var bytes = Encoding.UTF8.GetBytes(combinedKey);

                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(bytes);
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算基于字段的哈希值失败");
                throw;
            }
        }

        private async Task SaveDataHashAsync(string hash, string apiConfigId)
        {
            try
            {
                var collection = _mongoRepository.GetCollection<DataHashRecord>("data_hashes");
                var record = new DataHashRecord
                {
                    Hash = hash,
                    ApiConfigId = apiConfigId,
                    CreatedAt = DateTime.UtcNow
                };

                await collection.InsertOneAsync(record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存数据哈希值失败");
                // 不抛出异常，避免影响主流程
            }
        }

        private bool EvaluateFilterConditions(JToken item, List<FilterCondition> conditions, LogicalOperator logicalOperator)
        {
            if (conditions == null || !conditions.Any())
            {
                return true;
            }

            var results = new List<bool>();

            foreach (var condition in conditions)
            {
                var fieldValue = item.SelectToken(condition.FieldPath);
                var result = EvaluateFilterCondition(fieldValue, condition);
                results.Add(result);
            }

            return logicalOperator == LogicalOperator.And ? results.All(r => r) : results.Any(r => r);
        }

        private bool EvaluateFilterCondition(JToken fieldValue, FilterCondition condition)
        {
            if (fieldValue == null)
            {
                return condition.Operator == FilterOperator.Equals && string.IsNullOrEmpty(condition.Value);
            }

            var fieldValueStr = fieldValue.ToString();
            var conditionValue = condition.Value ?? "";

            if (!condition.CaseSensitive)
            {
                fieldValueStr = fieldValueStr.ToLowerInvariant();
                conditionValue = conditionValue.ToLowerInvariant();
            }

            switch (condition.Operator)
            {
                case FilterOperator.Equals:
                    return fieldValueStr == conditionValue;
                case FilterOperator.NotEquals:
                    return fieldValueStr != conditionValue;
                case FilterOperator.Contains:
                    return fieldValueStr.Contains(conditionValue);
                case FilterOperator.NotContains:
                    return !fieldValueStr.Contains(conditionValue);
                case FilterOperator.StartsWith:
                    return fieldValueStr.StartsWith(conditionValue);
                case FilterOperator.EndsWith:
                    return fieldValueStr.EndsWith(conditionValue);
                case FilterOperator.Regex:
                    return Regex.IsMatch(fieldValueStr, conditionValue);
                case FilterOperator.GreaterThan:
                    return CompareNumeric(fieldValue, condition.Value) > 0;
                case FilterOperator.GreaterThanOrEqual:
                    return CompareNumeric(fieldValue, condition.Value) >= 0;
                case FilterOperator.LessThan:
                    return CompareNumeric(fieldValue, condition.Value) < 0;
                case FilterOperator.LessThanOrEqual:
                    return CompareNumeric(fieldValue, condition.Value) <= 0;
                case FilterOperator.In:
                    var inValues = conditionValue.Split(',').Select(v => v.Trim());
                    return inValues.Contains(fieldValueStr);
                case FilterOperator.NotIn:
                    var notInValues = conditionValue.Split(',').Select(v => v.Trim());
                    return !notInValues.Contains(fieldValueStr);
                default:
                    return true;
            }
        }

        private int CompareNumeric(JToken fieldValue, string conditionValue)
        {
            if (double.TryParse(fieldValue.ToString(), out double fieldNum) &&
                double.TryParse(conditionValue, out double conditionNum))
            {
                return fieldNum.CompareTo(conditionNum);
            }

            // 如果不能转换为数字，按字符串比较
            return string.Compare(fieldValue.ToString(), conditionValue, StringComparison.OrdinalIgnoreCase);
        }

        private List<List<JToken>> GroupDataByFields(List<JToken> data, List<string> groupByFields)
        {
            if (groupByFields == null || !groupByFields.Any())
            {
                return new List<List<JToken>> { data };
            }

            var groups = data.GroupBy(item =>
            {
                var keyValues = groupByFields.Select(field => item.SelectToken(field)?.ToString() ?? "").ToArray();
                return string.Join("|", keyValues);
            });

            return groups.Select(g => g.ToList()).ToList();
        }

        private object PerformAggregation(List<JToken> group, AggregationOperation operation)
        {
            var values = group.Select(item => item.SelectToken(operation.SourceField)).Where(v => v != null).ToList();

            switch (operation.Type)
            {
                case AggregationType.Count:
                    return values.Count;
                case AggregationType.Sum:
                    return values.Sum(v => v.ToObject<double>());
                case AggregationType.Average:
                    return values.Average(v => v.ToObject<double>());
                case AggregationType.Max:
                    return values.Max(v => v.ToObject<double>());
                case AggregationType.Min:
                    return values.Min(v => v.ToObject<double>());
                case AggregationType.First:
                    return values.FirstOrDefault()?.ToObject<object>();
                case AggregationType.Last:
                    return values.LastOrDefault()?.ToObject<object>();
                default:
                    return null;
            }
        }

        private ValidationError ValidateField(JToken item, ValidationRule rule, int recordIndex)
        {
            var fieldValue = item.SelectToken(rule.FieldPath);

            // 检查必需字段
            if (rule.IsRequired && (fieldValue == null || string.IsNullOrEmpty(fieldValue.ToString())))
            {
                return new ValidationError
                {
                    RecordIndex = recordIndex,
                    FieldPath = rule.FieldPath,
                    ValidationRule = "Required",
                    ErrorMessage = rule.ErrorMessage ?? "字段为必需",
                    ActualValue = fieldValue?.ToObject<object>()
                };
            }

            if (fieldValue == null)
            {
                return null; // 非必需字段为空时不验证
            }

            var fieldValueStr = fieldValue.ToString();

            switch (rule.Type)
            {
                case ValidationType.Regex:
                    if (!string.IsNullOrEmpty(rule.Parameter) && !Regex.IsMatch(fieldValueStr, rule.Parameter))
                    {
                        return new ValidationError
                        {
                            RecordIndex = recordIndex,
                            FieldPath = rule.FieldPath,
                            ValidationRule = "Regex",
                            ErrorMessage = rule.ErrorMessage ?? "正则表达式验证失败",
                            ActualValue = fieldValue.ToObject<object>(),
                            ExpectedValue = rule.Parameter
                        };
                    }
                    break;

                case ValidationType.Length:
                    if (!string.IsNullOrEmpty(rule.Parameter))
                    {
                        var parts = rule.Parameter.Split('-');
                        if (parts.Length == 2 && int.TryParse(parts[0], out int minLen) && int.TryParse(parts[1], out int maxLen))
                        {
                            if (fieldValueStr.Length < minLen || fieldValueStr.Length > maxLen)
                            {
                                return new ValidationError
                                {
                                    RecordIndex = recordIndex,
                                    FieldPath = rule.FieldPath,
                                    ValidationRule = "Length",
                                    ErrorMessage = rule.ErrorMessage ?? $"字符串长度应在 {minLen}-{maxLen} 之间",
                                    ActualValue = fieldValue.ToObject<object>(),
                                    ExpectedValue = rule.Parameter
                                };
                            }
                        }
                    }
                    break;

                case ValidationType.Range:
                    if (!string.IsNullOrEmpty(rule.Parameter) && double.TryParse(fieldValueStr, out double numValue))
                    {
                        var parts = rule.Parameter.Split('-');
                        if (parts.Length == 2 && double.TryParse(parts[0], out double minVal) && double.TryParse(parts[1], out double maxVal))
                        {
                            if (numValue < minVal || numValue > maxVal)
                            {
                                return new ValidationError
                                {
                                    RecordIndex = recordIndex,
                                    FieldPath = rule.FieldPath,
                                    ValidationRule = "Range",
                                    ErrorMessage = rule.ErrorMessage ?? $"数值应在 {minVal}-{maxVal} 之间",
                                    ActualValue = fieldValue.ToObject<object>(),
                                    ExpectedValue = rule.Parameter
                                };
                            }
                        }
                    }
                    break;

                case ValidationType.Email:
                    var emailRegex = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
                    if (!Regex.IsMatch(fieldValueStr, emailRegex))
                    {
                        return new ValidationError
                        {
                            RecordIndex = recordIndex,
                            FieldPath = rule.FieldPath,
                            ValidationRule = "Email",
                            ErrorMessage = rule.ErrorMessage ?? "邮箱格式无效",
                            ActualValue = fieldValue.ToObject<object>()
                        };
                    }
                    break;

                case ValidationType.Url:
                    if (!Uri.TryCreate(fieldValueStr, UriKind.Absolute, out _))
                    {
                        return new ValidationError
                        {
                            RecordIndex = recordIndex,
                            FieldPath = rule.FieldPath,
                            ValidationRule = "Url",
                            ErrorMessage = rule.ErrorMessage ?? "URL格式无效",
                            ActualValue = fieldValue.ToObject<object>()
                        };
                    }
                    break;

                case ValidationType.Date:
                    if (!DateTime.TryParse(fieldValueStr, out _))
                    {
                        return new ValidationError
                        {
                            RecordIndex = recordIndex,
                            FieldPath = rule.FieldPath,
                            ValidationRule = "Date",
                            ErrorMessage = rule.ErrorMessage ?? "日期格式无效",
                            ActualValue = fieldValue.ToObject<object>()
                        };
                    }
                    break;
            }

            return null;
        }

        private List<List<T>> CreateBatches<T>(List<T> data, int batchSize)
        {
            var batches = new List<List<T>>();
            
            for (int i = 0; i < data.Count; i += batchSize)
            {
                var batch = data.Skip(i).Take(batchSize).ToList();
                batches.Add(batch);
            }

            return batches;
        }

        #endregion
    }

    /// <summary>
    /// 数据哈希记录
    /// </summary>
    public class DataHashRecord
    {
        public string Id { get; set; }
        public string Hash { get; set; }
        public string ApiConfigId { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
