@{
    Layout = "_Layout";
}

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 设置导航标签 -->
<ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
            <i class="fas fa-cog me-2"></i>常规设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button" role="tab">
            <i class="fas fa-database me-2"></i>数据库配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="scheduler-tab" data-bs-toggle="tab" data-bs-target="#scheduler" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>调度器设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="logging-tab" data-bs-toggle="tab" data-bs-target="#logging" type="button" role="tab">
            <i class="fas fa-file-alt me-2"></i>日志配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
            <i class="fas fa-shield-alt me-2"></i>安全设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance" type="button" role="tab">
            <i class="fas fa-tools me-2"></i>系统维护
        </button>
    </li>
</ul>

<!-- 设置内容 -->
<div class="tab-content" id="settingsTabContent">
    <!-- 常规设置 -->
    <div class="tab-pane fade show active" id="general" role="tabpanel">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">系统基本设置</h6>
                    </div>
                    <div class="card-body">
                        <form id="generalSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="systemName" class="form-label">系统名称</label>
                                        <input type="text" class="form-control" id="systemName" value="第三方接口集成管理系统">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="systemVersion" class="form-label">系统版本</label>
                                        <input type="text" class="form-control" id="systemVersion" value="1.0.0" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="defaultTimeout" class="form-label">默认超时时间(秒)</label>
                                        <input type="number" class="form-control" id="defaultTimeout" value="30" min="1" max="300">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="defaultRetryCount" class="form-label">默认重试次数</label>
                                        <input type="number" class="form-control" id="defaultRetryCount" value="3" min="0" max="10">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxConcurrentJobs" class="form-label">最大并发任务数</label>
                                        <input type="number" class="form-control" id="maxConcurrentJobs" value="10" min="1" max="100">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dataRetentionDays" class="form-label">数据保留天数</label>
                                        <input type="number" class="form-control" id="dataRetentionDays" value="30" min="1" max="365">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableAutoCleanup" checked>
                                    <label class="form-check-label" for="enableAutoCleanup">
                                        启用自动数据清理
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        启用系统通知
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                            <i class="fas fa-save me-2"></i>保存设置
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetGeneralSettings()">
                            <i class="fas fa-undo me-2"></i>重置
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">系统信息</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>运行时间:</strong></td>
                                <td id="systemUptime">-</td>
                            </tr>
                            <tr>
                                <td><strong>环境:</strong></td>
                                <td id="environment">Development</td>
                            </tr>
                            <tr>
                                <td><strong>框架版本:</strong></td>
                                <td>.NET Core 3.1</td>
                            </tr>
                            <tr>
                                <td><strong>最后重启:</strong></td>
                                <td id="lastRestart">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据库配置 -->
    <div class="tab-pane fade" id="database" role="tabpanel">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">MongoDB配置</h6>
                    </div>
                    <div class="card-body">
                        <form id="databaseSettingsForm">
                            <div class="mb-3">
                                <label for="mongoConnectionString" class="form-label">连接字符串</label>
                                <input type="text" class="form-control" id="mongoConnectionString"
                                       value="mongodb://admin:****@@192.168.8.106:31004/admin" readonly>
                                <div class="form-text">出于安全考虑，连接字符串已隐藏</div>
                            </div>
                            <div class="mb-3">
                                <label for="mongoDatabaseName" class="form-label">数据库名称</label>
                                <input type="text" class="form-control" id="mongoDatabaseName" 
                                       value="ThirdPartyIntegration" readonly>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="connectionPoolSize" class="form-label">连接池大小</label>
                                        <input type="number" class="form-control" id="connectionPoolSize" value="100" min="10" max="1000">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="connectionTimeout" class="form-label">连接超时(秒)</label>
                                        <input type="number" class="form-control" id="connectionTimeout" value="30" min="5" max="120">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-primary" onclick="testDatabaseConnection()">
                            <i class="fas fa-plug me-2"></i>测试连接
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="saveDatabaseSettings()">
                            <i class="fas fa-save me-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">数据库状态</h6>
                    </div>
                    <div class="card-body">
                        <div id="databaseStatus">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">检查中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调度器设置 -->
    <div class="tab-pane fade" id="scheduler" role="tabpanel">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">调度器配置</h6>
                    </div>
                    <div class="card-body">
                        <form id="schedulerSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="schedulerName" class="form-label">调度器名称</label>
                                        <input type="text" class="form-control" id="schedulerName" value="ThirdPartyIntegrationScheduler">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="schedulerInstanceId" class="form-label">实例ID</label>
                                        <input type="text" class="form-control" id="schedulerInstanceId" value="AUTO" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxThreads" class="form-label">最大线程数</label>
                                        <input type="number" class="form-control" id="maxThreads" value="10" min="1" max="50">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="jobStoreType" class="form-label">作业存储类型</label>
                                        <select class="form-select" id="jobStoreType">
                                            <option value="memory">内存存储</option>
                                            <option value="mongodb" selected>MongoDB存储</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableSchedulerAutoStart" checked>
                                    <label class="form-check-label" for="enableSchedulerAutoStart">
                                        系统启动时自动启动调度器
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableJobPersistence" checked>
                                    <label class="form-check-label" for="enableJobPersistence">
                                        启用作业持久化
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-primary" onclick="saveSchedulerSettings()">
                            <i class="fas fa-save me-2"></i>保存设置
                        </button>
                        <button type="button" class="btn btn-warning ms-2" onclick="restartScheduler()">
                            <i class="fas fa-redo me-2"></i>重启调度器
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">调度器状态</h6>
                    </div>
                    <div class="card-body">
                        <div id="schedulerStatus">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志配置 -->
    <div class="tab-pane fade" id="logging" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">日志配置</h6>
            </div>
            <div class="card-body">
                <form id="loggingSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="logLevel" class="form-label">日志级别</label>
                                <select class="form-select" id="logLevel">
                                    <option value="Trace">Trace</option>
                                    <option value="Debug">Debug</option>
                                    <option value="Information" selected>Information</option>
                                    <option value="Warning">Warning</option>
                                    <option value="Error">Error</option>
                                    <option value="Critical">Critical</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="logRetentionDays" class="form-label">日志保留天数</label>
                                <input type="number" class="form-control" id="logRetentionDays" value="30" min="1" max="365">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableFileLogging" checked>
                                    <label class="form-check-label" for="enableFileLogging">
                                        启用文件日志
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableConsoleLogging" checked>
                                    <label class="form-check-label" for="enableConsoleLogging">
                                        启用控制台日志
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-primary" onclick="saveLoggingSettings()">
                    <i class="fas fa-save me-2"></i>保存设置
                </button>
                <button type="button" class="btn btn-info ms-2" onclick="viewLogs()">
                    <i class="fas fa-eye me-2"></i>查看日志
                </button>
            </div>
        </div>
    </div>

    <!-- 安全设置 -->
    <div class="tab-pane fade" id="security" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">安全配置</h6>
            </div>
            <div class="card-body">
                <form id="securitySettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sessionTimeout" class="form-label">会话超时时间(分钟)</label>
                                <input type="number" class="form-control" id="sessionTimeout" value="30" min="5" max="480">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
                                <input type="number" class="form-control" id="maxLoginAttempts" value="5" min="1" max="20">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableHttps" checked>
                                    <label class="form-check-label" for="enableHttps">
                                        强制HTTPS
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableApiKeyAuth">
                                    <label class="form-check-label" for="enableApiKeyAuth">
                                        启用API密钥认证
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">
                    <i class="fas fa-save me-2"></i>保存设置
                </button>
                <button type="button" class="btn btn-warning ms-2" onclick="generateApiKey()">
                    <i class="fas fa-key me-2"></i>生成API密钥
                </button>
            </div>
        </div>
    </div>

    <!-- 系统维护 -->
    <div class="tab-pane fade" id="maintenance" role="tabpanel">
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">数据维护</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">清理过期数据</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="cleanupDays" value="30" min="1" max="365">
                                <span class="input-group-text">天前</span>
                                <button class="btn btn-warning" onclick="cleanupOldData()">
                                    <i class="fas fa-trash me-2"></i>清理
                                </button>
                            </div>
                            <div class="form-text">将删除指定天数前的执行历史和响应数据</div>
                        </div>
                        <div class="mb-3">
                            <button class="btn btn-info" onclick="optimizeDatabase()">
                                <i class="fas fa-database me-2"></i>优化数据库
                            </button>
                            <div class="form-text">重建索引并优化数据库性能</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">系统操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-success" onclick="exportSystemConfig()">
                                <i class="fas fa-download me-2"></i>导出系统配置
                            </button>
                            <div class="form-text">导出当前系统配置为备份文件</div>
                        </div>
                        <div class="mb-3">
                            <div class="input-group">
                                <input type="file" class="form-control" id="configFile" accept=".json">
                                <button class="btn btn-primary" onclick="importSystemConfig()">
                                    <i class="fas fa-upload me-2"></i>导入配置
                                </button>
                            </div>
                            <div class="form-text">从备份文件恢复系统配置</div>
                        </div>
                        <div class="mb-3">
                            <button class="btn btn-danger" onclick="resetToDefaults()">
                                <i class="fas fa-undo me-2"></i>恢复默认设置
                            </button>
                            <div class="form-text text-danger">将所有设置恢复为默认值</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadSystemInfo();
        loadDatabaseStatus();
        loadSchedulerStatus();
        
        // 每30秒刷新一次状态信息
        setInterval(function() {
            loadSystemInfo();
            loadDatabaseStatus();
            loadSchedulerStatus();
        }, 30000);
    });

    function loadSystemInfo() {
        // 模拟系统信息加载
        var startTime = new Date();
        startTime.setHours(startTime.getHours() - 2); // 假设2小时前启动
        
        $('#systemUptime').text(formatUptime(new Date() - startTime));
        $('#lastRestart').text(startTime.toLocaleString('zh-CN'));
    }

    function formatUptime(milliseconds) {
        var seconds = Math.floor(milliseconds / 1000);
        var minutes = Math.floor(seconds / 60);
        var hours = Math.floor(minutes / 60);
        var days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }

    function loadDatabaseStatus() {
        // 模拟数据库状态检查
        var html = `
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-circle text-success me-2"></i>
                <span>连接状态: 正常</span>
            </div>
            <div class="small text-muted mb-2">
                <div>活跃连接: 5/100</div>
                <div>数据库大小: 256 MB</div>
                <div>集合数量: 8</div>
                <div>最后检查: ${new Date().toLocaleTimeString('zh-CN')}</div>
            </div>
        `;
        $('#databaseStatus').html(html);
    }

    function loadSchedulerStatus() {
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                var html = `
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-circle text-success me-2"></i>
                        <span>状态: ${data.schedulerState || '运行中'}</span>
                    </div>
                    <div class="small text-muted">
                        <div>总任务数: ${data.totalJobs || 0}</div>
                        <div>运行中: ${data.runningJobs || 0}</div>
                        <div>今日执行: ${data.todayExecutions || 0}</div>
                        <div>成功率: ${data.todayExecutions > 0 ? ((data.todaySuccesses / data.todayExecutions) * 100).toFixed(1) + '%' : 'N/A'}</div>
                    </div>
                `;
                $('#schedulerStatus').html(html);
            })
            .fail(function() {
                $('#schedulerStatus').html(`
                    <div class="d-flex align-items-center">
                        <i class="fas fa-circle text-danger me-2"></i>
                        <span>状态: 异常</span>
                    </div>
                `);
            });
    }

    // 常规设置相关函数
    function saveGeneralSettings() {
        var settings = {
            systemName: $('#systemName').val(),
            defaultTimeout: parseInt($('#defaultTimeout').val()),
            defaultRetryCount: parseInt($('#defaultRetryCount').val()),
            maxConcurrentJobs: parseInt($('#maxConcurrentJobs').val()),
            dataRetentionDays: parseInt($('#dataRetentionDays').val()),
            enableAutoCleanup: $('#enableAutoCleanup').is(':checked'),
            enableNotifications: $('#enableNotifications').is(':checked')
        };

        // 这里应该调用实际的保存API
        showSuccess('常规设置已保存');
        console.log('保存常规设置:', settings);
    }

    function resetGeneralSettings() {
        if (confirm('确定要重置常规设置为默认值吗？')) {
            $('#systemName').val('第三方接口集成管理系统');
            $('#defaultTimeout').val(30);
            $('#defaultRetryCount').val(3);
            $('#maxConcurrentJobs').val(10);
            $('#dataRetentionDays').val(30);
            $('#enableAutoCleanup').prop('checked', true);
            $('#enableNotifications').prop('checked', true);
            showSuccess('常规设置已重置为默认值');
        }
    }

    // 数据库设置相关函数
    function testDatabaseConnection() {
        showInfo('正在测试数据库连接...');

        // 模拟连接测试
        setTimeout(function() {
            showSuccess('数据库连接测试成功');
            loadDatabaseStatus();
        }, 2000);
    }

    function saveDatabaseSettings() {
        var settings = {
            connectionPoolSize: parseInt($('#connectionPoolSize').val()),
            connectionTimeout: parseInt($('#connectionTimeout').val())
        };

        showSuccess('数据库设置已保存');
        console.log('保存数据库设置:', settings);
    }

    // 调度器设置相关函数
    function saveSchedulerSettings() {
        var settings = {
            schedulerName: $('#schedulerName').val(),
            maxThreads: parseInt($('#maxThreads').val()),
            jobStoreType: $('#jobStoreType').val(),
            enableSchedulerAutoStart: $('#enableSchedulerAutoStart').is(':checked'),
            enableJobPersistence: $('#enableJobPersistence').is(':checked')
        };

        showSuccess('调度器设置已保存');
        console.log('保存调度器设置:', settings);
    }

    function restartScheduler() {
        if (confirm('确定要重启调度器吗？这将暂时中断所有正在运行的任务。')) {
            showInfo('正在重启调度器...');

            callAPI('/api/Scheduler/stop', 'POST')
                .then(function() {
                    return callAPI('/api/Scheduler/start', 'POST');
                })
                .done(function() {
                    showSuccess('调度器重启成功');
                    loadSchedulerStatus();
                })
                .fail(function() {
                    showError('调度器重启失败');
                });
        }
    }

    // 日志设置相关函数
    function saveLoggingSettings() {
        var settings = {
            logLevel: $('#logLevel').val(),
            logRetentionDays: parseInt($('#logRetentionDays').val()),
            enableFileLogging: $('#enableFileLogging').is(':checked'),
            enableConsoleLogging: $('#enableConsoleLogging').is(':checked')
        };

        showSuccess('日志设置已保存');
        console.log('保存日志设置:', settings);
    }

    function viewLogs() {
        // 这里可以打开日志查看页面或模态框
        showInfo('日志查看功能开发中...');
    }

    // 安全设置相关函数
    function saveSecuritySettings() {
        var settings = {
            sessionTimeout: parseInt($('#sessionTimeout').val()),
            maxLoginAttempts: parseInt($('#maxLoginAttempts').val()),
            enableHttps: $('#enableHttps').is(':checked'),
            enableApiKeyAuth: $('#enableApiKeyAuth').is(':checked')
        };

        showSuccess('安全设置已保存');
        console.log('保存安全设置:', settings);
    }

    function generateApiKey() {
        var apiKey = 'ak_' + Math.random().toString(36).substr(2, 32);

        if (confirm('生成新的API密钥将使旧密钥失效，确定继续吗？')) {
            prompt('新的API密钥（请妥善保存）:', apiKey);
            showSuccess('API密钥已生成');
        }
    }

    // 系统维护相关函数
    function cleanupOldData() {
        var days = parseInt($('#cleanupDays').val());

        if (confirm(`确定要删除 ${days} 天前的数据吗？此操作不可恢复。`)) {
            showInfo('正在清理过期数据...');

            callAPI('/api/ApiResponseData/cleanup?olderThanDays=' + days, 'DELETE')
                .done(function(result) {
                    showSuccess(`数据清理完成，删除了 ${result.deletedCount || 0} 条记录`);
                })
                .fail(function() {
                    showError('数据清理失败');
                });
        }
    }

    function optimizeDatabase() {
        if (confirm('数据库优化可能需要较长时间，确定继续吗？')) {
            showInfo('正在优化数据库...');

            // 模拟数据库优化
            setTimeout(function() {
                showSuccess('数据库优化完成');
                loadDatabaseStatus();
            }, 5000);
        }
    }

    function exportSystemConfig() {
        var config = {
            general: {
                systemName: $('#systemName').val(),
                defaultTimeout: parseInt($('#defaultTimeout').val()),
                defaultRetryCount: parseInt($('#defaultRetryCount').val()),
                maxConcurrentJobs: parseInt($('#maxConcurrentJobs').val()),
                dataRetentionDays: parseInt($('#dataRetentionDays').val()),
                enableAutoCleanup: $('#enableAutoCleanup').is(':checked'),
                enableNotifications: $('#enableNotifications').is(':checked')
            },
            scheduler: {
                schedulerName: $('#schedulerName').val(),
                maxThreads: parseInt($('#maxThreads').val()),
                jobStoreType: $('#jobStoreType').val(),
                enableSchedulerAutoStart: $('#enableSchedulerAutoStart').is(':checked'),
                enableJobPersistence: $('#enableJobPersistence').is(':checked')
            },
            logging: {
                logLevel: $('#logLevel').val(),
                logRetentionDays: parseInt($('#logRetentionDays').val()),
                enableFileLogging: $('#enableFileLogging').is(':checked'),
                enableConsoleLogging: $('#enableConsoleLogging').is(':checked')
            },
            security: {
                sessionTimeout: parseInt($('#sessionTimeout').val()),
                maxLoginAttempts: parseInt($('#maxLoginAttempts').val()),
                enableHttps: $('#enableHttps').is(':checked'),
                enableApiKeyAuth: $('#enableApiKeyAuth').is(':checked')
            },
            exportTime: new Date().toISOString()
        };

        var blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `system_config_${new Date().toISOString().slice(0, 10)}.json`;
        link.click();

        showSuccess('系统配置已导出');
    }

    function importSystemConfig() {
        var fileInput = document.getElementById('configFile');
        var file = fileInput.files[0];

        if (!file) {
            showError('请选择配置文件');
            return;
        }

        var reader = new FileReader();
        reader.onload = function(e) {
            try {
                var config = JSON.parse(e.target.result);

                if (confirm('导入配置将覆盖当前设置，确定继续吗？')) {
                    // 应用配置
                    if (config.general) {
                        $('#systemName').val(config.general.systemName || '');
                        $('#defaultTimeout').val(config.general.defaultTimeout || 30);
                        $('#defaultRetryCount').val(config.general.defaultRetryCount || 3);
                        $('#maxConcurrentJobs').val(config.general.maxConcurrentJobs || 10);
                        $('#dataRetentionDays').val(config.general.dataRetentionDays || 30);
                        $('#enableAutoCleanup').prop('checked', config.general.enableAutoCleanup !== false);
                        $('#enableNotifications').prop('checked', config.general.enableNotifications !== false);
                    }

                    if (config.scheduler) {
                        $('#schedulerName').val(config.scheduler.schedulerName || '');
                        $('#maxThreads').val(config.scheduler.maxThreads || 10);
                        $('#jobStoreType').val(config.scheduler.jobStoreType || 'mongodb');
                        $('#enableSchedulerAutoStart').prop('checked', config.scheduler.enableSchedulerAutoStart !== false);
                        $('#enableJobPersistence').prop('checked', config.scheduler.enableJobPersistence !== false);
                    }

                    if (config.logging) {
                        $('#logLevel').val(config.logging.logLevel || 'Information');
                        $('#logRetentionDays').val(config.logging.logRetentionDays || 30);
                        $('#enableFileLogging').prop('checked', config.logging.enableFileLogging !== false);
                        $('#enableConsoleLogging').prop('checked', config.logging.enableConsoleLogging !== false);
                    }

                    if (config.security) {
                        $('#sessionTimeout').val(config.security.sessionTimeout || 30);
                        $('#maxLoginAttempts').val(config.security.maxLoginAttempts || 5);
                        $('#enableHttps').prop('checked', config.security.enableHttps !== false);
                        $('#enableApiKeyAuth').prop('checked', config.security.enableApiKeyAuth === true);
                    }

                    showSuccess('配置导入成功');
                }
            } catch (error) {
                showError('配置文件格式错误: ' + error.message);
            }
        };

        reader.readAsText(file);
    }

    function resetToDefaults() {
        if (confirm('确定要将所有设置恢复为默认值吗？此操作不可恢复。')) {
            // 重置所有设置为默认值
            resetGeneralSettings();

            // 重置其他设置
            $('#connectionPoolSize').val(100);
            $('#connectionTimeout').val(30);

            $('#schedulerName').val('ThirdPartyIntegrationScheduler');
            $('#maxThreads').val(10);
            $('#jobStoreType').val('mongodb');
            $('#enableSchedulerAutoStart').prop('checked', true);
            $('#enableJobPersistence').prop('checked', true);

            $('#logLevel').val('Information');
            $('#logRetentionDays').val(30);
            $('#enableFileLogging').prop('checked', true);
            $('#enableConsoleLogging').prop('checked', true);

            $('#sessionTimeout').val(30);
            $('#maxLoginAttempts').val(5);
            $('#enableHttps').prop('checked', true);
            $('#enableApiKeyAuth').prop('checked', false);

            showSuccess('所有设置已恢复为默认值');
        }
    }
</script>
}
