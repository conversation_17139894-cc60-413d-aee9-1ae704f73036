﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Docpark.HttpClientExtension;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Docpark.MultipleComparisons.Interface.Models;
using DocPark.Commons;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public class ProductService : IProductService
    {
        private readonly IObjectQuery objectQuery;

        public ILogger _logger { get; }
        public IDocparkHostService _docparkHostService { get; }
        public IDGrpc _grpc { get; }

        public ProductService(ILogger<ProductService> logger, IDocparkHostService docparkHostService, IDGrpc grpc,IObjectQuery objectQuery)
        {
            _logger = logger;
            _docparkHostService = docparkHostService;
            _grpc = grpc;
            this.objectQuery = objectQuery;
        }

        public string ScanBusinessDocumentTypeId { get => _grpc.GetStringAsync("App.DocumentService.ScanBusinessTable").Result; }
        public string InvoiceFolderDocumentTypeId { get => _grpc.GetStringAsync("App.DocumentService.InvoiceFolderTable").Result; }

        public string InvoiceDocumentTypeId { get => _grpc.GetStringAsync("App.DocumentService.InvoiceDocumentTypeId").Result; }
        public Dictionary<string, object> MultCompSettings
        {
            get
            {
                var multCompSettings = _grpc.GetStringAsync("App.DocumentService.MultCompSettings").Result;
                if (!string.IsNullOrEmpty(multCompSettings))
                {
                    return JsonConvert.DeserializeObject<Dictionary<string, object>>(multCompSettings);
                }
                return null;
            }
        }

        public async Task<List<ScanFolderItemDto>> GetScanFolderItems(string businessTypeId, List<string> billNos, string[] itemTypeIds)
        {
            if (string.IsNullOrEmpty(businessTypeId))
            {
                return new List<ScanFolderItemDto>();
            }
            List<Guid> itemTypeObjIds = null;
            if (itemTypeIds != null && itemTypeIds.Length > 0)
            {
                itemTypeObjIds = itemTypeIds.Select(s => CommonUtil.ConvertObjectIdToGuid(s)).ToList();
            }
            var filter = new List<Filter>();
            filter.Add(new Filter() { DisplayType = "文本", Field = "BusinessType", Method = "in", Mode = "in", Type = "Text", Values = new string[] { businessTypeId } });

            if (billNos.Any())
            {
                filter.Add(new Filter()
                {
                    DisplayType = "文本",
                    Field = "BillNo",
                    Method = "in",
                    Mode = "in",
                    Type = "Text",
                    Values = billNos.ToArray()
                });
            }
            //获取业务信息列表
            var folders =await objectQuery.GetList<BusinessDataDto>(filter,new List<Sorter>(),0,100);


            var TargetObjInstanceIds = folders.data.Select(s => s.mstId).ToList();
            var relationships = await _docparkHostService.GetRelationships(ConstObject.BusinessAndScanDocumentRelationships,
                TargetObjInstanceIds);

            List<ScanFolderItemDto> datas = new List<ScanFolderItemDto>();
            bool isAdd = false;
            foreach(var item in folders.data)
            {
                isAdd = false;
                if (itemTypeObjIds!=null && itemTypeObjIds.Count > 0)
                {
                    if (relationships.Where(s => itemTypeObjIds.Contains(s.ObjId)).Count() > 0)
                    {
                        isAdd = true;
                    }
                }
                else
                {
                    isAdd = true;
                }
                
                if(isAdd)
                {
                    ScanFolderItemDto data = new ScanFolderItemDto()
                    {
                        BillNo = item.BillNo,
                        BatchNo = item.BatchNo,
                        BusinessType = item.BusinessType,
                        MstId = item.mstId.ToString(),
                        DocumentIds = relationships.Where(s => s.TargetObjInstanceId == item.mstId).Select(s => s.ObjUniqueId).ToList()
                    };
                    datas.Add(data);
                }
            }
            return datas;
        }
       
    }
}
