using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// API响应数据控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ApiResponseDataController : ControllerBase
    {
        private readonly IApiResponseDataService _responseDataService;
        private readonly ILogger<ApiResponseDataController> _logger;

        public ApiResponseDataController(
            IApiResponseDataService responseDataService,
            ILogger<ApiResponseDataController> logger)
        {
            _responseDataService = responseDataService;
            _logger = logger;
        }

        /// <summary>
        /// 根据ID获取响应数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponseData>> GetById(string id)
        {
            try
            {
                var data = await _responseDataService.GetByIdAsync(id);
                if (data == null)
                {
                    return NotFound();
                }
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API response data by id: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取API配置的响应数据
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        [HttpGet("by-api/{apiConfigId}")]
        public async Task<ActionResult<List<ApiResponseData>>> GetByApiConfigId(string apiConfigId, [FromQuery] int limit = 100)
        {
            try
            {
                var data = await _responseDataService.GetByApiConfigIdAsync(apiConfigId, limit);
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API response data for API: {ApiConfigId}", apiConfigId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 查询响应数据
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="isProcessed"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        [HttpGet("query")]
        public async Task<ActionResult<List<ApiResponseData>>> Query(
            [FromQuery] string apiConfigId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] bool? isProcessed = null,
            [FromQuery] int limit = 100)
        {
            try
            {
                var data = await _responseDataService.QueryAsync(apiConfigId, startTime, endTime, isProcessed, limit);
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying API response data");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 标记数据为已处理
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPatch("{id}/mark-processed")]
        public async Task<ActionResult> MarkAsProcessed(string id)
        {
            try
            {
                var result = await _responseDataService.MarkAsProcessedAsync(id);
                if (!result)
                {
                    return NotFound();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking API response data as processed: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量标记数据为已处理
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPatch("batch-mark-processed")]
        public async Task<ActionResult> MarkBatchAsProcessed([FromBody] List<string> ids)
        {
            try
            {
                if (ids == null || ids.Count == 0)
                {
                    return BadRequest("IDs are required");
                }

                var count = await _responseDataService.MarkBatchAsProcessedAsync(ids);
                return Ok(new { processedCount = count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking batch API response data as processed");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<ApiDataStatistics>> GetStatistics(
            [FromQuery] string apiConfigId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _responseDataService.GetStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API data statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除过期数据
        /// </summary>
        /// <param name="olderThanDays"></param>
        /// <returns></returns>
        [HttpDelete("cleanup")]
        public async Task<ActionResult> CleanupExpiredData([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var olderThan = DateTime.UtcNow.AddDays(-olderThanDays);
                var deletedCount = await _responseDataService.DeleteExpiredDataAsync(olderThan);
                return Ok(new { deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired API response data");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
