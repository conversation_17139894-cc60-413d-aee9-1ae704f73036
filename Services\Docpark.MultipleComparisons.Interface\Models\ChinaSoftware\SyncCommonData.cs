﻿
namespace Docpark.MultipleComparisons.Interface.Models.ChinaSoftware
{
    /// <summary>
    /// 公共参数
    /// </summary>
    public class SyncCommonData
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string yhm { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string yhid { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string sjh { get; set; }

        /// <summary>
        /// 启用标志
        /// </summary>
        public string qybz { get; set; }

        /// <summary>
        /// 账号标志
        /// </summary>
        public string zhbz { get; set; }

        /// <summary>
        /// 录入日期
        /// </summary>
        public string lrrq { get; set; }
        /// <summary>
        /// 用户UUID
        /// </summary>
        public string yhuuid { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; }

        /// <summary>
        /// 组织架构信息
        /// </summary>
        public string qyjson { get; set; }

        /// <summary>
        /// 用户组织架构信息
        /// </summary>
        public string userjson { get; set; }
    }
}
