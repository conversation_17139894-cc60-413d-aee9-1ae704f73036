using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 熔断器服务接口
    /// </summary>
    public interface ICircuitBreakerService
    {
        /// <summary>
        /// 创建熔断器配置
        /// </summary>
        Task<string> CreateCircuitBreakerConfigAsync(CircuitBreakerConfig config);

        /// <summary>
        /// 更新熔断器配置
        /// </summary>
        Task<bool> UpdateCircuitBreakerConfigAsync(string id, CircuitBreakerConfig config);

        /// <summary>
        /// 获取熔断器配置
        /// </summary>
        Task<CircuitBreakerConfig> GetCircuitBreakerConfigAsync(string id);

        /// <summary>
        /// 根据API配置ID获取熔断器配置
        /// </summary>
        Task<CircuitBreakerConfig> GetCircuitBreakerConfigByApiIdAsync(string apiConfigId);

        /// <summary>
        /// 获取所有熔断器配置
        /// </summary>
        Task<List<CircuitBreakerConfig>> GetAllCircuitBreakerConfigsAsync(bool enabledOnly = false);

        /// <summary>
        /// 删除熔断器配置
        /// </summary>
        Task<bool> DeleteCircuitBreakerConfigAsync(string id);

        /// <summary>
        /// 检查是否允许请求
        /// </summary>
        Task<CircuitBreakerResult> IsRequestAllowedAsync(string apiConfigId);

        /// <summary>
        /// 记录请求结果
        /// </summary>
        Task RecordRequestResultAsync(string apiConfigId, bool isSuccess, long responseTimeMs);

        /// <summary>
        /// 手动打开熔断器
        /// </summary>
        Task<bool> OpenCircuitBreakerAsync(string apiConfigId, string reason = null);

        /// <summary>
        /// 手动关闭熔断器
        /// </summary>
        Task<bool> CloseCircuitBreakerAsync(string apiConfigId, string reason = null);

        /// <summary>
        /// 重置熔断器状态
        /// </summary>
        Task<bool> ResetCircuitBreakerAsync(string apiConfigId);

        /// <summary>
        /// 获取熔断器状态
        /// </summary>
        Task<object> GetCircuitBreakerStatusAsync(string apiConfigId);

        /// <summary>
        /// 获取熔断器统计信息
        /// </summary>
        Task<object> GetCircuitBreakerStatisticsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 执行带熔断保护的请求
        /// </summary>
        Task<T> ExecuteWithCircuitBreakerAsync<T>(string apiConfigId, Func<Task<T>> operation, Func<Task<T>> fallback = null);
    }
}
