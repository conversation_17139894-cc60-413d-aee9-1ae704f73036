﻿namespace Docpark.ThirdMaster.Services.Models.EmailCollection
{
    /// <summary>
    /// 发票收集类型
    /// </summary>
    public enum Enum_Collect_Type
    {
        /// <summary>
        /// 供应商发票
        /// </summary>
        SUPPLIER = 1,
        /// <summary>
        /// 客户索赔
        /// </summary>
        CUSTOMER_CLAIM = 2
    }
    public class ReceiveEmailInfo
    {
        public bool Enable { get; set; }
        public string ImapIdComond { get; set; }
        public string ImapHost { get; set; }
        public string SmtpHost { get; set; }
        public int ImapPort { get; set; }
        public int SmtpPort { get; set; }
        public string Account { get; set; }
        public string PassWord { get; set; }
    }
}
