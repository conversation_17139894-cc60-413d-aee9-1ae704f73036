using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 负载均衡配置实体
    /// </summary>
    public class LoadBalancerConfig
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 负载均衡策略
        /// </summary>
        [Required]
        public LoadBalanceStrategy Strategy { get; set; }

        /// <summary>
        /// 服务器列表（JSON格式）
        /// </summary>
        [Required]
        public string ServerList { get; set; }

        /// <summary>
        /// 健康检查配置（JSON格式）
        /// </summary>
        public string HealthCheckConfig { get; set; }

        /// <summary>
        /// 权重配置（JSON格式，用于加权策略）
        /// </summary>
        public string WeightConfig { get; set; }

        /// <summary>
        /// 会话保持配置（JSON格式）
        /// </summary>
        public string SessionAffinityConfig { get; set; }

        /// <summary>
        /// 故障转移配置（JSON格式）
        /// </summary>
        public string FailoverConfig { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 连接超时（毫秒）
        /// </summary>
        public int ConnectionTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 读取超时（毫秒）
        /// </summary>
        public int ReadTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 是否启用故障转移
        /// </summary>
        public bool EnableFailover { get; set; } = true;

        /// <summary>
        /// 是否启用会话保持
        /// </summary>
        public bool EnableSessionAffinity { get; set; } = false;

        /// <summary>
        /// 当前活跃服务器（JSON格式）
        /// </summary>
        public string ActiveServers { get; set; }

        /// <summary>
        /// 统计信息（JSON格式）
        /// </summary>
        public string Statistics { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }
    }
}
