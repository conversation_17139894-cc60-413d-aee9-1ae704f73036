﻿using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DocPark.MongoDb
{
    public partial interface IMongodbManager
    {
        Task<IMongoDatabase> GetDatabaseAsync();

        IMongoDatabase GetDatabase();

        IMongoCollection<T> GetCollection<T>(string name, MongoCollectionSettings settings = null);

        /// <summary>
        /// 异步根据id查询一条数据
        /// </summary>
        /// <param name="host">mongodb连接信息</param>
        /// <param name="id">objectid</param>
        /// <returns></returns>
        Task<T> FindOneAsync<T>(string id, string ClassName, bool isObjectId = true, string[] field = null) where T : class, new();

        /// <summary>
        /// 异步查询集合
        /// </summary>
        /// <param name="host">mongodb连接信息</param>
        /// <param name="filter">查询条件</param>
        /// <param name="ClassName">实体类</param>
        /// <param name="field">要查询的字段,不写时查询全部</param>
        /// <param name="sort">要排序的字段</param>
        /// <returns></returns>
        Task<List<T>> FindListAsync<T>(FilterDefinition<T> filter, string ClassName, string[] field = null, SortDefinition<T> sort = null) where T : class, new();
        /// <summary>
        /// 异步分页查询集合
        /// </summary>
        /// <param name="host">mongodb连接信息</param>
        /// <param name="filter">查询条件</param>
        /// <param name="ClassName">实体类</param>
        /// <param name="pageIndex">当前页</param>
        /// <param name="pageSize">页容量</param>
        /// <param name="field">要查询的字段,不写时查询全部</param>
        /// <param name="sort">要排序的字段</param>
        /// <returns></returns>
        Task<List<T>> FindListByPageAsync<T>(FilterDefinition<T> filter, string ClassName, int pageIndex, int pageSize, string[] field = null, SortDefinition<T> sort = null) where T : class, new();
        /// <summary>
        /// 异步修改一条数据
        /// </summary>
        /// <param name="t">添加的实体</param>
        /// <param name="ClassName">实体类</param>
        /// <param name="host">mongodb连接信息</param>
        /// <returns></returns>
        Task<UpdateResult> UpdateAsync<T>(T t, string ClassName, object id, bool isObjectId) where T : class, new();
        /// <summary>
        /// 异步根据条件获取总数
        /// </summary>
        /// <param name="host">mongodb连接信息</param>
        /// <param name="filter">条件</param>
        /// <returns></returns>
        Task<long> CountAsync<T>(FilterDefinition<T> filter, string ClassName) where T : class, new();
    }
}
