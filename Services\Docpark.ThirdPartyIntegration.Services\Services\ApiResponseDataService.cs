using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// API响应数据服务实现
    /// </summary>
    public class ApiResponseDataService : IApiResponseDataService
    {
        private readonly IMongoRepository _mongoRepository;
        private readonly IApiConfigurationService _apiConfigService;
        private readonly ILogger<ApiResponseDataService> _logger;
        private const string CollectionName = "api_response_data";

        public ApiResponseDataService(
            IMongoRepository mongoRepository,
            IApiConfigurationService apiConfigService,
            ILogger<ApiResponseDataService> logger)
        {
            _mongoRepository = mongoRepository;
            _apiConfigService = apiConfigService;
            _logger = logger;
        }

        public async Task<string> SaveResponseDataAsync(ApiExecutionResult executionResult)
        {
            try
            {
                if (!executionResult.IsSuccess || string.IsNullOrEmpty(executionResult.ResponseData))
                {
                    _logger.LogDebug("Skipping save for unsuccessful execution or empty response data");
                    return null;
                }

                // 获取API配置信息
                var apiConfig = await _apiConfigService.GetByIdAsync(executionResult.ApiConfigId);
                var apiName = apiConfig?.Name ?? "Unknown";

                // 计算数据哈希
                var dataHash = ComputeDataHash(executionResult.ResponseData);

                // 检查是否重复
                if (await IsDuplicateDataAsync(dataHash, executionResult.ApiConfigId))
                {
                    _logger.LogDebug("Duplicate data detected, skipping save for API: {ApiConfigId}", executionResult.ApiConfigId);
                    return null;
                }

                // 解析JSON数据
                BsonDocument parsedData = null;
                int recordCount = 0;
                try
                {
                    var jsonObject = JsonConvert.DeserializeObject(executionResult.ResponseData);
                    parsedData = BsonDocument.Parse(JsonConvert.SerializeObject(jsonObject));
                    recordCount = CountRecords(parsedData);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse response data as JSON for API: {ApiConfigId}", executionResult.ApiConfigId);
                }

                // 创建响应数据实体
                var responseData = new ApiResponseData
                {
                    ApiConfigId = executionResult.ApiConfigId,
                    ApiName = apiName,
                    ExecutionLogId = executionResult.ExecutionLogId,
                    RawResponseData = executionResult.ResponseData,
                    ParsedData = parsedData,
                    DataHash = dataHash,
                    StatusCode = executionResult.StatusCode,
                    DataSize = Encoding.UTF8.GetByteCount(executionResult.ResponseData),
                    RecordCount = recordCount,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(30) // 默认30天后过期
                };

                await _mongoRepository.InsertOneAsync(CollectionName, responseData);
                _logger.LogInformation("Saved API response data for API: {ApiConfigId}, Size: {DataSize} bytes, Records: {RecordCount}", 
                    executionResult.ApiConfigId, responseData.DataSize, recordCount);

                return responseData.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving API response data for API: {ApiConfigId}", executionResult.ApiConfigId);
                throw;
            }
        }

        public async Task<ApiResponseData> GetByIdAsync(string id)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.Eq(x => x.Id, id);
                return await _mongoRepository.FindOneAsync<ApiResponseData>(CollectionName, filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API response data by id: {Id}", id);
                throw;
            }
        }

        public async Task<List<ApiResponseData>> GetByApiConfigIdAsync(string apiConfigId, int limit = 100)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.Eq(x => x.ApiConfigId, apiConfigId);
                var sort = Builders<ApiResponseData>.Sort.Descending(x => x.CreatedAt);
                
                return await _mongoRepository.FindListAsync<ApiResponseData>(
                    CollectionName,
                    filter,
                    sort,
                    limit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API response data for API: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        public async Task<List<ApiResponseData>> QueryAsync(
            string apiConfigId = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            bool? isProcessed = null,
            int limit = 100)
        {
            try
            {
                var filterBuilder = Builders<ApiResponseData>.Filter;
                var filters = new List<FilterDefinition<ApiResponseData>>();

                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    filters.Add(filterBuilder.Eq(x => x.ApiConfigId, apiConfigId));
                }

                if (startTime.HasValue)
                {
                    filters.Add(filterBuilder.Gte(x => x.CreatedAt, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filters.Add(filterBuilder.Lte(x => x.CreatedAt, endTime.Value));
                }

                if (isProcessed.HasValue)
                {
                    filters.Add(filterBuilder.Eq(x => x.IsProcessed, isProcessed.Value));
                }

                var filter = filters.Count > 0 
                    ? filterBuilder.And(filters) 
                    : filterBuilder.Empty;

                var sort = Builders<ApiResponseData>.Sort.Descending(x => x.CreatedAt);

                return await _mongoRepository.FindListAsync<ApiResponseData>(
                    CollectionName,
                    filter,
                    sort,
                    limit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying API response data");
                throw;
            }
        }

        public async Task<bool> MarkAsProcessedAsync(string id)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.Eq(x => x.Id, id);
                var update = Builders<ApiResponseData>.Update
                    .Set(x => x.IsProcessed, true)
                    .Set(x => x.ProcessedAt, DateTime.UtcNow);

                var result = await _mongoRepository.UpdateOneAsync(CollectionName, filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking API response data as processed: {Id}", id);
                throw;
            }
        }

        public async Task<long> MarkBatchAsProcessedAsync(List<string> ids)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.In(x => x.Id, ids);
                var update = Builders<ApiResponseData>.Update
                    .Set(x => x.IsProcessed, true)
                    .Set(x => x.ProcessedAt, DateTime.UtcNow);

                var result = await _mongoRepository.UpdateManyAsync(CollectionName, filter, update);
                return result.ModifiedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking batch API response data as processed");
                throw;
            }
        }

        public async Task<long> DeleteExpiredDataAsync(DateTime olderThan)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.Or(
                    Builders<ApiResponseData>.Filter.Lt(x => x.ExpiresAt, DateTime.UtcNow),
                    Builders<ApiResponseData>.Filter.Lt(x => x.CreatedAt, olderThan)
                );

                var result = await _mongoRepository.DeleteManyAsync<ApiResponseData>(CollectionName, filter);
                _logger.LogInformation("Deleted {Count} expired API response data records", result.DeletedCount);
                return result.DeletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting expired API response data");
                throw;
            }
        }

        public async Task<bool> IsDuplicateDataAsync(string dataHash, string apiConfigId)
        {
            try
            {
                var filter = Builders<ApiResponseData>.Filter.And(
                    Builders<ApiResponseData>.Filter.Eq(x => x.DataHash, dataHash),
                    Builders<ApiResponseData>.Filter.Eq(x => x.ApiConfigId, apiConfigId)
                );

                var count = await _mongoRepository.CountDocumentsAsync<ApiResponseData>(CollectionName, filter);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for duplicate data");
                return false;
            }
        }

        private string ComputeDataHash(string data)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }

        public async Task<ApiDataStatistics> GetStatisticsAsync(
            string apiConfigId = null,
            DateTime? startTime = null,
            DateTime? endTime = null)
        {
            try
            {
                var filterBuilder = Builders<ApiResponseData>.Filter;
                var filters = new List<FilterDefinition<ApiResponseData>>();

                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    filters.Add(filterBuilder.Eq(x => x.ApiConfigId, apiConfigId));
                }

                if (startTime.HasValue)
                {
                    filters.Add(filterBuilder.Gte(x => x.CreatedAt, startTime.Value));
                }

                if (endTime.HasValue)
                {
                    filters.Add(filterBuilder.Lte(x => x.CreatedAt, endTime.Value));
                }

                var filter = filters.Count > 0
                    ? filterBuilder.And(filters)
                    : filterBuilder.Empty;

                // 获取基本统计信息
                var totalRecords = await _mongoRepository.CountDocumentsAsync<ApiResponseData>(CollectionName, filter);

                var processedFilter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsProcessed, true));
                var processedRecords = await _mongoRepository.CountDocumentsAsync<ApiResponseData>(CollectionName, processedFilter);

                var successFilter = filterBuilder.And(filter, filterBuilder.Gte(x => x.StatusCode, 200), filterBuilder.Lt(x => x.StatusCode, 300));
                var successfulResponses = await _mongoRepository.CountDocumentsAsync<ApiResponseData>(CollectionName, successFilter);

                // 获取数据大小统计
                var pipeline = new BsonDocument[]
                {
                    new BsonDocument("$match", filter.Render(BsonSerializer.SerializerRegistry.GetSerializer<ApiResponseData>(), BsonSerializer.SerializerRegistry)),
                    new BsonDocument("$group", new BsonDocument
                    {
                        { "_id", BsonNull.Value },
                        { "totalSize", new BsonDocument("$sum", "$DataSize") },
                        { "avgSize", new BsonDocument("$avg", "$DataSize") },
                        { "minDate", new BsonDocument("$min", "$CreatedAt") },
                        { "maxDate", new BsonDocument("$max", "$CreatedAt") }
                    })
                };

                var aggregateResult = await _mongoRepository.AggregateAsync(CollectionName, pipeline);
                var stats = aggregateResult.FirstOrDefault();

                var statistics = new ApiDataStatistics
                {
                    TotalRecords = totalRecords,
                    ProcessedRecords = processedRecords,
                    UnprocessedRecords = totalRecords - processedRecords,
                    SuccessfulResponses = successfulResponses,
                    FailedResponses = totalRecords - successfulResponses,
                    StatisticsPeriodStart = startTime,
                    StatisticsPeriodEnd = endTime,
                    ApiConfigId = apiConfigId
                };

                if (stats != null)
                {
                    statistics.TotalDataSize = stats.GetValue("totalSize", 0L).ToInt64();
                    statistics.AverageDataSize = stats.GetValue("avgSize", 0.0).ToDouble();
                    statistics.EarliestDataTime = stats.GetValue("minDate", BsonNull.Value).IsBsonNull ? (DateTime?)null : stats["minDate"].ToUniversalTime();
                    statistics.LatestDataTime = stats.GetValue("maxDate", BsonNull.Value).IsBsonNull ? (DateTime?)null : stats["maxDate"].ToUniversalTime();
                }

                // 获取API名称（如果是单个API的统计）
                if (!string.IsNullOrEmpty(apiConfigId))
                {
                    var apiConfig = await _apiConfigService.GetByIdAsync(apiConfigId);
                    statistics.ApiName = apiConfig?.Name;
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API data statistics");
                throw;
            }
        }

        private int CountRecords(BsonDocument document)
        {
            // 简单的记录计数逻辑，可以根据需要扩展
            if (document == null) return 0;

            // 如果是数组，返回数组长度
            if (document.Contains("data") && document["data"].IsBsonArray)
            {
                return document["data"].AsBsonArray.Count;
            }

            // 如果根级别是数组
            if (document.ElementCount == 1 && document.Elements.First().Value.IsBsonArray)
            {
                return document.Elements.First().Value.AsBsonArray.Count;
            }

            // 默认返回1（单个记录）
            return 1;
        }
    }
}
