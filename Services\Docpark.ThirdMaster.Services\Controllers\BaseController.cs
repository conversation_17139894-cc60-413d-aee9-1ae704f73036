﻿using DocPark.Commons;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using Docpark.ThirdMaster.Services.Models;

namespace Docpark.ThirdMaster.Services.Controllers
{
    /// <summary>
    /// 基础控制器
    /// </summary>
    //[Authorize]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        /// <summary>
        /// 当前用户信息
        /// </summary>
        protected UserData CurrentUser
        {
            get
            {
                return new UserData
                {
                    id = User.Claims.Where(w => w.Type == ClaimType.NameIdentifier).FirstOrDefault().Value,
                    name = User.Claims.Where(w => w.Type == ClaimType.Name).FirstOrDefault().Value,
                    role = User.Claims.Where(w => w.Type == ClaimType.Role).Select(s => s.Value).ToList(),
                    tenantId = User.Claims.Where(w => w.Type == ClaimType.TenantId).FirstOrDefault().Value
                };
            }
        }
        /// <summary>
        /// 成功
        /// </summary>
        /// <returns></returns>
        protected ResultDataDto OnSuccess()
        {
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = true
            };
        }
        /// <summary>
        /// 成功
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        protected ResultDataDto OnSuccess(dynamic data)
        {
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = data
            };
        }
        /// <summary>
        /// 成功
        /// </summary>
        /// <param name="data"></param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        protected ResultDataDto OnSuccess(dynamic data, int totalCount)
        {
            return new ResultDataDto()
            {
                IsSuccess = true,
                Data = data,
                TotalCount = totalCount
            };
        }
        /// <summary>
        /// 失败
        /// </summary>
        /// <returns></returns>
        protected ResultDataDto OnError()
        {
            return new ResultDataDto()
            {
                IsSuccess = false,
            };
        }
        /// <summary>
        /// 失败
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns></returns>
        protected ResultDataDto OnError(string message)
        {
            return new ResultDataDto()
            {
                IsSuccess = false,
                Message = message,
            };
        }
        /// <summary>
        /// 失败
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="ex">异常</param>
        /// <returns></returns>
        protected ResultDataDto OnError(string message, Exception ex)
        {
            Console.WriteLine($"Message:{ex.Message},StackTrace:${ex.StackTrace}");
            return new ResultDataDto()
            {
                IsSuccess = false,
                Message = message
            };
        }
    }
}
