using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// API响应数据服务接口
    /// </summary>
    public interface IApiResponseDataService
    {
        /// <summary>
        /// 保存API响应数据
        /// </summary>
        /// <param name="executionResult"></param>
        /// <returns></returns>
        Task<string> SaveResponseDataAsync(ApiExecutionResult executionResult);

        /// <summary>
        /// 根据ID获取响应数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<ApiResponseData> GetByIdAsync(string id);

        /// <summary>
        /// 获取API配置的响应数据
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        Task<List<ApiResponseData>> GetByApiConfigIdAsync(string apiConfigId, int limit = 100);

        /// <summary>
        /// 根据条件查询响应数据
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="isProcessed"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        Task<List<ApiResponseData>> QueryAsync(
            string apiConfigId = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            bool? isProcessed = null,
            int limit = 100);

        /// <summary>
        /// 标记数据为已处理
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> MarkAsProcessedAsync(string id);

        /// <summary>
        /// 批量标记数据为已处理
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<long> MarkBatchAsProcessedAsync(List<string> ids);

        /// <summary>
        /// 删除过期的响应数据
        /// </summary>
        /// <param name="olderThan"></param>
        /// <returns></returns>
        Task<long> DeleteExpiredDataAsync(DateTime olderThan);

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        /// <param name="apiConfigId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<ApiDataStatistics> GetStatisticsAsync(
            string apiConfigId = null,
            DateTime? startTime = null,
            DateTime? endTime = null);

        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        /// <param name="dataHash"></param>
        /// <param name="apiConfigId"></param>
        /// <returns></returns>
        Task<bool> IsDuplicateDataAsync(string dataHash, string apiConfigId);
    }
}
