@{
    Layout = "_Layout";
}

<!-- 警告容器 -->
<div id="alerts-container"></div>

<!-- 操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createApiModal">
            <i class="fas fa-plus me-2"></i>创建新API配置
        </button>
        <button class="btn btn-success" onclick="refreshApiList()">
            <i class="fas fa-sync-alt me-2"></i>刷新
        </button>
    </div>
    <div>
        <input type="text" class="form-control" id="searchInput" placeholder="搜索API配置..." style="width: 300px;">
    </div>
</div>

<!-- API配置列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">API配置列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="apiConfigsTable">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>URL</th>
                        <th>方法</th>
                        <th>状态</th>
                        <th>认证类型</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="apiConfigsTableBody">
                    <tr>
                        <td colspan="7" class="text-center">
                            <i class="fas fa-spinner fa-spin"></i>
                            加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建API配置模态框 -->
<div class="modal fade" id="createApiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建API配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createApiForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiName" class="form-label">API名称 *</label>
                                <input type="text" class="form-control" id="apiName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiMethod" class="form-label">HTTP方法 *</label>
                                <select class="form-select" id="apiMethod" required>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="apiBaseUrl" class="form-label">基础URL *</label>
                                <input type="url" class="form-control" id="apiBaseUrl" placeholder="https://api.example.com" required>
                                <div class="form-text">API的基础URL，例如：https://api.example.com</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="apiEndpoint" class="form-label">端点路径 *</label>
                                <input type="text" class="form-control" id="apiEndpoint" placeholder="/api/users" required>
                                <div class="form-text">API端点路径，例如：/api/users</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="apiDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="apiDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="apiAuthConfig" class="form-label">授权配置</label>
                        <select class="form-select" id="apiAuthConfig">
                            <option value="">无需授权</option>
                        </select>
                        <div class="form-text">
                            选择API调用时使用的授权配置。
                            <a href="/auth-configs" target="_blank">管理授权配置</a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiTimeout" class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-control" id="apiTimeout" value="30" min="1" max="300">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiRetryCount" class="form-label">重试次数</label>
                                <input type="number" class="form-control" id="apiRetryCount" value="3" min="0" max="10">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="apiIsEnabled" checked>
                            <label class="form-check-label" for="apiIsEnabled">
                                启用此API配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createApiConfig()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑API配置模态框 -->
<div class="modal fade" id="editApiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑API配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editApiForm">
                    <input type="hidden" id="editApiId">
                    <!-- 表单字段与创建表单相同 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editApiName" class="form-label">API名称 *</label>
                                <input type="text" class="form-control" id="editApiName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editApiMethod" class="form-label">HTTP方法 *</label>
                                <select class="form-select" id="editApiMethod" required>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="editApiBaseUrl" class="form-label">基础URL *</label>
                                <input type="url" class="form-control" id="editApiBaseUrl" placeholder="https://api.example.com" required>
                                <div class="form-text">API的基础URL，例如：https://api.example.com</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editApiEndpoint" class="form-label">端点路径 *</label>
                                <input type="text" class="form-control" id="editApiEndpoint" placeholder="/api/users" required>
                                <div class="form-text">API端点路径，例如：/api/users</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editApiDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editApiDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editApiAuthConfig" class="form-label">授权配置</label>
                        <select class="form-select" id="editApiAuthConfig">
                            <option value="">无需授权</option>
                        </select>
                        <div class="form-text">
                            选择API调用时使用的授权配置。
                            <a href="/auth-configs" target="_blank">管理授权配置</a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editApiTimeout" class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-control" id="editApiTimeout" min="1" max="300">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editApiRetryCount" class="form-label">重试次数</label>
                                <input type="number" class="form-control" id="editApiRetryCount" min="0" max="10">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editApiIsEnabled">
                            <label class="form-check-label" for="editApiIsEnabled">
                                启用此API配置
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateApiConfig()">保存</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadApiConfigs();
        loadAuthConfigs();

        // 搜索功能
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#apiConfigsTableBody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });

    function loadAuthConfigs() {
        callAPI('/api/AuthenticationConfig')
            .done(function(data) {
                var authSelect = $('#apiAuthConfig');
                var editAuthSelect = $('#editApiAuthConfig');

                // 清空现有选项（保留"无需授权"选项）
                authSelect.find('option:not(:first)').remove();
                editAuthSelect.find('option:not(:first)').remove();

                // 添加授权配置选项
                data.forEach(function(config) {
                    if (config.isEnabled) {
                        var option = `<option value="${config.id}">${config.name} (${getAuthTypeText(config.type)})</option>`;
                        authSelect.append(option);
                        editAuthSelect.append(option);
                    }
                });
            })
            .fail(function() {
                console.warn('加载授权配置失败');
            });
    }

    function getAuthTypeText(type) {
        switch(type) {
            case 1: return '基础认证';
            case 2: return 'OAuth2';
            case 3: return 'API密钥';
            case 4: return 'Bearer令牌';
            case 99: return '自定义';
            default: return '未知';
        }
    }

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                displayApiConfigs(data);
            })
            .fail(function() {
                $('#apiConfigsTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>');
            });
    }

    function displayApiConfigs(configs) {
        var tbody = $('#apiConfigsTableBody');
        tbody.empty();
        
        if (configs.length === 0) {
            tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无API配置</td></tr>');
            return;
        }
        
        configs.forEach(function(config) {
            var statusBadge = config.isEnabled 
                ? '<span class="badge bg-success">启用</span>' 
                : '<span class="badge bg-secondary">禁用</span>';
                
            var authType = getAuthTypeName(config.authenticationConfigId);
            
            var fullUrl = (config.baseUrl || '') + (config.endpoint || '');
            var row = `
                <tr>
                    <td>${config.name || 'N/A'}</td>
                    <td><code>${fullUrl || 'N/A'}</code></td>
                    <td><span class="badge bg-primary">${config.method || 'GET'}</span></td>
                    <td>${statusBadge}</td>
                    <td>${authType}</td>
                    <td>${formatDateTime(config.createdAt)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editApiConfig('${config.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="testApiConfig('${config.id}')">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="configureMapping('${config.id}')">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="configureSchedule('${config.id}')">
                                <i class="fas fa-clock"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteApiConfig('${config.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getAuthTypeName(authConfigId) {
        if (!authConfigId) return '<span class="text-muted">无认证</span>';
        return '<span class="badge bg-info">已配置</span>';
    }

    function createApiConfig() {
        var authConfigId = $('#apiAuthConfig').val();
        var formData = {
            name: $('#apiName').val(),
            baseUrl: $('#apiBaseUrl').val(),
            endpoint: $('#apiEndpoint').val(),
            method: $('#apiMethod').val(),
            description: $('#apiDescription').val(),
            authenticationConfigId: authConfigId || null,
            timeoutSeconds: parseInt($('#apiTimeout').val()) || 30,
            retryCount: parseInt($('#apiRetryCount').val()) || 3,
            isEnabled: $('#apiIsEnabled').is(':checked')
        };

        if (!formData.name || !formData.baseUrl || !formData.endpoint) {
            showError('请填写必填字段');
            return;
        }
        
        callAPI('/api/ApiConfiguration', 'POST', formData)
            .done(function(data) {
                showSuccess('API配置创建成功');
                $('#createApiModal').modal('hide');
                $('#createApiForm')[0].reset();
                loadApiConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showError('创建失败: ' + message);
            });
    }

    function editApiConfig(id) {
        callAPI('/api/ApiConfiguration/' + id)
            .done(function(config) {
                $('#editApiId').val(config.id);
                $('#editApiName').val(config.name);
                $('#editApiBaseUrl').val(config.baseUrl);
                $('#editApiEndpoint').val(config.endpoint);
                $('#editApiMethod').val(config.method);
                $('#editApiDescription').val(config.description);
                $('#editApiAuthConfig').val(config.authenticationConfigId || '');
                $('#editApiTimeout').val(config.timeoutSeconds);
                $('#editApiRetryCount').val(config.retryCount);
                $('#editApiIsEnabled').prop('checked', config.isEnabled);
                
                $('#editApiModal').modal('show');
            })
            .fail(function() {
                showError('获取API配置详情失败');
            });
    }

    function updateApiConfig() {
        var id = $('#editApiId').val();
        var authConfigId = $('#editApiAuthConfig').val();
        var formData = {
            id: id,
            name: $('#editApiName').val(),
            baseUrl: $('#editApiBaseUrl').val(),
            endpoint: $('#editApiEndpoint').val(),
            method: $('#editApiMethod').val(),
            description: $('#editApiDescription').val(),
            authenticationConfigId: authConfigId || null,
            timeoutSeconds: parseInt($('#editApiTimeout').val()) || 30,
            retryCount: parseInt($('#editApiRetryCount').val()) || 3,
            isEnabled: $('#editApiIsEnabled').is(':checked')
        };
        
        callAPI('/api/ApiConfiguration/' + id, 'PUT', formData)
            .done(function(data) {
                showSuccess('API配置更新成功');
                $('#editApiModal').modal('hide');
                loadApiConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '更新失败';
                showError('更新失败: ' + message);
            });
    }

    function deleteApiConfig(id) {
        if (!confirm('确定要删除这个API配置吗？此操作不可恢复。')) {
            return;
        }
        
        callAPI('/api/ApiConfiguration/' + id, 'DELETE')
            .done(function() {
                showSuccess('API配置删除成功');
                loadApiConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '删除失败';
                showError('删除失败: ' + message);
            });
    }

    function testApiConfig(id) {
        callAPI('/api/ApiExecution/' + id + '/execute', 'POST')
            .done(function(result) {
                if (result.isSuccess) {
                    showSuccess('API执行成功');
                    // 可以显示更多执行结果信息
                    if (result.responseData) {
                        console.log('API响应数据:', result.responseData);
                    }
                } else {
                    showWarning('API执行失败: ' + (result.errorMessage || '未知错误'));
                }
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : 'API执行请求失败';
                showError('执行失败: ' + message);
            });
    }

    function configureMapping(id) {
        window.location.href = '/data-mapping?apiId=' + id;
    }

    function configureSchedule(id) {
        window.location.href = '/scheduler?apiId=' + id;
    }

    function refreshApiList() {
        loadApiConfigs();
        showSuccess('列表已刷新');
    }
</script>
}
