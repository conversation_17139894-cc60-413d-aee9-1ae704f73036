﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Request_MC01
    {
        /// <summary>
        /// 单据来源
        /// </summary>
        public string third_bill_source { get; set; }
        /// <summary>
        /// 第三方单据状态
        /// </summary>
        public string third_bill_status { get; set; }
        /// <summary>
        /// 业务大类代码
        /// </summary>
        public string business_type { get; set; }
        /// <summary>
        /// 单据ID
        /// </summary>
        [Required(ErrorMessage ="单据ID必填")]
        public string third_bill_guid { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        [Required(ErrorMessage = "单据编码必填")]
        public string bill_no { get; set; }
        /// <summary>
        /// 单据标题
        /// </summary>
        public string third_bill_title { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public string bill_submit_time { get; set; }
        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string proposer_name { get; set; }
        /// <summary>
        /// 申请人编码
        /// </summary>
        public string proposer_code { get; set; }
        /// <summary>
        /// 申请人部门名称
        /// </summary>
        public string proposer_dept_name { get; set; }
        /// <summary>
        /// 申请人部门编码
        /// </summary>
        public string proposer_dept_code { get; set; }
        /// <summary>
        /// 费用归属成本中心名称
        /// </summary>
        public string cost_center_name { get; set; }
        /// <summary>
        /// 费用归属成本中心编码
        /// </summary>
        public string cost_center_code { get; set; }
        /// <summary>
        /// 费用归属公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 费用归属公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string supplier_name { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string supplier_code { get; set; }
        /// <summary>
        /// 费用明细
        /// </summary>
        public List<Expense_details> expense_details { get; set; }
        /// <summary>
        /// 发票明细
        /// </summary>
        ///[Required(ErrorMessage = "发票明细必填")]
        public List<Invoice_details> invoice_details { get; set; }
        /// <summary>
        /// 单据发票附件
        /// </summary>
        public List<AnnexItems> annex_items { get; set; }
        /// <summary>
        /// 报销总金额
        /// </summary>
        public Double total_amount { get; set; }
        /// <summary>
        /// 财务核定总金额
        /// </summary>
        public Double total_approved_amount { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 其他附件
        /// </summary>
        public List<OtherAnnexItems> other_annex_items { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string business_status { get; set; }
        /// <summary>
        /// 费用凭证号
        /// </summary>
        public string expense_voucher_no { get; set; }
        /// <summary>
        /// 支付凭证号
        /// </summary>
        public string payment_voucher_no { get; set; }
        [Required(ErrorMessage ="任务信息必填")]
        /// <summary>
        /// 标记(1.报销发起推送，2.财务审批通过后数据推送)
        /// </summary>
        public int task_type { get; set; }
        /// <summary>
        /// 报销单文件-base64
        /// </summary>
        public string reim_file_base64 { get; set; }
        /// <summary>
        /// 报销单文件-类型
        /// </summary>
        public string reim_filetype { get; set; }
        /// <summary>
        /// 是否为预付款      1：是预付款    2：不是预付款
        /// </summary>
        public string is_restart { get; set; }
        /// <summary>
        /// 是否为无实物发票（电子发票）   1：是无实物发票    2：不是无实物发票
        /// </summary>
        public string is_real_invoice { get; set; }
    }
}
