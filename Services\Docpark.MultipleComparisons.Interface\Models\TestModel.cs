﻿using Docpark.HttpClientExtension.Json;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Docpark.MultipleComparisons.Interface.Models
{
    [JsonTable("MC_BILL_MASTER_DATA")]
    public class TestModel
    {
        [JsonProperty(PropertyName = "bill_no")]
        public string no { get; set; }

        public string business_type { get; set; }

        public string MstId { get; set; }

    }
}
