﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 费用明细
    /// </summary>
    public class Field_3
    {
        /// <summary>
        /// 费用明细ID
        /// </summary>
        public string fd_id { get; set; }
        /// <summary>
        /// 财务核定金额
        /// </summary>
        public double fd_approved_money { get; set; }
        /// <summary>
        /// 申请金额
        /// </summary>
        public double fd_apply_money { get; set; }
        /// <summary>
        /// 费用类型名称
        /// </summary>
        public string fd_expense_item_name { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string fd_over_standar_reason { get; set; }
        /// <summary>
        /// 费用类型编码
        /// </summary>
        public string fd_expense_item_code { get; set; }
        /// <summary>
        /// 备用字段1
        /// </summary>
        public string fd_attribute_one { get; set; }
        /// <summary>
        /// 备用字段2
        /// </summary>
        public string fd_attribute_two { get; set; }
    }

    /// <summary>
    /// 单据附件
    /// </summary>
    public class FdOtherFileList
    {
        /// <summary>
        /// 附件url(url请以url@开头)或文件base64编码
        /// </summary>
        public string file { get; set; }
        /// <summary>
        /// 附件文件名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 文件后缀
        /// </summary>
        public string type { get; set; }
    }

    /// <summary>
    /// 0A单据归档数据推送实体
    /// </summary>
    public class Request_OA_Bill_Archive_Data
    {
        /// <summary>
        /// 申请时间
        /// </summary>
        public string fd_apply_date { get; set; }
        /// <summary>
        /// 费用明细
        /// </summary>
        public List<Field_3> field_3 { get; set; }
        /// <summary>
        /// 费用归属公司名称
        /// </summary>
        public string fd_company_name { get; set; }
        /// <summary>
        /// 费用明细
        /// </summary>
        public string fdDetailList { get; set; }
        /// <summary>
        /// 发票明细
        /// </summary>
        public string fdInvoiceList { get; set; }
        /// <summary>
        /// 单据标题
        /// </summary>
        public string fd_subject { get; set; }
        /// <summary>
        /// 单据附件
        /// </summary>
        public List<FdOtherFileList> fdOtherFileList { get; set; }
        /// <summary>
        /// 申请人编码
        /// </summary>
        public string fd_claimant_no { get; set; }
        /// <summary>
        /// 费控二维码单据编号
        /// </summary>
        public string fk_ewm { get; set; }
        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string fd_claimant_name { get; set; }
        /// <summary>
        /// 发票附件
        /// </summary>
        public string fdinvoiceFileList { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string fd_supplier_code { get; set; }
        /// <summary>
        /// 申请人部门名称
        /// </summary>
        public string fd_claimant_dept_name { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string fd_status { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public string fd_catagory { get; set; }
        /// <summary>
        /// 单据号
        /// </summary>
        public string fd_number { get; set; }
        /// <summary>
        /// 单据来源
        /// </summary>
        public string fd_sys_flag { get; set; }
        /// <summary>
        /// 财务核定总金额
        /// </summary>
        public double fd_total_pay_money { get; set; }
        /// <summary>
        /// 报销总金额
        /// </summary>
        public double fd_total_approved_money { get; set; }
        /// <summary>
        /// 备用字段1
        /// </summary>
        public string fd_attribute_one { get; set; }
        /// <summary>
        /// 备用字段2
        /// </summary>
        public string fd_attribute_two { get; set; }
        /// <summary>
        /// 单据ID
        /// </summary>
        public string fd_id { get; set; }
        /// <summary>
        /// 凭证号
        /// </summary>
        public List<string> voucherNo { get; set; }
        /// <summary>
        /// 费用归属公司编码
        /// </summary>
        public string fd_company_code { get; set; }
        /// <summary>
        /// 申请人部门编码
        /// </summary>
        public string fd_claimant_dept_no { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string fd_supplier_name { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string fd_content { get; set; }
    }

}
