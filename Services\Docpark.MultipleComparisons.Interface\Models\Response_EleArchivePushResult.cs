﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Response_EleArchivePushResult
    {
        /// <summary>
        /// 返回代码
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 消息代码
        /// </summary>
        public string messageCode { get; set; }
        /// <summary>
        /// 消息
        /// </summary>
        public string message { get; set; }
        /// <summary>
        /// 错误消息
        /// </summary>
        public string errMsg { get; set; }
        /// <summary>
        /// 返回数据
        /// </summary>
        public object data { get; set; }
        /// <summary>
        /// 执行时间戳
        /// </summary>
        public string timestamp { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string manual { get; set; }
    }
}
