using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// API网关服务接口
    /// </summary>
    public interface IApiGatewayService
    {
        /// <summary>
        /// 处理网关请求
        /// </summary>
        Task<ApiGatewayResponse> ProcessRequestAsync(HttpContext context, string apiConfigId);

        /// <summary>
        /// 记录网关日志
        /// </summary>
        Task LogGatewayRequestAsync(ApiGatewayLog log);

        /// <summary>
        /// 获取网关日志
        /// </summary>
        Task<List<ApiGatewayLog>> GetGatewayLogsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null, int limit = 100);

        /// <summary>
        /// 获取网关统计信息
        /// </summary>
        Task<object> GetGatewayStatisticsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 清理过期的网关日志
        /// </summary>
        Task<int> CleanupExpiredGatewayLogsAsync(int olderThanDays = 30);

        /// <summary>
        /// 验证API密钥
        /// </summary>
        Task<bool> ValidateApiKeyAsync(string apiKey, string apiConfigId = null);

        /// <summary>
        /// 获取客户端标识
        /// </summary>
        string GetClientIdentifier(HttpContext context);

        /// <summary>
        /// 构建错误响应
        /// </summary>
        ApiGatewayResponse BuildErrorResponse(int statusCode, string message, string requestId = null);

        /// <summary>
        /// 构建限流响应
        /// </summary>
        ApiGatewayResponse BuildRateLimitResponse(RateLimitResult rateLimitResult, string requestId = null);

        /// <summary>
        /// 构建熔断响应
        /// </summary>
        ApiGatewayResponse BuildCircuitBreakerResponse(CircuitBreakerResult circuitBreakerResult, string requestId = null);
    }

    /// <summary>
    /// API网关响应
    /// </summary>
    public class ApiGatewayResponse
    {
        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 响应头
        /// </summary>
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 响应体
        /// </summary>
        public string Body { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; } = "application/json";

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 目标服务器
        /// </summary>
        public string TargetServer { get; set; }

        /// <summary>
        /// 缓存信息
        /// </summary>
        public string CacheInfo { get; set; }

        /// <summary>
        /// 额外元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
