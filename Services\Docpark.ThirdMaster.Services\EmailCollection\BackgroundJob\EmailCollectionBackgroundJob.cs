﻿using Docpark.ThirdMaster.Services.Models.EmailCollection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Threading;
using System;
using Docpark.ThirdMaster.Services.Backgroundjob;
using DocPark.Workflow.Share;

namespace Docpark.ThirdMaster.Services.EmailCollection.BackgroundJob
{
    /// <summary>
    /// 大陆汽车邮箱采集
    /// </summary>
    [MasterServiceType("continental")]
    public class EmailCollectionBackgroundJob : BackgroundService, IThirdMasterBackgroundJob
    {
        private readonly ILogger<EmailCollectionBackgroundJob> logger;
        private readonly IEmailCollectionService receiptEmailService;
        public EmailCollectionBackgroundJob(ILogger<EmailCollectionBackgroundJob> logger, IEmailCollectionService receiptEmailService)
        {
            this.logger = logger;
            this.receiptEmailService = receiptEmailService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            logger.LogInformation("----------------------------供应商/客户索赔发票 收集服务启动----------------------------");

            stoppingToken.Register(() =>
            {
                logger.LogInformation("----------------------------供应商/客户索赔发票 收集服务停止----------------------------");
            });

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await receiptEmailService.ReceiveEmails(Enum_Collect_Type.SUPPLIER);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "供应商发票接收失败");
                }

                try
                {
                    await receiptEmailService.ReceiveEmails(Enum_Collect_Type.CUSTOMER_CLAIM);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "客户索赔发票接收失败");
                }

                await Task.Delay(1000 * 60 * 10, stoppingToken);
            }

            logger.LogInformation("----------------------------供应商/客户索赔发票 收集服务启动----------------------------");
        }
    }
}
