﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DocPark.MongoDb
{
    public static class MongodbExtions
    {
        public static IServiceCollection UseMongodb(this IServiceCollection services, IConfiguration _configuration)
        {
            services.AddSingleton<IMongodbManager>(s => {
                return new MongodbManager(_configuration);
            });
            return services;
        }
    }
}
