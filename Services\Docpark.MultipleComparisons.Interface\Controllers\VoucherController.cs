﻿using Docpark.HttpClientExtension.IServices;
using Docpark.MultipleComparisons.Interface.Models;
using Docpark.MultipleComparisons.Interface.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VoucherController: ControllerBase
    {
        private readonly IVoucherService _voucherService;

        public VoucherController(IVoucherService voucherService)
        {
            _voucherService = voucherService;
        }

        /// <summary>
        /// 生成记账凭证
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("GenerateVoucherInfo")]
        public async Task<ResponseResult> GenerateVoucherInfo(RequestVoucherDto dto)
        {
            return await _voucherService.GenerateVoucherInfo(dto);
        }

        /// <summary>
        /// 获取当前最大档案号
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetInvoiceData")]
        public async Task<ResponseResult> GetInvoiceData()
        {
            var data = new ResponseResult();
            var result= await _voucherService.GetMaxVoucherNo();
            data.data = result;
            data.count = result;
            return data;
        }
    }
}