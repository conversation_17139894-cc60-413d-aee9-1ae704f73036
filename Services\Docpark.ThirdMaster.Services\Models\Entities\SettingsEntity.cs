﻿using Docpark.HttpClientExtension.Json;
using System;

namespace Docpark.ThirdMaster.Services.Models.Entities
{
    [JsonTable("dl_Settings")]
    public class SettingsEntity
    {
        public string ConfigSettings { get; set; }
        public string Enable { get; set; }
        public DateTime? CreatedTimeFrom { get;set; }
        public DateTime? CreatedTimeTo { get; set; }
        public string ExcludeCompany { get; set; }
    }
}
