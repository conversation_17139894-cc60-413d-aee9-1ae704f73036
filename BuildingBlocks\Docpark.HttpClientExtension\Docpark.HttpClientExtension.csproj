﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Proto\uuid.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Google.Protobuf" Version="3.13.0" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.32.0" />
    <PackageReference Include="Grpc.Net.ClientFactory" Version="2.32.0" />
    <PackageReference Include="Grpc.Tools" Version="2.32.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.9" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.2.22" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DocPark.Commons\DocPark.Commons.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Proto\users.proto" GrpcServices="Client" Link="Proto\users.proto" />
    <Protobuf Include="Proto\uuid.proto" GrpcServices="Client" />
  </ItemGroup>

</Project>
