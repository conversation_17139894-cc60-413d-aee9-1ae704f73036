﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    [JsonTable("MC_BILL_VOUCHER_DATA")]
    /// <summary>
    /// 单据费用及付款凭证接收数据
    /// </summary>
    public class MC_BILL_VOUCHER_DATA
    {
        /// <summary>
        /// 单据mstId
        /// </summary>
        public Guid mstId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 日记账名(日志账名)
        /// </summary>
        public string accounting_name { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public string accounting_date { get; set; }
        /// <summary>
        /// 凭证编号
        /// </summary>
        public string voucher_no { get; set; }
        /// <summary>
        /// 批名
        /// </summary>
        public string batch_name { get; set; }
        /// <summary>
        /// 合计金额贷方
        /// </summary>
        public string credit_total_amount { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public string write_off_status { get; set; }
        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucher_type { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 凭证合计金额
        /// </summary>
        public string voucher_total_amount { get; set; }
        /// <summary>
        /// 凭证审核人工号
        /// </summary>
        public string voucher_approver_job_num { get; set; }
        /// <summary>
        /// 凭证审核人名称
        /// </summary>
        public string voucher_approver_name { get; set; }
        /// <summary>
        /// 会计期间
        /// </summary>
        public string accounting_period { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string voucher_source { get; set; }
        /// <summary>
        /// 合计金额借方
        /// </summary>
        public string debit_total_amount { get; set; }
        /// <summary>
        /// 制单人名称
        /// </summary>
        public string voucher_creator_name { get; set; }
        /// <summary>
        /// 制单人工号
        /// </summary>
        public string voucher_creator_job_num { get; set; }
        /// <summary>
        /// OA流程编码
        /// </summary>
        public string oa_flow_code { get; set; }
        /// <summary>
        /// 摘要详情
        /// </summary>
        public List<summary_details> summary_details { get; set; }
        /// <summary>
        /// 影像数据
        /// </summary>
        public string img_data { get; set; }
        /// <summary>
        /// 文件类型（png/jpg/jpeg/pdf/ofd）
        /// </summary>
        public string img_data_filetype { get; set; }
        /// <summary>
        /// 批次状态
        /// </summary>
        public string batch_status { get; set; }
        /// <summary>
        /// 凭证分类账
        /// </summary>
        public string voucher_ledger { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 是否发送, 0 未发送; 1 已发送
        /// </summary>
        public int ispost { get; set; }
        /// <summary>
        /// OA单据是否发送, 0 未发送; 1 已发送
        /// </summary>
        public int oa_send { get; set; }
        /// <summary>
        /// OA发票是否发送, 0 未发送; 1 已发送
        /// </summary>
        public int inv_send { get; set; }
        /// <summary>
        /// 电子回单是否发送, 0 未发送; 1 已发送
        /// </summary>
        public int nc_send { get; set; }
    }
    /// <summary>
    /// 摘要详情
    /// </summary>
    public class summary_details
    {
        /// <summary>
        /// 摘要
        /// </summary>
        public string summary { get; set; }
        /// <summary>
        /// 科目名称
        /// </summary>
        public string accounting_title { get; set; }
        /// <summary>
        /// 会计科目代码
        /// </summary>
        public string account_code { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string dept_name { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public string dept_code { get; set; }
        /// <summary>
        /// 人员名称
        /// </summary>
        public string user_name { get; set; }
        /// <summary>
        /// 人员编码
        /// </summary>
        public string user_code { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string item_supplier_code { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string item_supplier_name { get; set; }
        /// <summary>
        /// 原币金额
        /// </summary>
        public string original_currency_amount { get; set; }
        /// <summary>
        /// 借方金额
        /// </summary>
        public string debit_amount { get; set; }
        /// <summary>
        /// 贷方金额
        /// </summary>
        public string credit_amount { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string item_company_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string item_company_code { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string item_product_name { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string item_product_code { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string item_project_name { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string item_project_code { get; set; }
        /// <summary>
        /// 内部往来名称
        /// </summary>
        public string item_current_name { get; set; }
        /// <summary>
        /// 内部往来编码
        /// </summary>
        public string item_current_code { get; set; }
    }
}
