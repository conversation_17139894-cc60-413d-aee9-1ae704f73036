<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>thirdparty-integration-secrets</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.32" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.32" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Docpark.ThirdPartyIntegration.Services\Docpark.ThirdPartyIntegration.Services.csproj" />
    <ProjectReference Include="..\Docpark.ThirdPartyIntegration.Domain\Docpark.ThirdPartyIntegration.Domain.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.Commons\DocPark.Commons.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.MongoDb\DocPark.MongoDb.csproj" />
  </ItemGroup>

</Project>
