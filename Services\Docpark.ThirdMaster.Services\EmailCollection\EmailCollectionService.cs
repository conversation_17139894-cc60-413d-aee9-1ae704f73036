﻿using Docpark.ThirdMaster.Services.Models.EmailCollection;
using Docpark.ThirdMaster.Services.Models.Entities;
using Docpark.ThirdMaster.Services.Models.MongoEntities;
using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Commons;
using DocPark.EmailService;
using DocPark.MongoDb;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static GrpcDocpark.UserApi;

namespace Docpark.ThirdMaster.Services.EmailCollection
{
    public class EmailCollectionService : IEmailCollectionService
    {
        private readonly ILogger<EmailCollectionService> logger;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly IDGrpc grpc;
        private readonly IObjectQuery objectQuery;
        private readonly UserApiClient userClient;
        private readonly IDocparkHostService docparkHostService;
        private readonly IDocparkFileService ftpService;
        private readonly IMongodbManager mongodbManager;
        private readonly IEventBus eventBus;
        private readonly IConfiguration configuration;
        private readonly ICaseNoService caseNoService;
        public EmailCollectionService(ILogger<EmailCollectionService> logger, IObjectQuery objectQuery, IDGrpc grpc, UserApiClient userClient, IDocparkFileService ftpService, IHttpClientFactory httpClientFactory, IMongodbManager mongodbManager, IDocparkHostService docparkHostService, IEventBus eventBus, IConfiguration configuration, ICaseNoService caseNoService)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.grpc = grpc;
            this.userClient = userClient;
            this.ftpService = ftpService;
            this.httpClientFactory = httpClientFactory;
            this.mongodbManager = mongodbManager;
            this.docparkHostService = docparkHostService;
            this.eventBus = eventBus;
            this.configuration = configuration;
            this.caseNoService = caseNoService;
        }
        public async Task<(string businessType, string docTypeId)> GetBusinessType(Enum_Collect_Type type)
        {
            if (type == Enum_Collect_Type.SUPPLIER)
            {
                return (await grpc.GetStringAsync("App.SupplierCollect.BusinessType"), await grpc.GetStringAsync("App.SupplierCollect.DocTypeId"));
            }
            else
                return (await grpc.GetStringAsync("App.CustomerClaim.BusinessType"), await grpc.GetStringAsync("App.CustomerClaim.DocTypeId"));
        }
        public async Task<ReceiveEmailInfo> GetReceiveEmailInfo(Enum_Collect_Type type)
        {
            if (type == Enum_Collect_Type.SUPPLIER)//供应商邮箱收集配置
            {
                return new ReceiveEmailInfo()
                {
                    ImapIdComond = await grpc.GetStringAsync("App.SupplierCollect.ImapIdComond"),
                    ImapHost = await grpc.GetStringAsync("App.SupplierCollect.ImapHost"),
                    ImapPort = int.Parse(await grpc.GetStringAsync("App.SupplierCollect.ImapPort")),
                    SmtpHost = await grpc.GetStringAsync("App.SupplierCollect.SmtpHost"),
                    SmtpPort = int.Parse(await grpc.GetStringAsync("App.SupplierCollect.SmtpPort")),
                    Enable = string.Compare(await grpc.GetStringAsync("App.SupplierCollect.Enable"), "true", true) == 0,
                    Account = await grpc.GetStringAsync("App.SupplierCollect.Account"),
                    PassWord = await grpc.GetStringAsync("App.SupplierCollect.PassWord"),
                };
            }
            else//客户索赔邮箱收集配置
            {
                return new ReceiveEmailInfo()
                {
                    ImapIdComond = await grpc.GetStringAsync("App.CustomerClaim.ImapIdComond"),
                    ImapHost = await grpc.GetStringAsync("App.CustomerClaim.ImapHost"),
                    ImapPort = int.Parse(await grpc.GetStringAsync("App.CustomerClaim.ImapPort")),
                    SmtpHost = await grpc.GetStringAsync("App.CustomerClaim.SmtpHost"),
                    SmtpPort = int.Parse(await grpc.GetStringAsync("App.CustomerClaim.SmtpPort")),
                    Enable = string.Compare(await grpc.GetStringAsync("App.CustomerClaim.Enable"), "true", true) == 0,
                    Account = await grpc.GetStringAsync("App.CustomerClaim.Account"),
                    PassWord = await grpc.GetStringAsync("App.CustomerClaim.PassWord"),
                };
            }
        }

        /// <summary>
        /// 获取供应商邮箱
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetSupplierEmails()
        {
            var emails = new List<string>();

            var (data, totalCount) = await objectQuery.GetList<Dictionary<string, object>>("SupplierEmail", new List<Filter>(), new List<Sorter>(), 1, 1000);

            if (totalCount > 0)
            {
                foreach (var item in data)
                {
                    if (item.ContainsKey("Email") && !string.IsNullOrWhiteSpace(item["Email"].ToString()))
                    {
                        emails.Add(item["Email"].ToString());
                    }
                }
            }
            return emails;
        }

        /// <summary>
        /// 邮箱收集
        /// </summary>
        /// <param name="type">1供应商 2客户索赔</param>
        /// <returns></returns>
        public async Task ReceiveEmails(Enum_Collect_Type type)
        {
            var historys = new List<Receipt_ReceiveEmail_History>();

            try
            {
                /*
                //供应商公邮
                //1-每月最后一天11点起到当天晚上24点，停止供应商公邮自动抓取发票；
                //2-次月第一天0点恢复抓取任务；
                if (type == Enum_Collect_Type.SUPPLIER)
                {
                    var now = DateTime.Now;
                    //本月最后一天23:59:59
                    var end = DateTime.Parse(now.ToString("yyyy-MM-01 00:00:00")).AddMonths(1).AddSeconds(-1);
                    //本月最后一天11点
                    var start = DateTime.Parse(end.ToString("yyyy-MM-dd 11:00:00"));
                    if (now >= start && now <= end)
                    {
                        logger.LogInformation($"该时间段暂停收件({start:yyyy-MM-dd HH:mm:ss}-->{end:yyyy-MM-dd HH:mm:ss})");
                        return;
                    }
                }
                */

                var (businessType, docTypeId) = await GetBusinessType(type);
                var info = await GetReceiveEmailInfo(type);
                if (!info.Enable)
                    return;

                #region 接收邮件
                var mkService = new MailKitService(logger, httpClientFactory)
                {
                    Account = info.Account,
                    PassWord = info.PassWord,
                    ImapIdComond = info?.ImapIdComond?.ToLower() == "true",
                    ImapHost = info.ImapHost,
                    ImapPort = info.ImapPort,
                    SmtpHost = info.SmtpHost,
                    SmtpPort = info.SmtpPort
                };
                var result = await mkService.ReceiveEmails(configuration);
                #endregion

                #region 上传文件
                var bacthNo = $"{DateTime.Now:yyyyMMdd}{new Random().Next(100, 999)}";
                var saveDatas = new List<EmailScanDataDto>();
                foreach (var item in result)
                {
                    var history = new Receipt_ReceiveEmail_History
                    {
                        From = item.SendAddress,
                        FromName = item.SendlName,
                        FileCount = item?.Attachments?.Count ?? 0,
                        ReceiveDate = DateTime.Now,
                        SubTitle = item.Subject,
                        Status = item?.Attachments?.Count > 0,
                        Message = item?.Attachments?.Count > 0 ? string.Empty : "该邮件没有附件",
                        CollectType = (int)type,
                    };
                    if (item?.Attachments?.Count > 0)
                    {
                        var dataItem = new EmailScanDataDto()
                        {
                            MstId = grpc.NewGuid(),
                            BillNo = $"{DateTime.Now:yyyyMMdd}{new Random().Next(1000, 9909)}",
                            Data = new List<EmailFileDocumentDto>(),
                        };

                        var attachments = GroupAttachments(item.Attachments);
                        //无效的发票
                        var invalidAttachments = attachments.Where(w => string.IsNullOrWhiteSpace(w.FilePath)).ToList();
                        foreach (var file in invalidAttachments)
                        {
                            var names = file.Children.Select(w => w.FileName).ToList();
                            history.Message += $"发票号码{file.FPHM}下没有主影像件，该发票号码下有附件数{names.Count}；附件名称为：{string.Join('|', names)}";
                        }
                        //有效发票
                        var validAttachments = attachments.Where(w => !string.IsNullOrWhiteSpace(w.FilePath)).ToList();
                        foreach (var file in validAttachments)
                        {
                            var (status, dto) = await UploadFile(file, history);
                            if (status)
                            {
                                var mstId = grpc.NewGuid();
                                var dData = new EmailFileDocumentDto()
                                {
                                    MstId = mstId,
                                    DocId = dto.documentID,
                                    SendAddress = item.SendAddress,
                                    Subject = item.Subject,
                                    Files = new List<FormFileDto>(),
                                };
                                foreach (var childFile in file.Children)
                                {
                                    var (childStatus, childDto) = await UploadFile(childFile, history, file.FPHM);
                                    if (childStatus)
                                    {
                                        dData.Files.Add(childDto);
                                    }
                                }
                                dataItem.Data.Add(dData);
                            }
                        }
                        if (dataItem.Data.Count > 0)
                        {
                            saveDatas.Add(dataItem);
                        }
                    }
                    historys.Add(history);
                }
                #endregion

                #region 保存数据
                if (saveDatas.Count > 0)
                {
                    //获取提交用户
                    var account = await grpc.GetStringAsync(ConstSettings.SystemAccount);
                    var user = await userClient.FindByNameOrEmailAsync(new GrpcDocpark.UserRequest() { UserName = account });
                    //获取字段配置
                    var fields = await docparkHostService.GetFields(docTypeId);
                    var docView = fields.Find(o => o.PropType == "documentView");
                    if (docView == null)
                    {
                        logger.LogError($"文档表单{docTypeId},未配置影像预览控件");
                        return;
                    }
                    docView.Identity = string.IsNullOrWhiteSpace(docView.Identity) ? "tmp_documentView" : docView.Identity;
                    //附件控件
                    var docFile = fields.Find(o => o.PropType == "file" && o.CustomType == "attachments");
                    if (docFile == null)
                        docFile = fields.Find(o => o.PropType == "file");
                    if (docFile != null)
                        docFile.Identity = string.IsNullOrWhiteSpace(docFile.Identity) ? "tmp_documentFile" : docFile.Identity;

                    //保存影像文件
                    var saveFileInputs = new List<(string, string)>();
                    var formDatas = new List<Dictionary<string, object>>();
                    foreach (var item in saveDatas)
                    {
                        var caseNo = await caseNoService.Get("YXCJ");
                        item.BillNo = caseNo;
                        var i = 1;
                        foreach (var document in item.Data)
                        {
                            saveFileInputs.Add((document.MstId.ToString(), document.DocId));
                            var itemData = new Dictionary<string, object>()
                            {
                                ["MstId"] = document.MstId,
                                ["email_address"] = document.SendAddress,
                                ["dlsjyx"] = document.SendAddress,
                                ["email_subject"] = document.Subject,
                                ["businesstypeid"] = CommonUtil.ConvertObjectIdToGuid(businessType),
                                ["sys_bill_id"] = item.MstId.ToString(),
                                ["collect_type"] = type.ToString(),
                                [docView.Identity] = document.DocId,
                                ["CaseNo"] = caseNo + "_" + i.ToString().PadLeft(4, '0'),
                                ["TaskCreatedTime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                ["TaskModifiedTime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                            };
                            if (docFile != null && document.Files?.Count > 0)
                            {
                                itemData[docFile.Identity] = document.Files;
                            }
                            formDatas.Add(itemData);
                            i++;
                        }
                    }
                    var saveResult = await objectQuery.BulkCreateOrUpdate(docTypeId, formDatas, fields);
                    if (saveResult)
                    {
                        //保存业务信息
                        var scans = saveDatas.Select(w => new ScanningEntity(w.MstId, businessType, bacthNo, w.BillNo)).ToList();
                        if (await objectQuery.BulkCreateOrUpdate(scans))
                        {
                            var businessTypeId = CommonUtil.ConvertObjectIdToGuid(businessType);
                            var objId = CommonUtil.ConvertObjectIdToGuid(docTypeId);
                            //业务信息与文件的关系
                            var documentRelationships = formDatas.Select(s => new Dictionary<string, object>()
                            {
                                ["BusinessTypeId"] = businessTypeId,
                                ["ObjId"] = objId,
                                ["ObjUniqueId"] = s[docView.Identity],
                                ["RelationId"] = "SCANDOCUMENT",
                                ["TargetObjInstanceId"] = s["sys_bill_id"],
                                ["CreatorUserId"] = user.Id
                            });
                            //文件与元数据的关系
                            var relationships = formDatas.Select(s => new Dictionary<string, object>()
                            {
                                ["BusinessTypeId"] = businessTypeId,
                                ["ObjId"] = objId,
                                ["ObjUniqueId"] = s["MstId"],
                                ["RelationId"] = "SCANMETADATA",
                                ["TargetObjInstanceId"] = s[docView.Identity],
                                ["CreatorUserId"] = user.Id
                            });
                            var relationshipsList = documentRelationships.Concat(relationships).ToList();
                            await docparkHostService.SaveRelationships(relationshipsList);
                            //发布消息
                            PublishMessage(user.Id, businessType, docTypeId, saveFileInputs);
                        }
                        else
                        {
                            historys.ForEach(o => { o.Status = false; o.Message = $"保存业务信息失败；{o.Message}"; });
                        }
                    }
                    else
                    {
                        historys.ForEach(o => { o.Status = false; o.Message = $"保存文件失败；{o.Message}"; });
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                historys.ForEach(o => { o.Status = false; o.Message = $"保存文件失败:{ex.Message}；{o.Message}"; });
                logger.LogError(ex, $"发票接收失败{type}。");
            }
            finally
            {
                if (historys.Count > 0)
                {
                    var coll = mongodbManager.GetCollection<Receipt_ReceiveEmail_History>("Receipt_ReceiveEmail_History");
                    await coll.InsertManyAsync(historys);
                }
            }
        }
        /// <summary>
        /// 发布信息,执行后续的识别和验真流程
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="BusinessType"></param>
        /// <param name="documentTypeId"></param>
        /// <param name="saveFileInputs"></param>
        private void PublishMessage(string userId, string BusinessType, string documentTypeId, List<(string mstId, string documentId)> saveFileInputs)
        {
            foreach (var item in saveFileInputs)
            {
                var documentRequest = new
                {
                    requestId = Guid.NewGuid(),
                    code = "UploadFile",
                    name = "上传文件",
                    documentID = item.documentId,
                    documentType = documentTypeId,
                    messageType = string.Empty,
                    item.mstId,
                    businessType = BusinessType,
                    scanUser = userId,
                    ownUser = userId,
                    data = new Dictionary<string, string>()
                };

                string jsonContent = JsonConvert.SerializeObject(documentRequest);
                var publishMsg = JsonConvert.DeserializeObject<WorkflowMQContent>(jsonContent);

                var msg = JsonConvert.SerializeObject(publishMsg);
                var publishEventData = new DocumentServiceMQEventData(msg);
                eventBus.Publish(publishEventData);
                logger.LogInformation($"方法TaskMQPublish=>input:{msg};");
            }
        }
        /// <summary>
        /// 根据规则分组
        /// 邮件支持XML；区分邮件中不同类型文件：根据文件名开头符合dzfp_20位发票号码或20位发票号码_的规则，取出发票号码信息，并结合文件后缀，区分出XML、ofd、PDF文件，同一张发票只存pdf文件不留ofd，XML文件作为附件存储
        /// </summary>
        /// <param name="attachments"></param>
        /// <returns></returns>
        private List<AttachmentsTree> GroupAttachments(List<Attachments> attachments)
        {
            var result = new List<AttachmentsTree>();

            foreach (var attachment in attachments)
            {
                var invNo = string.Empty;
                var fileName = attachment.FileName?.ToLower();
                var ext = Path.GetExtension(fileName).Replace(".", string.Empty).ToLower();

                if (!string.IsNullOrEmpty(fileName))
                {
                    if (fileName.StartsWith("dzfp_"))
                    {
                        var tmp = fileName.Split('_', '.')[1];
                        if (tmp.Length == 20 && Regex.IsMatch(tmp, @"^[0-9]+$"))
                        {
                            invNo = tmp;
                        }
                    }
                    else
                    {
                        var tmp = fileName.Split('_', '.')[0];
                        if (tmp.Length == 20 && Regex.IsMatch(tmp, @"^[0-9]+$"))
                        {
                            invNo = tmp;
                        }
                    }
                }
                if (string.IsNullOrWhiteSpace(invNo))
                {
                    if (ext == "pdf")
                        result.Add(new AttachmentsTree(attachment.FileName, attachment.FilePath));
                }
                else
                {
                    var m = result.Where(w => w.FPHM == invNo).FirstOrDefault();
                    if (m == null)
                    {
                        if (ext == "pdf")
                        {
                            result.Add(new AttachmentsTree(invNo, attachment.FileName, attachment.FilePath));
                        }
                        else
                        {
                            var att = new AttachmentsTree()
                            {
                                FPHM = invNo
                            };
                            att.Children.Add(new AttachmentsTree(attachment.FileName, attachment.FilePath));
                            result.Add(att);
                        }
                    }
                    else
                    {
                        if (string.IsNullOrWhiteSpace(m.FilePath) && ext == "pdf")
                        {
                            m.FileName = attachment.FileName;
                            m.FilePath = attachment.FilePath;
                        }
                        else
                        {
                            m.Children.Add(new AttachmentsTree(attachment.FileName, attachment.FilePath));
                        }
                    }
                }
            }
            return result;
        }

        private async Task<(bool status, FormFileDto dto)> UploadFile(AttachmentsTree file, Receipt_ReceiveEmail_History history, string fphm = "")
        {
            if (File.Exists(file.FilePath))
            {
                var bytes = File.ReadAllBytes(file.FilePath);

                var resultUploadFile = await ftpService.UploadFile(new KeyValuePair<string, byte[]>(file.FileName, bytes));
                if (resultUploadFile.Code == ResultCode.Success)
                {
                    File.Delete(file.FilePath);

                    var dto = new FormFileDto()
                    {
                        documentID = resultUploadFile.Result.Data,
                        name = file.FileName,
                        size = bytes.Length,
                        type = Path.GetExtension(file.FileName).Replace(".", string.Empty).ToLower(),
                        thumbUrl = ""
                    };
                    return (true, dto);
                }
                else
                {
                    history.Status = false;
                    if (string.IsNullOrWhiteSpace(fphm))
                        history.Message = $"文件{file.FileName}上传至FTP失败，FilePath:{file.FilePath},Bytes:{bytes.Length}";
                    else
                        history.Message = $"发票号码${fphm}的附件{file.FileName}上传至FTP失败，FilePath:{file.FilePath},Bytes:{bytes.Length}";
                }
            }
            else
            {
                history.Status = false;
                if (string.IsNullOrWhiteSpace(fphm))
                    history.Message += $"文件{file.FileName}不存在，FilePath:{file.FilePath}；";
                else
                    history.Message = $"发票号码${fphm}的附件{file.FileName}不存在，FilePath:{file.FilePath}；";
            }
            return (false, null);
        }

        public Task<ResultDto> MailSignIn()
        {
            throw new System.NotImplementedException();
        }
    }
}
