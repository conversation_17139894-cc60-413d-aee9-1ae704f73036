using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// API响应数据实体
    /// </summary>
    public class ApiResponseData
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称
        /// </summary>
        [StringLength(100)]
        public string ApiName { get; set; }

        /// <summary>
        /// 执行日志ID
        /// </summary>
        public string ExecutionLogId { get; set; }

        /// <summary>
        /// 原始响应数据
        /// </summary>
        public string RawResponseData { get; set; }

        /// <summary>
        /// 解析后的JSON数据
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument ParsedData { get; set; }

        /// <summary>
        /// 数据哈希值（用于去重）
        /// </summary>
        [StringLength(64)]
        public string DataHash { get; set; }

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 响应头信息
        /// </summary>
        public string ResponseHeaders { get; set; }

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public long DataSize { get; set; }

        /// <summary>
        /// 记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsProcessed { get; set; } = false;

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 过期时间（用于数据清理）
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 标签（用于分类和查询）
        /// </summary>
        public string[] Tags { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }
    }
}
