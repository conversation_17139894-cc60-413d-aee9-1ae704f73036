﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class ListResultDto<T>
    {
        public ItemModel Result { get; set; }

        public class ItemModel
        {
            private IReadOnlyList<T> _items;

            /// <summary>List of items.</summary>
            public IReadOnlyList<T> Items
            {
                get => this._items ?? (this._items = (IReadOnlyList<T>) new List<T>());
                set => this._items = value;
            }
        }

        /// <summary>
        /// Creates a new <see cref="T:Abp.Application.Services.Dto.ListResultDto`1" /> object.
        /// </summary>
        public ListResultDto()
        {
        }

        /// <summary>
        /// Creates a new <see cref="T:Abp.Application.Services.Dto.ListResultDto`1" /> object.
        /// </summary>
        /// <param name="items">List of items</param>
        public ListResultDto(IReadOnlyList<T> items) => this.Result.Items = items;
    }
}
