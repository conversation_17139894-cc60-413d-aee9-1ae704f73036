﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{

    public class Lines_Custom
    {
        /// <summary>
        /// 产品编码
        /// </summary>
        public string productCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }
    }

    public class Lines
    {
        /// <summary>
        /// 部门名称
        /// </summary>
        public string deptName { get; set; }
        /// <summary>
        /// 往来单位名称
        /// </summary>
        public string transactionUnitName { get; set; }
        /// <summary>
        /// 员工编码
        /// </summary>
        public string employeeCode { get; set; }
        /// <summary>
        /// 客商名称
        /// </summary>
        public string merchantName { get; set; }
        /// <summary>
        /// 摘要
        /// </summary>
        public string voucherAbstract { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string projectCode { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 科目名称
        /// </summary>
        public string subjectName { get; set; }
        /// <summary>
        /// 员工名称
        /// </summary>
        public string employeeName { get; set; }
        /// <summary>
        /// 客商编码
        /// </summary>
        public string merchantCode { get; set; }
        /// <summary>
        /// 往来单位编码
        /// </summary>
        public string transactionUnitCode { get; set; }
        /// <summary>
        /// 自定义产品编码
        /// </summary>
        public Lines_Custom custom { get; set; }
        /// <summary>
        /// 借方金额
        /// </summary>
        public double debitAmount { get; set; }
        /// <summary>
        /// 原币金额
        /// </summary>
        public double originalAmount { get; set; }
        /// <summary>
        /// 贷方金额
        /// </summary>
        public double creditAmount { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string projectName { get; set; }
        /// <summary>
        /// 科目代码
        /// </summary>
        public string subjectCode { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public string deptCode { get; set; }
    }

    public class Custom
    {
        /// <summary>
        /// 批名
        /// </summary>
        public string batchName { get; set; }
        /// <summary>
        /// OA流程编码
        /// </summary>
        public string oaprocesscode { get; set; }
        /// <summary>
        /// 日记账名
        /// </summary>
        public string journalName { get; set; }
        /// <summary>
        /// 分类账
        /// </summary>
        public string classificationZha { get; set; }
        /// <summary>
        /// 制单人工号
        /// </summary>
        public string voucherlabor { get; set; }
        /// <summary>
        /// 日志账名
        /// </summary>
        public string logZha { get; set; }
        /// <summary>
        /// 合计金额借方
        /// </summary>
        public string totalDebit { get; set; }
        /// <summary>
        /// 合计金额贷方
        /// </summary>
        public string totallCredit { get; set; }
        /// <summary>
        /// 合计金额大写
        /// </summary>
        public string totalAs { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public string reversalStatus { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string source { get; set; }
        /// <summary>
        /// 审核人工号
        /// </summary>
        public string auditabor { get; set; }
        /// <summary>
        /// 批状态
        /// </summary>
        public string batchStatus { get; set; }
    }

    public class Request_Voucher_Archive_Data
    {
        /// <summary>
        /// 凭证期间(yyyy-MM格式)
        /// </summary>
        public string voucherPeriod { get; set; }
        /// <summary>
        /// 制单人
        /// </summary>
        public string preparedBy { get; set; }
        /// <summary>
        /// 凭证日期(yyyy-MM-dd格式)
        /// </summary>
        public string voucherDate { get; set; }
        /// <summary>
        /// 业务实体名
        /// </summary>
        public string businessEntityName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<Lines> lines { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Custom custom { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>
        public string reviewer { get; set; }
        /// <summary>
        /// 凭证号
        /// </summary>
        public string voucherNo { get; set; }
        /// <summary>
        /// 业务说明
        /// </summary>
        public string buzDeclare { get; set; }
        /// <summary>
        /// 业务实体码
        /// </summary>
        public string businessEntityCode { get; set; }
        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucherType { get; set; }
    }


}
