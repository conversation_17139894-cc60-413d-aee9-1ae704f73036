using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 限流服务接口
    /// </summary>
    public interface IRateLimitService
    {
        /// <summary>
        /// 创建限流规则
        /// </summary>
        Task<string> CreateRateLimitRuleAsync(RateLimitRule rule);

        /// <summary>
        /// 更新限流规则
        /// </summary>
        Task<bool> UpdateRateLimitRuleAsync(string id, RateLimitRule rule);

        /// <summary>
        /// 获取限流规则
        /// </summary>
        Task<RateLimitRule> GetRateLimitRuleAsync(string id);

        /// <summary>
        /// 获取所有限流规则
        /// </summary>
        Task<List<RateLimitRule>> GetAllRateLimitRulesAsync(bool enabledOnly = false);

        /// <summary>
        /// 删除限流规则
        /// </summary>
        Task<bool> DeleteRateLimitRuleAsync(string id);

        /// <summary>
        /// 检查是否允许请求
        /// </summary>
        Task<RateLimitResult> IsRequestAllowedAsync(string apiConfigId, string clientIdentifier, string requestPath = null);

        /// <summary>
        /// 记录请求
        /// </summary>
        Task RecordRequestAsync(string apiConfigId, string clientIdentifier, bool isSuccess = true);

        /// <summary>
        /// 获取限流统计信息
        /// </summary>
        Task<object> GetRateLimitStatisticsAsync(string apiConfigId = null, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 重置限流计数器
        /// </summary>
        Task<bool> ResetRateLimitCounterAsync(string apiConfigId, string clientIdentifier);

        /// <summary>
        /// 获取客户端限流状态
        /// </summary>
        Task<object> GetClientRateLimitStatusAsync(string apiConfigId, string clientIdentifier);

        /// <summary>
        /// 清理过期的限流数据
        /// </summary>
        Task<int> CleanupExpiredRateLimitDataAsync(int olderThanHours = 24);
    }
}