﻿using ICSharpCode.SharpZipLib.Zip;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.InteropServices.ComTypes;
using System.Web;
using System.Web.Http;
using System.Web.UI.WebControls.WebParts;

namespace Docpark.Continental.WebApi.Controllers
{
    public class TiffController : ApiController
    {
        [HttpPost]
        [Route("api/tiff/tiftozip")]
        [AllowAnonymous]
        public HttpResponseMessage TifToZip()
        {
            var file = HttpContext.Current.Request.Files[0];

            var image = Image.FromStream(file.InputStream);
            var count = image.GetFrameCount(FrameDimension.Page);

            var buffer = new byte[6500];
            var zip = new MemoryStream();
            using (var zipStream = new ZipOutputStream(zip))
            {
                for (int i = 0; i < count; i++)
                {
                    image.SelectActiveFrame(FrameDimension.Page, i);

                    using (var imageStream = new MemoryStream())
                    {
                        image.Save(imageStream, ImageFormat.Jpeg);
                        imageStream.Position = 0;

                        var tempZipEntry = new ZipEntry($"{i}.jpg") { IsUnicodeText = true, DateTime = DateTime.Now };

                        zipStream.PutNextEntry(tempZipEntry);
                        while (true)
                        {
                            var readCount = imageStream.Read(buffer, 0, buffer.Length);
                            if (readCount > 0)
                                zipStream.Write(buffer, 0, readCount);
                            else
                                break;
                        }
                        zipStream.Flush();
                    }
                }
                zipStream.Finish();
                zip.Position = 0;
            }

            var msg = new HttpResponseMessage();
            msg.Content = new ByteArrayContent(zip.ToArray());
            msg.StatusCode = System.Net.HttpStatusCode.OK;
            msg.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");
            msg.Content.Headers.ContentDisposition.FileName = $"{Guid.NewGuid()}.zip";
            return msg;
        }

        [HttpPost]
        [Route("api/tiff/tiftojpg")]
        [AllowAnonymous]
        public HttpResponseMessage TifToJpg()
        {
            var file = HttpContext.Current.Request.Files[0];

            var image = Image.FromStream(file.InputStream);
            var count = image.GetFrameCount(FrameDimension.Page);

            byte[] bytes = null;
            for (int i = 0; i < count; i++)
            {
                image.SelectActiveFrame(FrameDimension.Page, i);
                using (var imageStream = new MemoryStream())
                {
                    image.Save(imageStream, ImageFormat.Jpeg);
                    bytes=imageStream.ToArray();
                }
                break;
            }

            var msg = new HttpResponseMessage();
            msg.Content = new ByteArrayContent(bytes);
            msg.StatusCode = System.Net.HttpStatusCode.OK;
            msg.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");
            msg.Content.Headers.ContentDisposition.FileName = $"{Guid.NewGuid()}.jpg";
            return msg;
        }
    }
}
