﻿using Docpark.HttpClientExtension.IServices.Dto;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IObjectQuery
    {


        /// <summary>
        /// 根据MstId获取数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="mstId"></param>
        /// <returns></returns>
        Task<T> Get<T>(Guid mstId);

        /// <summary>
        /// 根据虚拟表实体新增或者更新
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="mstId"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<bool> CreateOrUpdate<T>(Guid mstId, T entity);

        Task<bool> CreateOrUpdate(string documentTypeId, Guid mstId, Dictionary<string, object> entity);

        Task<bool> BulkCreateOrUpdate<T>(List<T> Entity);

        Task<bool> BulkCreateOrUpdate<T>(string documentTypeId, List<T> Entity, List<DocFormField> fields = null);
        /// <summary>
        /// 根据tableName新增或者更新
        /// </summary>
        /// <typeparam name="T">Dictionary<string,object> 或者 Entity </string></typeparam>
        /// <param name="tableName"></param>
        /// <param name="mstId"></param>
        /// <param name="entity">字典类型：Dictionary<string,string></param>
        /// <returns></returns>
        Task<bool> CreateOrUpdate<T>(string tableName, Guid mstId, T entity);

        /// <summary>
        /// 查询语句
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetList<T>(List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize);

        /// <summary>
        /// 查询
        /// </summary>
        /// <typeparam name="T">Dictionary<string,object> 或者 Entity</typeparam>
        /// <param name="tableName"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetList<T>(string tableName, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize);

        /// <summary>
        /// 查询
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="mstIds"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetList<T>(List<Guid> mstIds, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize);

        /// <summary>
        /// 查询
        /// </summary>
        /// <typeparam name="T">Dictionary<string,object> 或者 Entity</typeparam>
        /// <param name="tableName"></param>
        /// <param name="mstIds"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetList<T>(string tableName, List<Guid> mstIds, List<Filter> filters, List<Sorter> sorts, int pageIndex, int pageSize);

        /// <summary>
        /// 联合查询
        /// </summary>
        /// <typeparam name="T">Entity</typeparam>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetUnionList<T>(List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize);
        /// <summary>
        /// 联合查询
        /// </summary>
        /// <typeparam name="T">Entity</typeparam>
        /// <param name="mstIds"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetUnionList<T>(List<Guid> mstIds, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize);
        /// <summary>
        /// 联合查询
        /// </summary>
        /// <typeparam name="T">Dictionary<string,object> 或者 Entity</typeparam>
        /// <param name="tableName"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetUnionList<T>(string tableName, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize);
        /// <summary>
        /// 联合查询
        /// </summary>
        /// <typeparam name="T">Dictionary<string,object> 或者 Entity</typeparam>
        /// <param name="tableName"></param>
        /// <param name="mstIds"></param>
        /// <param name="filters"></param>
        /// <param name="sorts"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<(List<T> data, int totalCount)> GetUnionList<T>(string tableName, List<Guid> mstIds, List<List<Filter>> filters, List<Sorter> sorts, int pageIndex, int pageSize);

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="mstIds"></param>
        /// <returns></returns>
        Task<bool> RemoveAll(List<Guid> mstIds);

        /// <summary>
        /// 通过文本类型标识获取对应的文档类型ID
        /// </summary>
        /// <param name="tableName">文档类型标识</param>
        /// <returns>返回文档类型ID</returns>
        Task<string> GetObjectId(string tableName);

        Task<int> Count<T>(List<Filter> filters, List<Guid> mstIds = null);
        Task<int> Count(string tableName, List<Filter> filters, List<Guid> mstIds = null);
        Task<int> Counts(string tableName, List<List<Filter>> filters, List<Guid> mstIds = null);
        Task<int> Counts<T>(List<List<Filter>> filters, List<Guid> mstIds = null);


    }
}
