@{
    Layout = "_Layout";
}

<!-- 页面标题 -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">告警管理</h1>
    <div>
        <button type="button" class="btn btn-primary btn-sm" onclick="showCreateAlertRuleModal()">
            <i class="fas fa-plus fa-sm text-white-50"></i> 创建告警规则
        </button>
        <button type="button" class="btn btn-warning btn-sm" onclick="evaluateAllRules()">
            <i class="fas fa-search fa-sm text-white-50"></i> 评估规则
        </button>
        <button type="button" class="btn btn-success btn-sm" onclick="refreshAlertData()">
            <i class="fas fa-sync fa-sm text-white-50"></i> 刷新
        </button>
    </div>
</div>

<!-- 告警状态概览 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            活跃告警
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-alerts">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            严重告警
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="critical-alerts">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-fire fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            告警规则
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="alert-rules-count">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cogs fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            今日告警
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-alerts">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 活跃告警列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">活跃告警</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow">
                <a class="dropdown-item" href="#" onclick="acknowledgeAllAlerts()">全部确认</a>
                <a class="dropdown-item" href="#" onclick="exportAlerts()">导出告警</a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="active-alerts-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>严重程度</th>
                        <th>标题</th>
                        <th>API名称</th>
                        <th>告警类型</th>
                        <th>告警次数</th>
                        <th>首次告警</th>
                        <th>最后告警</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="active-alerts-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 告警规则列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">告警规则</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="alert-rules-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>规则名称</th>
                        <th>API名称</th>
                        <th>告警类型</th>
                        <th>严重程度</th>
                        <th>评估间隔</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="alert-rules-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 告警历史 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">告警历史</h6>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-3">
                <select class="form-control" id="severity-filter">
                    <option value="">所有严重程度</option>
                    <option value="1">信息</option>
                    <option value="2">警告</option>
                    <option value="3">错误</option>
                    <option value="4">严重</option>
                    <option value="5">致命</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="start-date" placeholder="开始日期">
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="end-date" placeholder="结束日期">
            </div>
            <div class="col-md-3">
                <button class="btn btn-primary" onclick="filterAlertHistory()">筛选</button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered" id="alert-history-table" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>告警ID</th>
                        <th>标题</th>
                        <th>严重程度</th>
                        <th>API名称</th>
                        <th>首次告警</th>
                        <th>解决时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="alert-history-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建告警规则模态框 -->
<div class="modal fade" id="createAlertRuleModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建告警规则</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createAlertRuleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="ruleName">规则名称</label>
                                <input type="text" class="form-control" id="ruleName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="ruleApiConfigId">API配置</label>
                                <select class="form-control" id="ruleApiConfigId" name="apiConfigId">
                                    <option value="">全局规则</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="ruleDescription">规则描述</label>
                        <textarea class="form-control" id="ruleDescription" name="description" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="alertType">告警类型</label>
                                <select class="form-control" id="alertType" name="alertType" required>
                                    <option value="ResponseTime">响应时间</option>
                                    <option value="Availability">可用性</option>
                                    <option value="ErrorRate">错误率</option>
                                    <option value="HealthCheck">健康检查</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="severity">严重程度</label>
                                <select class="form-control" id="severity" name="severity" required>
                                    <option value="1">信息</option>
                                    <option value="2">警告</option>
                                    <option value="3" selected>错误</option>
                                    <option value="4">严重</option>
                                    <option value="5">致命</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="threshold">阈值</label>
                                <input type="number" class="form-control" id="threshold" name="threshold" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="evaluationInterval">评估间隔（分钟）</label>
                                <input type="number" class="form-control" id="evaluationInterval" name="evaluationIntervalMinutes" value="5" min="1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="triggerThreshold">触发次数阈值</label>
                                <input type="number" class="form-control" id="triggerThreshold" name="triggerThreshold" value="1" min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="silencePeriod">静默期（分钟）</label>
                                <input type="number" class="form-control" id="silencePeriod" name="silencePeriodMinutes" value="60" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>通知渠道</label>
                        <div class="form-check-inline">
                            <input type="checkbox" class="form-check-input" id="emailNotification" value="1">
                            <label class="form-check-label" for="emailNotification">邮件</label>
                        </div>
                        <div class="form-check-inline">
                            <input type="checkbox" class="form-check-input" id="smsNotification" value="2">
                            <label class="form-check-label" for="smsNotification">短信</label>
                        </div>
                        <div class="form-check-inline">
                            <input type="checkbox" class="form-check-input" id="webhookNotification" value="6" checked>
                            <label class="form-check-label" for="webhookNotification">Webhook</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="isEnabled" name="isEnabled" checked>
                            <label class="form-check-label" for="isEnabled">启用规则</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createAlertRule()">创建</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        loadAlertOverview();
        loadActiveAlerts();
        loadAlertRules();
        loadAlertHistory();
        loadApiConfigs();
        
        // 每30秒刷新一次活跃告警
        setInterval(function() {
            loadAlertOverview();
            loadActiveAlerts();
        }, 30000);
    });

    function loadAlertOverview() {
        // 加载告警概览数据
        callAPI('/api/Alert/statistics')
            .done(function(data) {
                $('#active-alerts').text(data.activeAlerts || 0);
                $('#critical-alerts').text(data.criticalAlerts || 0);
                $('#today-alerts').text(data.todayAlerts || 0);
            })
            .fail(function() {
                // 使用模拟数据
                $('#active-alerts').text('3');
                $('#critical-alerts').text('1');
                $('#today-alerts').text('12');
            });

        callAPI('/api/Alert/rules')
            .done(function(data) {
                $('#alert-rules-count').text(data.length || 0);
            })
            .fail(function() {
                $('#alert-rules-count').text('5');
            });
    }

    function loadActiveAlerts() {
        callAPI('/api/Alert/active')
            .done(function(data) {
                var html = '';
                data.forEach(function(alert) {
                    var severityBadge = getSeverityBadge(alert.severity);
                    var statusBadge = alert.isAcknowledged ? 
                        '<span class="badge badge-warning">已确认</span>' : 
                        '<span class="badge badge-danger">未确认</span>';
                    
                    html += `
                        <tr>
                            <td>${severityBadge}</td>
                            <td>${alert.title}</td>
                            <td>${alert.apiName || 'N/A'}</td>
                            <td>${alert.alertType}</td>
                            <td>${alert.alertCount}</td>
                            <td>${formatDateTime(alert.firstAlertTime)}</td>
                            <td>${formatDateTime(alert.lastAlertTime)}</td>
                            <td>${statusBadge}</td>
                            <td>
                                ${!alert.isAcknowledged ? 
                                    `<button class="btn btn-sm btn-warning" onclick="acknowledgeAlert('${alert.alertId}')">确认</button>` : ''}
                                <button class="btn btn-sm btn-success" onclick="resolveAlert('${alert.alertId}')">解决</button>
                                <button class="btn btn-sm btn-info" onclick="viewAlertDetails('${alert.id}')">详情</button>
                            </td>
                        </tr>
                    `;
                });
                $('#active-alerts-tbody').html(html);
            })
            .fail(function() {
                // 使用模拟数据
                var html = `
                    <tr>
                        <td><span class="badge badge-danger">严重</span></td>
                        <td>API响应时间过长</td>
                        <td>用户数据API</td>
                        <td>ResponseTime</td>
                        <td>3</td>
                        <td>2023-12-01 10:25:00</td>
                        <td>2023-12-01 10:30:00</td>
                        <td><span class="badge badge-danger">未确认</span></td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="acknowledgeAlert('alert_001')">确认</button>
                            <button class="btn btn-sm btn-success" onclick="resolveAlert('alert_001')">解决</button>
                            <button class="btn btn-sm btn-info" onclick="viewAlertDetails('alert_001')">详情</button>
                        </td>
                    </tr>
                `;
                $('#active-alerts-tbody').html(html);
            });
    }

    function loadAlertRules() {
        callAPI('/api/Alert/rules')
            .done(function(data) {
                var html = '';
                data.forEach(function(rule) {
                    var statusBadge = rule.isEnabled ? 
                        '<span class="badge badge-success">启用</span>' : 
                        '<span class="badge badge-secondary">禁用</span>';
                    var severityBadge = getSeverityBadge(rule.severity);
                    
                    html += `
                        <tr>
                            <td>${rule.name}</td>
                            <td>${rule.apiName || '全局'}</td>
                            <td>${rule.alertType}</td>
                            <td>${severityBadge}</td>
                            <td>${rule.evaluationIntervalMinutes}分钟</td>
                            <td>${statusBadge}</td>
                            <td>${formatDateTime(rule.createdAt)}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="evaluateRule('${rule.id}')">评估</button>
                                <button class="btn btn-sm btn-info" onclick="editAlertRule('${rule.id}')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteAlertRule('${rule.id}')">删除</button>
                            </td>
                        </tr>
                    `;
                });
                $('#alert-rules-tbody').html(html);
            })
            .fail(function() {
                // 使用模拟数据
                var html = `
                    <tr>
                        <td>API响应时间告警</td>
                        <td>用户数据API</td>
                        <td>ResponseTime</td>
                        <td><span class="badge badge-danger">严重</span></td>
                        <td>5分钟</td>
                        <td><span class="badge badge-success">启用</span></td>
                        <td>2023-12-01 09:00:00</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="evaluateRule('rule_001')">评估</button>
                            <button class="btn btn-sm btn-info" onclick="editAlertRule('rule_001')">编辑</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteAlertRule('rule_001')">删除</button>
                        </td>
                    </tr>
                `;
                $('#alert-rules-tbody').html(html);
            });
    }

    function loadAlertHistory() {
        var params = '';
        var severity = $('#severity-filter').val();
        var startDate = $('#start-date').val();
        var endDate = $('#end-date').val();
        
        if (severity) params += `&severity=${severity}`;
        if (startDate) params += `&startTime=${startDate}T00:00:00Z`;
        if (endDate) params += `&endTime=${endDate}T23:59:59Z`;
        
        callAPI(`/api/Alert/history?limit=50${params}`)
            .done(function(data) {
                var html = '';
                data.forEach(function(alert) {
                    var severityBadge = getSeverityBadge(alert.severity);
                    var statusBadge = alert.isResolved ? 
                        '<span class="badge badge-success">已解决</span>' : 
                        '<span class="badge badge-warning">未解决</span>';
                    
                    html += `
                        <tr>
                            <td>${alert.alertId}</td>
                            <td>${alert.title}</td>
                            <td>${severityBadge}</td>
                            <td>${alert.apiName || 'N/A'}</td>
                            <td>${formatDateTime(alert.firstAlertTime)}</td>
                            <td>${alert.resolvedAt ? formatDateTime(alert.resolvedAt) : 'N/A'}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="viewAlertDetails('${alert.id}')">详情</button>
                            </td>
                        </tr>
                    `;
                });
                $('#alert-history-tbody').html(html);
            })
            .fail(function() {
                showAlert('加载告警历史失败', 'danger');
            });
    }

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                var options = '<option value="">全局规则</option>';
                data.forEach(function(config) {
                    options += `<option value="${config.id}">${config.name}</option>`;
                });
                $('#ruleApiConfigId').html(options);
            });
    }

    function getSeverityBadge(severity) {
        var badges = {
            1: '<span class="badge badge-info">信息</span>',
            2: '<span class="badge badge-warning">警告</span>',
            3: '<span class="badge badge-danger">错误</span>',
            4: '<span class="badge badge-danger">严重</span>',
            5: '<span class="badge badge-dark">致命</span>'
        };
        return badges[severity] || '<span class="badge badge-secondary">未知</span>';
    }

    function showCreateAlertRuleModal() {
        $('#createAlertRuleModal').modal('show');
    }

    function createAlertRule() {
        var notificationChannels = [];
        if ($('#emailNotification').is(':checked')) notificationChannels.push(1);
        if ($('#smsNotification').is(':checked')) notificationChannels.push(2);
        if ($('#webhookNotification').is(':checked')) notificationChannels.push(6);

        var formData = {
            name: $('#ruleName').val(),
            description: $('#ruleDescription').val(),
            apiConfigId: $('#ruleApiConfigId').val() || null,
            isEnabled: $('#isEnabled').is(':checked'),
            alertType: $('#alertType').val(),
            severity: parseInt($('#severity').val()),
            conditions: JSON.stringify({
                threshold: parseFloat($('#threshold').val()),
                operator: '>'
            }),
            evaluationIntervalMinutes: parseInt($('#evaluationInterval').val()),
            triggerThreshold: parseInt($('#triggerThreshold').val()),
            silencePeriodMinutes: parseInt($('#silencePeriod').val()),
            notificationChannels: notificationChannels
        };

        callAPI('/api/Alert/rules', 'POST', formData)
            .done(function(response) {
                showAlert('告警规则创建成功', 'success');
                $('#createAlertRuleModal').modal('hide');
                loadAlertRules();
                loadAlertOverview();
            })
            .fail(function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showAlert('创建告警规则失败: ' + error, 'danger');
            });
    }

    function acknowledgeAlert(alertId) {
        var data = {
            acknowledgedBy: 'admin',
            notes: '已确认告警'
        };

        callAPI(`/api/Alert/acknowledge/${alertId}`, 'POST', data)
            .done(function() {
                showAlert('告警已确认', 'success');
                loadActiveAlerts();
            })
            .fail(function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '确认失败';
                showAlert('确认告警失败: ' + error, 'danger');
            });
    }

    function resolveAlert(alertId) {
        var data = {
            resolvedBy: 'admin',
            resolutionNotes: '问题已解决'
        };

        callAPI(`/api/Alert/resolve/${alertId}`, 'POST', data)
            .done(function() {
                showAlert('告警已解决', 'success');
                loadActiveAlerts();
                loadAlertOverview();
            })
            .fail(function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '解决失败';
                showAlert('解决告警失败: ' + error, 'danger');
            });
    }

    function evaluateAllRules() {
        callAPI('/api/Alert/evaluate', 'POST')
            .done(function() {
                showAlert('告警规则评估完成', 'success');
                loadActiveAlerts();
            })
            .fail(function(xhr) {
                var error = xhr.responseJSON ? xhr.responseJSON.message : '评估失败';
                showAlert('评估告警规则失败: ' + error, 'danger');
            });
    }

    function filterAlertHistory() {
        loadAlertHistory();
    }

    function refreshAlertData() {
        loadAlertOverview();
        loadActiveAlerts();
        loadAlertRules();
        loadAlertHistory();
        showAlert('数据已刷新', 'info');
    }

    // 其他功能函数
    function evaluateRule(ruleId) { /* 评估单个规则 */ }
    function editAlertRule(ruleId) { /* 编辑告警规则 */ }
    function deleteAlertRule(ruleId) { /* 删除告警规则 */ }
    function viewAlertDetails(alertId) { /* 查看告警详情 */ }
    function acknowledgeAllAlerts() { /* 确认所有告警 */ }
    function exportAlerts() { /* 导出告警数据 */ }
</script>
}
