﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 单据费用及付款凭证接收数据
    /// </summary>
    public class Request_MC04
    {
        /// <summary>
        /// 公司名称
        /// </summary>
        public string company_name { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company_code { get; set; }
        /// <summary>
        /// 日志账名
        /// </summary>
        public string accounting_name { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public string accounting_date { get; set; }
        /// <summary>
        /// 凭证编号
        /// </summary>
        [Required(ErrorMessage = "凭证编号不可为空")]
        public string voucher_no { get; set; }
        /// <summary>
        /// 批名
        /// </summary>
        public string batch_name { get; set; }
        /// <summary>
        /// 合计金额贷方
        /// </summary>
        public string credit_total_amount { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public string write_off_status { get; set; }
        /// <summary>
        /// 凭证类型
        /// </summary>
        public string voucher_type { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 凭证合计金额
        /// </summary>
        public string voucher_total_amount { get; set; }
        /// <summary>
        /// 凭证审核人工号
        /// </summary>
        public string voucher_approver_job_num { get; set; }
        /// <summary>
        /// 凭证审核人名称
        /// </summary>
        public string voucher_approver_name { get; set; }
        /// <summary>
        /// 会计期间
        /// </summary>
        public string accounting_period { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string voucher_source { get; set; }
        /// <summary>
        /// 合计金额借方
        /// </summary>
        public string debit_total_amount { get; set; }
        /// <summary>
        /// 制单人名称
        /// </summary>
        public string voucher_creator_name { get; set; }
        /// <summary>
        /// 制单人工号
        /// </summary>
        public string voucher_creator_job_num { get; set; }
        /// <summary>
        /// OA流程编码
        /// </summary>
        [Required(ErrorMessage = "OA流程编码不可为空")]
        public string oa_flow_code { get; set; }
        /// <summary>
        /// 摘要详情
        /// </summary>
        public List<Summary_details> summary_details { get; set; }
        /// <summary>
        /// 影像数据
        /// </summary>
        public string img_data { get; set; }
        /// <summary>
        /// 文件类型（png/jpg/jpeg/pdf/ofd）
        /// </summary>
        public string img_data_filetype { get; set; }
        /// <summary>
        /// 批次状态
        /// </summary>
        public string batch_status { get; set; }
        /// <summary>
        /// 凭证分类账
        /// </summary>
        public string voucher_ledger { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 发票影像标识集合
        /// </summary>
        /// <remarks>
        /// 发票报销时从票池中选择的发票标识
        /// </remarks>
        [Required(ErrorMessage = "发票影像标识集合必填")]
        public string[] image_mstids { get; set; }
    }
}
