﻿using System.Xml;
using System;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace Docpark.ThirdMaster.Services.Utils
{
    public static class EncryptionHelper
    {
        public static IConfiguration Configuration { get; set; }
        public static string GetDataXMLByName(string xmlname, string nodename)
        {
            string companyCode = Configuration["CompanyCode"];
            string bpath = "./ConfigXml/" + companyCode + "/" + xmlname;

            try
            {
                if(!File.Exists(bpath))
                {
                    throw new Exception($"配置文件不存在,文件路径为：{bpath} 请检查文件路径");
                }

                string Filestring = FileClassHelper.GetXMLFilestring(bpath);
                XmlDocument xd = new XmlDocument();
                xd.LoadXml(Filestring);
                XmlNode xnode = xd.SelectSingleNode("root/" + nodename);
                return xnode.InnerText;
            }
            catch (Exception ex)
            {
                throw new Exception($"{xmlname}未配置此节点:{nodename}；路径:{bpath}； 错误信息:{ex.Message}");
            }
        }
    }
}
