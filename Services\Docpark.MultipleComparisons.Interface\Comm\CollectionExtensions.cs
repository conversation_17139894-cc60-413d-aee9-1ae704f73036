﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Comm
{
    public static class CollectionExtensions
    {
        public static bool IsNullOrWhiteSpace(this string str)
        {
            return string.IsNullOrWhiteSpace(str);
        }

        public static bool IsNullOrEmpty(this string str)
        {
            return string.IsNullOrEmpty(str);
        }

        /// <summary>
        /// Converts given object to a value type using <see cref="Convert.ChangeType(object,System.Type)"/> method.
        /// </summary>
        /// <param name="obj">Object to be converted</param>
        /// <typeparam name="T">Type of the target object</typeparam>
        /// <returns>Converted object</returns>
        public static T To<T>(this object obj)
            where T : struct
        {
            return (T)Convert.ChangeType(obj, typeof(T), CultureInfo.InvariantCulture);
        }

        public static string getString(this JObject jObject, string key)
        {
            try
            {
                return jObject.ContainsKey(key) ? jObject[key].ToString() : "";
            }
            catch
            {
                return "";
            }
        }

        public static int getInt(this JObject jObject, string key, int defaultValue = 0)
        {
            try
            {
                string value = jObject.getString(key);
                return string.IsNullOrEmpty(value) ? defaultValue : int.Parse(value);
            }
            catch
            {
                return defaultValue;
            }
        }
        public static JObject getJObject(this JObject jObject, string key)
        {
            try
            {
                return (JObject)jObject[key];
            }
            catch
            {
                return null;
            }
        }

        public static JArray getJArray(this JObject jObject, string key)
        {
            try
            {
                return (JArray)jObject[key];
            }
            catch
            {
                return null;
            }
        }
        public static string getStrDateTime(this JObject jObject, string key)
        {
            try
            {
                string value = jObject.getString(key);
                if (string.IsNullOrEmpty(value))
                    return "";
                DateTime dtStart = new DateTime(1970, 1, 1, 0, 0, 0);
                long lTime = long.Parse(value + "0000000");
                TimeSpan toNow = new TimeSpan(lTime);
                return dtStart.Add(toNow).ToString("yyyy-MM-dd");
            }
            catch
            {
                return "";
            }
        }

        public static string getStrWXAmount(this JObject jObject, string key)
        {
            try
            {
                string value = jObject.getString(key);
                if (string.IsNullOrEmpty(value))
                    return "0.00";
                if (value.Length >= 3)
                {
                    return value.Insert(value.Length - 2, ".");
                }
                return value;
            }
            catch
            {
                return "0.00";
            }
        }
    }
}
