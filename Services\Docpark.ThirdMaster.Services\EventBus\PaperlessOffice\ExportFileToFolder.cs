﻿using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.Services;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;
using Docpark.ThirdMaster.Services.Utils;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.EventBus.PaperlessOffice
{
    [MasterServiceType("PaperlessOffice")]
    [ServiceType("ExportFileToFolder", "导出文件到指定目录")]
    public class ExportFileToFolder : IEventService
    {
        private readonly ILogger<ExportFileToFolder> logger;
        private readonly IObjectQuery objectQuery;
        private readonly IStorageService storageService;
        private readonly IEventBus eventBus;

        protected string configXml = "exportFileToFolder.xml";

        public ExportFileToFolder(ILogger<ExportFileToFolder> logger,
            IObjectQuery objectQuery, IStorageService storageService, IEventBus eventBus)
        {
            this.logger = logger;
            this.objectQuery = objectQuery;
            this.storageService = storageService;
            this.eventBus = eventBus;
        }

        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            StringBuilder resMessage = new StringBuilder();

            try
            {

                resMessage.Append("开始导出文件到指定目录");

                var data = await objectQuery.Get<Dictionary<string, object>>(new Guid(mqContent.MstId));

                if (data != null)
                {
                    var folder = EncryptionHelper.GetDataXMLByName(configXml, "folder");
                    var fileName = EncryptionHelper.GetDataXMLByName(configXml, "fileName");

                    var type = data.ContainsKey("type") && data["type"].ToString()!="" ? data["type"].ToString() : "other";

                    foreach (var _item in data)
                    {
                        fileName = fileName.Replace("${" + _item.Key + "}", _item.Value != null ? _item.Value.ToString() : "");
                    }

                    var documentInfo = await storageService.GetDocumentInfo(mqContent.DocumentID);
                    var ext = Path.GetExtension(documentInfo.FileName)?.ToLower();

                    var bytes = await storageService.GetDocument(mqContent.DocumentID);


                    folder = folder + "/" + type;


                    folder = folder + "/" + DateTime.Now.ToString("yyyyMMdd");
                    if (!Directory.Exists(folder))
                    {
                        Directory.CreateDirectory(folder);
                    }

                    File.WriteAllBytes(folder + "/" + fileName + ext, bytes);


                    mqContent.TriggerStatus = true;
                    mqContent.TriggerMessage = "";
                    var msg = JsonConvert.SerializeObject(mqContent);
                    DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
                    eventBus.Publish(publishEventData);

                    resMessage.Append("导出文件到指定目录");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message, ex);
                mqContent.TriggerStatus = false;
                mqContent.TriggerMessage = ex.Message;
                var msg = JsonConvert.SerializeObject(mqContent);
                DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
                eventBus.Publish(publishEventData);
            }

            return resMessage.ToString();
        }
    }
}
