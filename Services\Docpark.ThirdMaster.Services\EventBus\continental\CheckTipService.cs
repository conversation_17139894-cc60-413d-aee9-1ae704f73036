﻿using Docpark.ThirdMaster.Services.Models.Entities;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using DocPark.Commons;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Docpark.ThirdMaster.Services.EventBus.continental.Dto;

namespace Docpark.ThirdMaster.Services.EventBus.continental
{
    [MasterServiceType("continental")]
    [ServiceType("CheckTip", "校验服务")]
    public class CheckTipService : IEventService
    {

        private readonly IConfiguration configuration;
        private readonly IEventBus eventBus;
        private readonly IDGrpc grpc;
        private readonly IObjectQuery objectQuery;
        private readonly string[] types = new string[] { "10100", "10101", "10102", "10103", "10106", "10107", "10108" };
        public CheckTipService(IConfiguration configuration, IEventBus eventBus, IDGrpc grpc, IObjectQuery objectQuery)
        {
            this.configuration = configuration;
            this.eventBus = eventBus;
            this.grpc = grpc;
            this.objectQuery = objectQuery;
        }

        public async Task<string> Handle(WorkflowMQContent mqContent)
        {
            var businessType = await grpc.GetStringAsync("App.CustomerClaim.BusinessType");//客户索赔的文档类型
            var invoice = await objectQuery.Get<InvoiceInfo>(new Guid(mqContent.MstId));

            mqContent.TriggerStatus = true;
            if (string.Compare(invoice.businesstypeid, CommonUtil.ConvertObjectIdToGuid(businessType).ToString(), true) == 0)//客户索赔流程
            {
                if (!types.Contains(invoice.type))
                {
                    //识别发票时若不是专票、普票类型需提示“需维护外币发票/收据！”
                    mqContent.TriggerStatus = false;
                    mqContent.TriggerMessage = configuration["CheckTip_Msg"] ?? "需维护外币发票/收据！";
                }
            }
            else //非客户索赔流程
            {
                //根据维护的客户名称-税号-简称对照表，校验销售方名称和销售方税号是否符合上述客户清单，符合则为客户索赔发票，否则不是
                var filters = new List<Filter>()
                {
                    new Filter() { Field = "TaxNumber", Mode = "in", Values = new string[] { invoice.seller_tax_id, invoice.buyer_tax_id } }
                };
                var count = await objectQuery.Count<CustomerEntity>(filters);
                if (count > 0)
                {
                    mqContent.TriggerStatus = false;
                    mqContent.TriggerMessage = "该发票为客户索赔发票!";
                }
            }

            var msg = JsonConvert.SerializeObject(mqContent);
            var publishEventData = new DocumentServiceMQEventData(msg);
            eventBus.Publish(publishEventData);

            return string.Empty;
        }
    }
}
