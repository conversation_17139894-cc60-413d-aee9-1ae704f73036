namespace Docpark.ThirdPartyIntegration.Domain.Enums
{
    /// <summary>
    /// 冲突解决策略枚举
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        /// <summary>
        /// 跳过冲突数据
        /// </summary>
        Skip = 1,

        /// <summary>
        /// 覆盖现有数据
        /// </summary>
        Overwrite = 2,

        /// <summary>
        /// 保留现有数据
        /// </summary>
        KeepExisting = 3,

        /// <summary>
        /// 合并数据
        /// </summary>
        Merge = 4,

        /// <summary>
        /// 创建新版本
        /// </summary>
        CreateVersion = 5,

        /// <summary>
        /// 手动处理
        /// </summary>
        Manual = 6
    }
}
