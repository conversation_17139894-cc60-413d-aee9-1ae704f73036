using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 限流管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RateLimitController : ControllerBase
    {
        private readonly IRateLimitService _rateLimitService;
        private readonly ILogger<RateLimitController> _logger;

        public RateLimitController(IRateLimitService rateLimitService, ILogger<RateLimitController> logger)
        {
            _rateLimitService = rateLimitService;
            _logger = logger;
        }

        /// <summary>
        /// 创建限流规则
        /// </summary>
        [HttpPost("rules")]
        public async Task<IActionResult> CreateRateLimitRule([FromBody] RateLimitRule rule)
        {
            try
            {
                var id = await _rateLimitService.CreateRateLimitRuleAsync(rule);
                return Ok(new { id, message = "限流规则创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建限流规则失败");
                return StatusCode(500, new { message = "创建限流规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新限流规则
        /// </summary>
        [HttpPut("rules/{id}")]
        public async Task<IActionResult> UpdateRateLimitRule(string id, [FromBody] RateLimitRule rule)
        {
            try
            {
                var success = await _rateLimitService.UpdateRateLimitRuleAsync(id, rule);
                if (success)
                {
                    return Ok(new { message = "限流规则更新成功" });
                }
                return NotFound(new { message = "限流规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新限流规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "更新限流规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取限流规则
        /// </summary>
        [HttpGet("rules/{id}")]
        public async Task<IActionResult> GetRateLimitRule(string id)
        {
            try
            {
                var rule = await _rateLimitService.GetRateLimitRuleAsync(id);
                if (rule != null)
                {
                    return Ok(rule);
                }
                return NotFound(new { message = "限流规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取限流规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有限流规则
        /// </summary>
        [HttpGet("rules")]
        public async Task<IActionResult> GetAllRateLimitRules([FromQuery] bool enabledOnly = false)
        {
            try
            {
                var rules = await _rateLimitService.GetAllRateLimitRulesAsync(enabledOnly);
                return Ok(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流规则列表失败");
                return StatusCode(500, new { message = "获取限流规则列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除限流规则
        /// </summary>
        [HttpDelete("rules/{id}")]
        public async Task<IActionResult> DeleteRateLimitRule(string id)
        {
            try
            {
                var success = await _rateLimitService.DeleteRateLimitRuleAsync(id);
                if (success)
                {
                    return Ok(new { message = "限流规则删除成功" });
                }
                return NotFound(new { message = "限流规则不存在" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除限流规则失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除限流规则失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 检查限流状态
        /// </summary>
        [HttpGet("check/{apiConfigId}")]
        public async Task<IActionResult> CheckRateLimit(string apiConfigId, [FromQuery] string clientIdentifier = null, [FromQuery] string requestPath = null)
        {
            try
            {
                clientIdentifier = clientIdentifier ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                var result = await _rateLimitService.IsRequestAllowedAsync(apiConfigId, clientIdentifier, requestPath);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查限流状态失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "检查限流状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取限流统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetRateLimitStatistics([FromQuery] string apiConfigId = null, [FromQuery] DateTime? startTime = null, [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var statistics = await _rateLimitService.GetRateLimitStatisticsAsync(apiConfigId, startTime, endTime);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取限流统计信息失败");
                return StatusCode(500, new { message = "获取限流统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重置限流计数器
        /// </summary>
        [HttpPost("reset")]
        public async Task<IActionResult> ResetRateLimitCounter([FromQuery] string apiConfigId, [FromQuery] string clientIdentifier)
        {
            try
            {
                var success = await _rateLimitService.ResetRateLimitCounterAsync(apiConfigId, clientIdentifier);
                if (success)
                {
                    return Ok(new { message = "限流计数器重置成功" });
                }
                return BadRequest(new { message = "限流计数器重置失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置限流计数器失败");
                return StatusCode(500, new { message = "重置限流计数器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取客户端限流状态
        /// </summary>
        [HttpGet("status")]
        public async Task<IActionResult> GetClientRateLimitStatus([FromQuery] string apiConfigId, [FromQuery] string clientIdentifier = null)
        {
            try
            {
                clientIdentifier = clientIdentifier ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                var status = await _rateLimitService.GetClientRateLimitStatusAsync(apiConfigId, clientIdentifier);
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端限流状态失败");
                return StatusCode(500, new { message = "获取客户端限流状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的限流数据
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupExpiredRateLimitData([FromQuery] int olderThanHours = 24)
        {
            try
            {
                var deletedCount = await _rateLimitService.CleanupExpiredRateLimitDataAsync(olderThanHours);
                return Ok(new { message = "清理完成", deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期限流数据失败");
                return StatusCode(500, new { message = "清理过期限流数据失败", error = ex.Message });
            }
        }
    }
}
