﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IThirdpartyOrganizationService
    {

        /// <summary>
        /// 同步第三方组织机构数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <param name="errMsg"></param>
        /// <returns></returns>
        bool SyncThirdpartyOrganization(Dictionary<string, object> requestData, out string errMsg);

        /// <summary>
        /// 同步第三方组织用户数据
        /// </summary>
        /// <param name="requestData"></param>
        /// <param name="errMsg"></param>
        /// <returns></returns>
        bool SyncThirdpartyUser(Dictionary<string, object> requestData, out string errMsg);
    }
}
