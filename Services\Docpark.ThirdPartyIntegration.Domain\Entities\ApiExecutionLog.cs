using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// API执行日志实体
    /// </summary>
    public class ApiExecutionLog
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称
        /// </summary>
        [StringLength(100)]
        public string ApiName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public ExecutionStatus Status { get; set; } = ExecutionStatus.Running;

        /// <summary>
        /// 请求数据
        /// </summary>
        public string RequestData { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string ResponseData { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int? StatusCode { get; set; }

        /// <summary>
        /// 执行时长（毫秒）
        /// </summary>
        public long? DurationMs { get; set; }

        /// <summary>
        /// 处理记录数
        /// </summary>
        public int RecordsProcessed { get; set; }

        /// <summary>
        /// 执行者
        /// </summary>
        [StringLength(100)]
        public string ExecutedBy { get; set; }

        /// <summary>
        /// 执行类型（手动/自动）
        /// </summary>
        [StringLength(20)]
        public string ExecutionType { get; set; } = "Auto";
    }
}
