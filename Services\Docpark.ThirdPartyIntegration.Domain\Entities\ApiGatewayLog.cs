using System;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// API网关日志实体
    /// </summary>
    public class ApiGatewayLog
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        [Required]
        public string RequestId { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称
        /// </summary>
        [StringLength(100)]
        public string ApiName { get; set; }

        /// <summary>
        /// 客户端IP
        /// </summary>
        [StringLength(45)]
        public string ClientIp { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [StringLength(500)]
        public string UserAgent { get; set; }

        /// <summary>
        /// 请求方法
        /// </summary>
        [StringLength(10)]
        public string Method { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        [StringLength(500)]
        public string Path { get; set; }

        /// <summary>
        /// 查询参数
        /// </summary>
        public string QueryString { get; set; }

        /// <summary>
        /// 请求头（JSON格式）
        /// </summary>
        public string RequestHeaders { get; set; }

        /// <summary>
        /// 请求体
        /// </summary>
        public string RequestBody { get; set; }

        /// <summary>
        /// 响应状态码
        /// </summary>
        public int ResponseStatusCode { get; set; }

        /// <summary>
        /// 响应头（JSON格式）
        /// </summary>
        public string ResponseHeaders { get; set; }

        /// <summary>
        /// 响应体
        /// </summary>
        public string ResponseBody { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// 请求大小（字节）
        /// </summary>
        public long RequestSize { get; set; }

        /// <summary>
        /// 响应大小（字节）
        /// </summary>
        public long ResponseSize { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 限流信息
        /// </summary>
        public string RateLimitInfo { get; set; }

        /// <summary>
        /// 熔断器信息
        /// </summary>
        public string CircuitBreakerInfo { get; set; }

        /// <summary>
        /// 负载均衡信息
        /// </summary>
        public string LoadBalancerInfo { get; set; }

        /// <summary>
        /// 目标服务器
        /// </summary>
        [StringLength(200)]
        public string TargetServer { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 缓存信息
        /// </summary>
        public string CacheInfo { get; set; }

        /// <summary>
        /// 认证信息
        /// </summary>
        public string AuthInfo { get; set; }

        /// <summary>
        /// 请求时间
        /// </summary>
        public DateTime RequestTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime? ResponseTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
