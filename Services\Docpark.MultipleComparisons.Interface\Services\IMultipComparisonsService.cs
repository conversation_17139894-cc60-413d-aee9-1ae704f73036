﻿using Docpark.MultipleComparisons.Interface.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Services
{
    public interface IMultipComparisonsService
    {

        /// <summary>
        /// 获取单据影像预览HTML页面地址
        /// </summary>
        /// <param name="bill_no">单据编号</param>
        /// <param name="mark">预览数据标识, 1 单据发票附件预览, 2 单据扫描影像预览</param>
        /// <returns></returns>
        string GetBillPreviewHtml(string bill_no, int mark);

        /// <summary>
        /// 删除单据下的表单数据
        /// </summary>
        /// <param name="delMark">0 删除扫描影像数据, 1 删除单据下所有的数据</param>
        /// <param name="bill_no">单据编号</param>
        /// <returns>返回执行结果</returns>
        Task<(bool result, string errMsg)> RemoveBillDataAsync(int delMark, string bill_no);

        /// <summary>
        /// 上传附件Base64文件, 返回附件文件预览地址
        /// </summary>
        /// <param name="inputBase64">附件Base64字符串</param>
        /// <param name="annexMstId">附件ID</param>
        /// <returns></returns>
        Task<string> UploadImage(string inputBase64, string annexMstId,string filetype);
        /// <summary>
        /// 获取远东-电子档案的单点登录Token
        /// </summary>
        /// <returns></returns>
        string GetEleArchivesLoginToken();


        /// <summary>
        /// 推送0A单据归档数据
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_OA_Bill_Archives_Data(string eleLoginToken);

        /// <summary>
        /// 推送0A单据发票明细归档数据
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_OA_Invoice_Archives_Data(string eleLoginToken);

        /// <summary>
        /// 推送凭证归档数据
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_Voucher_Archives_Data(string eleLoginToken);

        /// <summary>
        /// 推送电子回单归档数据
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_Ele_Receipt_Archives_Data(string eleLoginToken);

        /// <summary>
        /// 推送 凭证和单据关系接口API
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_Voucher2Bill_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_MASTER_DATA> pushBillList, string eleLoginToken);

        /// <summary>
        /// 推送 凭证和发票关系接口API
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_Voucher2Invoice_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_INVOICE_DATA> pushInvoiceList, string eleLoginToken);

        /// <summary>
        /// 推送 凭证和流水关系接口API
        /// </summary>
        /// <returns></returns>
        Task<bool> Push_Voucher2Batch_Archives_Data(List<MC_BILL_VOUCHER_DATA> pushVoucherList, List<MC_BILL_ELE_RECEIPT_DATA> pushEleReceiptList, string eleLoginToken);

        /// <summary>
        /// 实现推送 凭证和单据关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        Task<bool> SendOaMaster(string eleLoginToken);
        /// <summary>
        /// 实现推送 凭证和发票关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        Task<bool> SendInvoice(string eleLoginToken);
        /// <summary>
        /// 实现推送 凭证和流水关系接口API
        /// </summary>
        /// <param name="eleLoginToken"></param>
        /// <returns></returns>
        Task<bool> Send_Archives_Data(string eleLoginToken);
        /// <summary>
        /// 设置影像发票状态
        /// </summary>
        /// <param name="mstId">影像MstId</param>
        /// <param name="status">影像发票状态: 0 草稿, 1 未报销, 2 报销中, 3 已报销</param>
        /// <returns></returns>
        Task<bool> SetImageInvoiceStatus(string mstId, string status);
    }
}
