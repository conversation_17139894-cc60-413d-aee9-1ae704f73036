﻿################################################################################
# 此 .gitignore 文件已由 Microsoft(R) Visual Studio 自动创建。
################################################################################

/Services/Docpark.MultipleComparisons.Interface/bin/Debug/netcoreapp3.1

/Services/Docpark.MultipleComparisons.Interface/bin/Debug/netcoreapp3.1

/Services/Docpark.MultipleComparisons.Interface/obj

/BuildingBlocks/DocPark.Commons/bin/Debug/netcoreapp3.1
/BuildingBlocks/DocPark.Commons/obj
/BuildingBlocks/Docpark.HttpClientExtension/bin/Debug/netcoreapp3.1
/BuildingBlocks/Docpark.HttpClientExtension/obj
/BuildingBlocks/DocPark.CustomExtension/bin/Debug/netcoreapp3.1
/BuildingBlocks/DocPark.CustomExtension/obj
/.vs
/BuildingBlocks/Devspaces.Support/obj
/BuildingBlocks/Devspaces.Support/bin/Debug/netcoreapp3.1
/BuildingBlocks/EventBus/bin/Debug/netcoreapp3.1
/BuildingBlocks/EventBus/obj
/BuildingBlocks/EventBusRabbitMQ/bin/Debug/netcoreapp3.1
/BuildingBlocks/EventBusRabbitMQ/obj
/BuildingBlocks/EventBusServiceBus/bin/Debug/netcoreapp3.1
/BuildingBlocks/EventBusServiceBus/obj
/BuildingBlocks/IntergrationEventLogEF/bin/Debug/netcoreapp3.1
/BuildingBlocks/IntergrationEventLogEF/obj
/BuildingBlocks/WebHost.Customization/bin/Debug/netcoreapp3.1
/BuildingBlocks/WebHost.Customization/obj
/BuildingBlocks/DocPark.CustomExtension/obj
/BuildingBlocks/DocPark.CustomExtension/bin
/BuildingBlocks/DocPark.CustomExtension/obj
/BuildingBlocks/DocPark.CustomExtension/bin
/BuildingBlocks/DocPark.MongoDb/obj
/BuildingBlocks/DocPark.MongoDb/bin
/BuildingBlocks/DocPark.MongoDb/bin
/BuildingBlocks/EventBusServiceBus/obj
/BuildingBlocks/EventBusServiceBus/bin
/BuildingBlocks/WebHost.Customization/obj
/BuildingBlocks/DocPark.CustomExtension/obj

/BuildingBlocks/Devspaces.Support/bin/Release/netcoreapp3.1
/BuildingBlocks/DocPark.Commons/bin/Release/netcoreapp3.1
/BuildingBlocks/Docpark.HttpClientExtension/bin/Release/netcoreapp3.1
/BuildingBlocks/EventBus/bin/Release/netcoreapp3.1
/BuildingBlocks/EventBusRabbitMQ/bin/Release/netcoreapp3.1
/Services/Docpark.MultipleComparisons.Interface/bin/Release/netcoreapp3.1
/Services/Docpark.MultipleComparisons.Interface/Properties/PublishProfiles
/Services/Docpark.MultipleComparisons.Interface/Docpark.MultipleComparisons.Interface.csproj.user
/BuildingBlocks/DocPark.Workflow.Share/bin/Debug/netcoreapp3.1
/BuildingBlocks/DocPark.Workflow.Share/bin/Release
/BuildingBlocks/DocPark.Workflow.Share/obj
/Services/Docpark.Continental.Services/bin/Debug/netcoreapp3.1
/Services/Docpark.Continental.Services/obj
/BuildingBlocks/DocPark.EmailService/bin/Debug/netcoreapp3.1
/BuildingBlocks/DocPark.EmailService/obj/Debug/netcoreapp3.1
/BuildingBlocks/DocPark.EmailService/obj/DocPark.EmailService.csproj.nuget.dgspec.json
/BuildingBlocks/DocPark.EmailService/obj/DocPark.EmailService.csproj.nuget.g.props
/BuildingBlocks/DocPark.EmailService/obj/DocPark.EmailService.csproj.nuget.g.targets
/BuildingBlocks/DocPark.EmailService/obj/project.assets.json
/BuildingBlocks/DocPark.EmailService/obj/project.nuget.cache
/Services/Docpark.Continental.Services/DL_BathDK
/Services/Docpark.Continental.Services/RewritingInvoiceToGooney
/Services/Docpark.Continental.WebApi/bin
/Services/Docpark.Continental.WebApi/obj/Debug/.NETFramework,Version=v4.8.AssemblyAttributes.cs
/Services/Docpark.Continental.WebApi/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
/Services/Docpark.Continental.WebApi/obj/Debug/Docpark.Continental.WebApi.csproj.AssemblyReference.cache
/Services/Docpark.Continental.WebApi/obj/Debug/Docpark.Continental.WebApi.csproj.FileListAbsolute.txt
/Services/Docpark.Siemens.Concur/obj
/Services/Docpark.Siemens.Services/obj
/BuildingBlocks/DocPark.EmailService/bin
/BuildingBlocks/DocPark.EmailService/obj
/Services/Docpark.Continental.Services/RewritingInvoiceToGooney/finish
/Services/Docpark.Continental.Services/RewritingInvoiceToGooney
/Services/Docpark.Continental.Services/DL_BathDK
/Services/Docpark.Continental.WebApi/bin
/Services/Docpark.Continental.WebApi/obj
/Services/Docpark.ThirdMaster.Services/bin/Debug/netcoreapp3.1
/Services/Docpark.ThirdMaster.Services/obj
*.user
/Services/Docpark.Siemens.Concur/obj/Debug/netcoreapp3.1
/Services/Docpark.Siemens.Concur/obj
/Services/Docpark.Siemens.Services/obj
.vs/
.vscode/launch.json
.vscode/settings.json
.vscode/tasks.json
/Services/Docpark.ThirdPartyIntegration.Domain/bin/Debug/netcoreapp3.1
/Services/Docpark.ThirdPartyIntegration.Domain/obj
/Services/Docpark.ThirdPartyIntegration.Services/obj
/Services/Docpark.ThirdPartyIntegration.WebApi/obj
/Services/Docpark.ThirdPartyIntegration.Services/bin/Debug/netcoreapp3.1
/Services/Docpark.ThirdPartyIntegration.WebApi/bin/Debug/netcoreapp3.1
