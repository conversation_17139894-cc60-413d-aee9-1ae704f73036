﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Docpark.HttpClientExtension.Grpc.Model;
using DocPark.V2.Web.Host.Grpc;
using GrpcDocpark;

namespace Docpark.HttpClientExtension.Grpc
{
    public class DGrpc : IDGrpc
    {
        public DGrpc(UserApi.UserApiClient client, UuidRPC.UuidRPCClient uidClient)
        {
            _client = client;
            _uidClient = uidClient;
        }

        public UserApi.UserApiClient _client { get; }
        public UuidRPC.UuidRPCClient _uidClient { get; }

        public async Task<string> GetStringAsync(string key)
        {
            var result = await _client.GetAppSettingAsync(new SettingRequest() { Key = key });
            if (result != null)
            {
                return result.Value;
            }
            return null;
        }

        public async Task<List<UserInfo>> GetUsers()
        {
            var result = await _client.GetUsersAsync(new UserRequest() { });
            List<UserInfo> userList = new List<UserInfo>();
            foreach (var _data in result.Data)
            {
                userList.Add(new UserInfo()
                {
                    Id = _data.Id,
                    Name = _data.FullName,
                    UserName = _data.UserName,
                    Email = _data.Email
                });
            }
            return userList;
        }

        public async Task<string> GetUserToken(string userId)
        {
            var result = await _client.GetTokenAsync(new UserRequest() { UserId = userId });
            return result.Token;
        }

        public Guid NewGuid()
        {
            var result = _uidClient.GetNewGuid(new Google.Protobuf.WellKnownTypes.Empty());
            return new Guid(result.Guid);
        }
    }
}
