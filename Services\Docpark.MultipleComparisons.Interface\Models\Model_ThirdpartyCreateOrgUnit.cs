﻿ using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    public class Model_ThirdpartyCreateOrgUnit
    {

        public string ThirdpartyId { get; set; }
        public string ThirdpartyParentId { get; set; }
        public string DisplayName { get; set; }
        /// <summary>
        /// 类型, G 集团; A 区域; C 公司; D 部门
        /// </summary>
        public string Type { get; set; }
        public string Label { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string No { get; set; } = string.Empty;
        /// <summary>
        /// 简称
        /// </summary>
        public string ShortName { get; set; }
    }
}
