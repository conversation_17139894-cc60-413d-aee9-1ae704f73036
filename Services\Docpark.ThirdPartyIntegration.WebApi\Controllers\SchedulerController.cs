using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 任务调度管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SchedulerController : ControllerBase
    {
        private readonly ISchedulerService _schedulerService;
        private readonly IApiConfigurationService _apiConfigurationService;
        private readonly ILogger<SchedulerController> _logger;

        public SchedulerController(
            ISchedulerService schedulerService,
            IApiConfigurationService apiConfigurationService,
            ILogger<SchedulerController> logger)
        {
            _schedulerService = schedulerService;
            _apiConfigurationService = apiConfigurationService;
            _logger = logger;
        }

        /// <summary>
        /// 启动调度器
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("start")]
        public async Task<IActionResult> StartScheduler()
        {
            try
            {
                await _schedulerService.StartAsync();
                return Ok(new { message = "调度器启动成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动调度器失败");
                return StatusCode(500, new { message = "启动调度器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 停止调度器
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("stop")]
        public async Task<IActionResult> StopScheduler()
        {
            try
            {
                await _schedulerService.StopAsync();
                return Ok(new { message = "调度器停止成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止调度器失败");
                return StatusCode(500, new { message = "停止调度器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 暂停调度器
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("pause")]
        public async Task<IActionResult> PauseScheduler()
        {
            try
            {
                await _schedulerService.PauseAsync();
                return Ok(new { message = "调度器暂停成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停调度器失败");
                return StatusCode(500, new { message = "暂停调度器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 恢复调度器
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("resume")]
        public async Task<IActionResult> ResumeScheduler()
        {
            try
            {
                await _schedulerService.ResumeAsync();
                return Ok(new { message = "调度器恢复成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复调度器失败");
                return StatusCode(500, new { message = "恢复调度器失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 调度API任务
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="scheduleConfig">调度配置</param>
        /// <returns>任务键</returns>
        [HttpPost("schedule/{apiConfigId}")]
        public async Task<IActionResult> ScheduleApiJob(string apiConfigId, [FromBody] ScheduleConfig scheduleConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                // 验证API配置是否存在
                var apiConfig = await _apiConfigurationService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return NotFound(new { message = "API配置不存在" });
                }

                if (scheduleConfig == null)
                {
                    return BadRequest(new { message = "调度配置不能为空" });
                }

                var jobKey = await _schedulerService.ScheduleApiJobAsync(apiConfigId, scheduleConfig);

                // 更新API配置的调度信息
                apiConfig.Schedule = scheduleConfig;
                apiConfig.UpdatedAt = DateTime.UtcNow;
                await _apiConfigurationService.UpdateAsync(apiConfig);

                return Ok(new { 
                    message = "任务调度成功",
                    jobKey = jobKey,
                    apiConfigId = apiConfigId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调度API任务失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "调度API任务失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 取消调度任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>操作结果</returns>
        [HttpDelete("unschedule/{jobKey}")]
        public async Task<IActionResult> UnscheduleJob(string jobKey)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var result = await _schedulerService.UnscheduleJobAsync(jobKey);
                
                if (result)
                {
                    return Ok(new { message = "取消调度成功", jobKey = jobKey });
                }
                else
                {
                    return NotFound(new { message = "任务不存在或取消失败", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消调度任务失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "取消调度任务失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 暂停任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>操作结果</returns>
        [HttpPost("pause-job/{jobKey}")]
        public async Task<IActionResult> PauseJob(string jobKey)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var result = await _schedulerService.PauseJobAsync(jobKey);
                
                if (result)
                {
                    return Ok(new { message = "暂停任务成功", jobKey = jobKey });
                }
                else
                {
                    return NotFound(new { message = "任务不存在或暂停失败", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停任务失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "暂停任务失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 恢复任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>操作结果</returns>
        [HttpPost("resume-job/{jobKey}")]
        public async Task<IActionResult> ResumeJob(string jobKey)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var result = await _schedulerService.ResumeJobAsync(jobKey);
                
                if (result)
                {
                    return Ok(new { message = "恢复任务成功", jobKey = jobKey });
                }
                else
                {
                    return NotFound(new { message = "任务不存在或恢复失败", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复任务失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "恢复任务失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 立即执行任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>操作结果</returns>
        [HttpPost("trigger/{jobKey}")]
        public async Task<IActionResult> TriggerJob(string jobKey)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var result = await _schedulerService.TriggerJobAsync(jobKey);
                
                if (result)
                {
                    return Ok(new { message = "触发任务执行成功", jobKey = jobKey });
                }
                else
                {
                    return NotFound(new { message = "任务不存在或触发失败", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发任务执行失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "触发任务执行失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新任务调度
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <param name="scheduleConfig">新的调度配置</param>
        /// <returns>操作结果</returns>
        [HttpPut("reschedule/{jobKey}")]
        public async Task<IActionResult> RescheduleJob(string jobKey, [FromBody] ScheduleConfig scheduleConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                if (scheduleConfig == null)
                {
                    return BadRequest(new { message = "调度配置不能为空" });
                }

                var result = await _schedulerService.RescheduleJobAsync(jobKey, scheduleConfig);
                
                if (result)
                {
                    return Ok(new { message = "重新调度任务成功", jobKey = jobKey });
                }
                else
                {
                    return NotFound(new { message = "任务不存在或重新调度失败", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新调度任务失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "重新调度任务失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>任务状态</returns>
        [HttpGet("job-status/{jobKey}")]
        public async Task<IActionResult> GetJobStatus(string jobKey)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var status = await _schedulerService.GetJobStatusAsync(jobKey);
                
                if (status != null)
                {
                    return Ok(status);
                }
                else
                {
                    return NotFound(new { message = "任务不存在", jobKey = jobKey });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务状态失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "获取任务状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有任务状态
        /// </summary>
        /// <returns>任务状态列表</returns>
        [HttpGet("jobs")]
        public async Task<IActionResult> GetAllJobStatus()
        {
            try
            {
                var statuses = await _schedulerService.GetAllJobStatusAsync();
                return Ok(statuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有任务状态失败");
                return StatusCode(500, new { message = "获取所有任务状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取任务执行历史
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <param name="limit">限制数量</param>
        /// <returns>执行历史</returns>
        [HttpGet("job-history/{jobKey}")]
        public async Task<IActionResult> GetJobExecutionHistory(string jobKey, [FromQuery] int limit = 50)
        {
            try
            {
                if (string.IsNullOrEmpty(jobKey))
                {
                    return BadRequest(new { message = "任务键不能为空" });
                }

                var history = await _schedulerService.GetJobExecutionHistoryAsync(jobKey, limit);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务执行历史失败，任务键: {JobKey}", jobKey);
                return StatusCode(500, new { message = "获取任务执行历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取调度器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetSchedulerStatistics()
        {
            try
            {
                var statistics = await _schedulerService.GetSchedulerStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调度器统计信息失败");
                return StatusCode(500, new { message = "获取调度器统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验证Cron表达式
        /// </summary>
        /// <param name="request">验证请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate-cron")]
        public async Task<IActionResult> ValidateCronExpression([FromBody] CronValidationRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.CronExpression))
                {
                    return BadRequest(new { message = "Cron表达式不能为空" });
                }

                var result = await _schedulerService.ValidateCronExpressionAsync(request.CronExpression);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证Cron表达式失败");
                return StatusCode(500, new { message = "验证Cron表达式失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取Cron表达式的下几次执行时间
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>执行时间列表</returns>
        [HttpPost("cron-next-times")]
        public async Task<IActionResult> GetCronNextExecutionTimes([FromBody] CronNextTimesRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.CronExpression))
                {
                    return BadRequest(new { message = "Cron表达式不能为空" });
                }

                var times = await _schedulerService.GetCronNextExecutionTimesAsync(request.CronExpression, request.Count);
                return Ok(new { 
                    cronExpression = request.CronExpression,
                    nextExecutionTimes = times
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Cron表达式执行时间失败");
                return StatusCode(500, new { message = "获取Cron表达式执行时间失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理过期的执行历史
        /// </summary>
        /// <param name="olderThanDays">保留天数</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup-history")]
        public async Task<IActionResult> CleanupExecutionHistory([FromQuery] int olderThanDays = 30)
        {
            try
            {
                var deletedCount = await _schedulerService.CleanupExecutionHistoryAsync(olderThanDays);
                return Ok(new { 
                    message = "清理执行历史完成",
                    deletedCount = deletedCount,
                    olderThanDays = olderThanDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理执行历史失败");
                return StatusCode(500, new { message = "清理执行历史失败", error = ex.Message });
            }
        }
    }

    #region 请求模型

    /// <summary>
    /// Cron验证请求
    /// </summary>
    public class CronValidationRequest
    {
        public string CronExpression { get; set; }
    }

    /// <summary>
    /// Cron下次执行时间请求
    /// </summary>
    public class CronNextTimesRequest
    {
        public string CronExpression { get; set; }
        public int Count { get; set; } = 5;
    }

    #endregion
}
