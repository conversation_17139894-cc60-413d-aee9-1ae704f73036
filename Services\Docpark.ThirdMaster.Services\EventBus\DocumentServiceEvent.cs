﻿using Autofac.Core;
using DocPark.Workflow.Share;
using EventBus.Abstractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.ThirdMaster.Services.EventBus
{
    public class DocumentServiceEvent : IIntegrationEventHandler<DocumentWorkflowMQEventData>
    {
        private readonly ILogger<DocumentServiceEvent> logger;
        private readonly IEnumerable<IEventService> services;
        private readonly IEventBus eventBus;

        public DocumentServiceEvent(ILogger<DocumentServiceEvent> logger, IEnumerable<IEventService> services,IEventBus eventBus)
        {
            this.logger = logger;
            this.services = services;
            this.eventBus = eventBus;
        }
        public async Task Handle(DocumentWorkflowMQEventData eventData)
        {
            var mqContent = Newtonsoft.Json.JsonConvert.DeserializeObject<WorkflowMQContent>(eventData.Message);

            StringBuilder resMsg = new StringBuilder();

            resMsg.AppendLine($"===========================工作流开始==============================================");
            resMsg.AppendLine($"Key:{Guid.NewGuid()}");
            resMsg.AppendLine("[MQ]-消息订阅-订阅消息[Subscribe]日志");
            resMsg.AppendLine($"消息内容:{eventData.Message}");

            string messageType = "Info";

            var logId = $"{mqContent.DocumentID.Replace("-", string.Empty).ToUpper()}-";

            try
            {
                foreach (var service in services)
                {
                    var type = service.GetServiceType();
                    if (type.Code == mqContent.Code)
                    {
                        resMsg.AppendLine(await service.Handle(mqContent));
                    }
                }
            }
            catch (Exception ex)
            {
                messageType = "Error";
                resMsg.AppendLine($"消息处理异常:{ex.Message}");
                resMsg.AppendLine($"{ex.StackTrace}");

                mqContent.TriggerStatus = false;
                mqContent.TriggerMessage = ex.Message;
                mqContent.Data = new Dictionary<string, string>
                {
                    ["ResultState"] = "false",
                    ["ResultMessage"] = ex.Message
                };
                var msg = JsonConvert.SerializeObject(mqContent);
                DocumentServiceMQEventData publishEventData = new DocumentServiceMQEventData(msg);
                eventBus.Publish(publishEventData);
            }
            finally
            {
                resMsg.AppendLine($"===========================结束==============================================");

                if (messageType == "Info")
                    this.logger.LogInformation(resMsg.ToString());
                else
                {
                    this.logger.LogError(resMsg.ToString());
                }
            }
        }
    }
}
