﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 预览页面树结构对象
    /// </summary>
    public class MC_PREVIEW_TREE_DATA_Model
    {
        /// <summary>
        /// 标识ID
        /// </summary>
        public int id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string mastID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string documentId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string fileName { get; set; }
        /// <summary>
        /// 图片地址或base64数据格式
        /// </summary>
        public string imageUrlOrBase64 { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 类型(发票还是pdf或主目录)
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string docTypeID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string archivesKey { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<MC_PREVIEW_TREE_DATA_Model> children { get; set; }
        /// <summary>
        /// 排序索引
        /// </summary>
        public int sortIndex { get; set; }
    }

    public class MC_PREVIEW_TREE_DATA_Comparer : IComparer<MC_PREVIEW_TREE_DATA_Model>
    {
        public int Compare(MC_PREVIEW_TREE_DATA_Model x, MC_PREVIEW_TREE_DATA_Model y)
        {
            return (x.sortIndex.CompareTo(y.sortIndex));
        }
    }
}
