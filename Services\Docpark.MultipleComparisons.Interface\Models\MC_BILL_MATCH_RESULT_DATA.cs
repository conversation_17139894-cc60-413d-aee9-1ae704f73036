﻿using Docpark.HttpClientExtension.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models
{
    /// <summary>
    /// 二次比对-单据发票比对结果数据
    /// </summary>
    [JsonTable("MC_BILL_MATCH_RESULT_DATA")]
    public class MC_BILL_MATCH_RESULT_DATA
    {
        /// <summary>
        /// 比对结果mstId
        /// </summary>
        public string MstId { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string bill_no { get; set; }
        /// <summary>
        /// 单据mstId
        /// </summary>
        public string bill_mstid { get; set; }
        /// <summary>
        /// 影像扫描mstid
        /// </summary>
        public string scan_mstid { get; set; }
        /// <summary>
        /// 发票凭证mstid
        /// </summary>
        public string inv_mstid { get; set; }
        /// <summary>
        /// 匹配结果: 00 匹配失败, 01 无需匹配, 02 匹配成功
        /// </summary>
        public string match_result { get; set; }
        /// <summary>
        /// 是否为自动匹配, 00 不是自动匹配, 01 自动匹配
        /// </summary>
        public string is_auto_match { get; set; }
        /// <summary>
        /// 匹配时间
        /// </summary>
        public string match_time { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
    }
}
