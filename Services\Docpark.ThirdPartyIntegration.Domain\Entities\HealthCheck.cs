using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 健康检查实体
    /// </summary>
    public class HealthCheck
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        [Required]
        public string ApiConfigId { get; set; }

        /// <summary>
        /// API名称
        /// </summary>
        [StringLength(100)]
        public string ApiName { get; set; }

        /// <summary>
        /// 健康状态
        /// </summary>
        [Required]
        public HealthStatus Status { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public string ErrorDetails { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 检查类型
        /// </summary>
        [StringLength(50)]
        public string CheckType { get; set; } = "API";

        /// <summary>
        /// 检查结果详情
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument CheckDetails { get; set; }

        /// <summary>
        /// 性能指标
        /// </summary>
        [BsonRepresentation(BsonType.Document)]
        public BsonDocument PerformanceMetrics { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string[] Tags { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
