﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.IServices
{
    public interface IDocparkHttpClient
    {
        Task<string> GetAsync(string url);

        Task<string> PostAsync(string url, object content);

        Task<string> PostJsonAsync(string url, string apiName, object model, string token = "");

        Task<T> GetAsync<T>(string url);

        Task<T> PostAsync<T>(string url, object content);

        Task<T> DeleteAsync<T>(string url);

        Task<String> UploadFile(String url, KeyValuePair<string, KeyValuePair<string, byte[]>> formData);

        Task<(byte[], System.Net.Http.Headers.MediaTypeHeaderValue, string)> DowLoadFile(string url);
    }
}
