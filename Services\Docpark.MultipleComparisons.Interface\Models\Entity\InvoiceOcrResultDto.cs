﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Docpark.MultipleComparisons.Interface.Models.Entity
{
    public class InvoiceOcrResultDto
    {
        /// <summary>
        /// 文件页内容识别数据集合
        /// </summary>
        public List<IdentifyResult> identify_results { get; set; }
        /// <summary>
        /// OFD文件数字签名数据
        /// </summary>
        public OfdSignatureResult signature_results { get; set; }
    }
    /// <summary>
    /// 响应结果
    /// </summary>
    public class ResponseResult_Ocr
    {
        public ResponseResult_Ocr()
        {
            IsSuccess = false;
            Data = null;
            Message = string.Empty;
            TotalCount = 0;
        }
        /// <summary>
        /// 是在执行成功, true 成功, false 失败
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 返回的执行结果
        /// </summary>
        public InvoiceOcrResultDto Data { get; set; }
        /// <summary>
        /// 返回的消息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 返回的结果数据数
        /// </summary>
        public long TotalCount { get; set; }
    }
    public class InvoiceOcrResultDtos
    {
        /// <summary>
        /// 文件页内容识别数据集合
        /// </summary>
        public List<IdentifyResult> identify_results { get; set; }
        /// <summary>
        /// OFD文件数字签名数据
        /// </summary>
        public OfdSignatureResult signature_results { get; set; }
        /// <summary>
        /// 验真结果
        /// </summary>
        public bool verify_result { get; set; }
    }

    /// <summary>
    /// 发票识别的信息(匹配票小秘的数据结构)
    /// </summary>
    public class IdentifyResult
    {
        public IdentifyResult()
        {
            this.type = "";
            this.orientation = 0;
            this.region = new int[] { 0, 0, 0, 0 };
            this.details = new IdentifyDetails();
        }

        /// <summary>
        /// 发票类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 发票顺时针旋转方向, OFD发票默认为0
        /// </summary>
        public int orientation { get; set; }
        /// <summary>
        /// 单张页面图片区域: 左上点 和 右下点[x1, y1, x2, y2]
        /// </summary>
        public int[] region { get; set; }
        /// <summary>
        /// 发票识别的具体信息
        /// </summary>
        public IdentifyDetails details { get; set; }

    }

    /// <summary>
    /// 发票识别详情信息
    /// </summary>
    public class IdentifyDetails
    {
        #region 识别数据对象-私有对象

        /// <summary>
        /// 印刷发票代码
        /// </summary>
        private string _code = "";

        /// <summary>
        /// 印刷发票号码
        /// </summary>
        private string _number = "";
        /// <summary>
        /// 开票日期
        /// </summary>
        private string _date = "";
        /// <summary>
        /// 税前总金额
        /// </summary>
        private string _pretax_amount = "";
        /// <summary>
        /// 税后总金额
        /// </summary>
        private string _total = "";
        /// <summary>
        /// 税额
        /// </summary>
        private string _tax = "";
        /// <summary>
        /// 校验码
        /// </summary>
        private string _check_code = "";
        /// <summary>
        /// 销售方名称
        /// </summary>
        private string _seller = "";
        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        private string _seller_tax_id = "";
        /// <summary>
        /// 购买方方名称
        /// </summary>
        private string _buyer = "";
        /// <summary>
        /// 购买方纳税人识别号
        /// </summary>
        private string _buyer_tax_id = "";
        /// <summary>
        /// 购买方地址/电话
        /// </summary>
        private string _buyer_addr_tel = "";
        /// <summary>
        /// 购买方银行账号
        /// </summary>
        private string _buyer_bank_account = "";
        /// <summary>
        /// 是否有公司印章（0：没有； 1： 有）
        /// </summary>
        private string _company_seal = "0";
        /// <summary>
        /// 密码区,四行密码,每行以逗号隔开
        /// </summary>
        private string _ciphertext = "";
        /// <summary>
        /// 机器编号
        /// </summary>
        private string _machine_code = "";
        /// <summary>
        /// 收款人
        /// </summary>
        private string _receiptor = "";
        /// <summary>
        /// 复核
        /// </summary>
        private string _reviewer = "";
        /// <summary>
        /// 开票人
        /// </summary>
        private string _issuer = "";
        /// <summary>
        /// 备注
        /// </summary>
        private string _remark = "";
        /// <summary>
        /// 二维码数据内容
        /// </summary>
        private string _qrcode = "";
        /// <summary>
        /// 机打发票代码
        /// </summary>
        private string _code_confirm = "";
        /// <summary>
        /// 机打发票号码
        /// </summary>
        private string _number_confirm = "";
        /// <summary>
        /// 品名，每个以逗号隔开,示例: *餐饮服务*餐饮服务,*酒*白酒
        /// </summary>
        private string _item_names = "";
        /// <summary>
        /// 区块链标记, 0 非区块链发票, 1 区块链发票
        /// </summary>
        private string _block_chain = "0";
        /// <summary>
        /// 销售方地址/电话
        /// </summary>
        private string _seller_addr_tel = "";
        /// <summary>
        /// 销售方银行账号
        /// </summary>
        private string _seller_bank_account = "";
        /// <summary>
        /// 发票消费类型
        /// </summary>
        private string _kind = "";
        /// <summary>
        /// 服务类型
        /// </summary>
        private string _service_name = "";
        /// <summary>
        /// 省
        /// </summary>
        private string _province = "";
        /// <summary>
        /// 市
        /// </summary>
        private string _city = "";
        /// <summary>
        /// 发票是第几联
        /// </summary>
        private string _form_type = "";
        /// <summary>
        /// 发票联次
        /// </summary>
        private string _form_name = "";
        #endregion

        #region OFD识别数据对象-公共对象

        /// <summary>
        /// 印刷发票代码<br/>
        /// 规则说明: 长度12位, 从左至右排列：
        /// 第1位为国家税务局、地方税务局代码，1为国家税务局、2为地方税务局，0为总局。
        /// 第2、3、4、5位为地区代码(地、市级)，以全国行政区域统一代码为准，总局为0000。
        /// 第6、7位为年份代码（例如2004年以04表示）。
        /// 第8位为统一的行业代码，其中，国税行业划分：1工业、2商业、3加工修理修配业、4收购业、5水电业、6其他；地税行业划分：1交通运输业、2建筑业、3金融保险业、4邮电通信业、5文化体育业、6娱乐业、7服务业、8转让无形资产、9销售不动产、0表示其他。
        /// 第9、10、11、12位为细化的发票种类代码，按照保证每份发票编码惟一的原则，由省、自治区、直辖市和计划单列市国家税务局、地方税务局自行编制。
        /// </summary>
        public string code { get => _code; set => _code = value; }

        /// <summary>
        /// 印刷发票号码
        /// </summary>
        public string number { get => _number; set => _number = value; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string date { get => _date; set => _date = value; }
        /// <summary>
        /// 税前金额
        /// </summary>
        public string pretax_amount { get => _pretax_amount; set => _pretax_amount = value; }
        /// <summary>
        /// 价税合计(小写), 即: 总金额
        /// </summary>
        public string total { get => _total; set => _total = value; }
        /// <summary>
        /// 税额
        /// </summary>
        public string tax { get => _tax; set => _tax = value; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string check_code { get => _check_code; set => _check_code = value; }
        /// <summary>
        /// 销售方名称
        /// </summary>
        public string seller { get => _seller; set => _seller = value; }
        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        public string seller_tax_id { get => _seller_tax_id; set => _seller_tax_id = value; }
        /// <summary>
        /// 销售方地址/电话
        /// </summary>
        public string seller_addr_tel { get => _seller_addr_tel; set => _seller_addr_tel = value; }
        /// <summary>
        /// 销售方银行账号
        /// </summary>
        public string seller_bank_account { get => _seller_bank_account; set => _seller_bank_account = value; }
        /// <summary>
        /// 购买方名称
        /// </summary>
        public string buyer { get => _buyer; set => _buyer = value; }
        /// <summary>
        /// 购买方纳税人识别号
        /// </summary>
        public string buyer_tax_id { get => _buyer_tax_id; set => _buyer_tax_id = value; }
        /// <summary>
        /// 购买方地址/电话
        /// </summary>
        public string buyer_addr_tel { get => _buyer_addr_tel; set => _buyer_addr_tel = value; }
        /// <summary>
        /// 购买方银行账号
        /// </summary>
        public string buyer_bank_account { get => _buyer_bank_account; set => _buyer_bank_account = value; }
        /// <summary>
        /// 是否有公司印章（0：没有； 1： 有）
        /// </summary>
        public string company_seal { get => _company_seal; set => _company_seal = value; }
        /// <summary>
        /// 密码区, 四行密码, 每行以逗号隔开
        /// </summary>
        public string ciphertext { get => _ciphertext; set => _ciphertext = value; }
        /// <summary>
        /// 机器编号
        /// </summary>
        public string machine_code { get => _machine_code; set => _machine_code = value; }
        /// <summary>
        /// 收款人
        /// </summary>
        public string receiptor { get => _receiptor; set => _receiptor = value; }
        /// <summary>
        /// 复核
        /// </summary>
        public string reviewer { get => _reviewer; set => _reviewer = value; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string issuer { get => _issuer; set => _issuer = value; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get => _remark; set => _remark = value; }
        /// <summary>
        /// 二维码数据内容
        /// </summary>
        public string qrcode { get => _qrcode; set => _qrcode = value; }
        /// <summary>
        /// 机打发票代码
        /// </summary>
        public string code_confirm { get => _code_confirm; set => _code_confirm = value; }
        /// <summary>
        /// 机打发票号码
        /// </summary>
        public string number_confirm { get => _number_confirm; set => _number_confirm = value; }
        /// <summary>
        /// 品名，每个以逗号隔开,示例: *餐饮服务*餐饮服务,*酒*白酒
        /// </summary>
        public string item_names { get => _item_names; set => _item_names = value; }
        /// <summary>
        /// 区块链标记, 0 非区块链发票, 1 区块链发票
        /// </summary>
        public string block_chain { get => _block_chain; set => _block_chain = value; }
        /// <summary>
        /// 发票消费类型
        /// </summary>
        public string kind { get => _kind; set => _kind = value; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string service_name { get => _service_name; set => _service_name = value; }
        /// <summary>
        /// 省
        /// </summary>
        public string province { get => _province; set => _province = value; }
        /// <summary>
        /// 市
        /// </summary>
        public string city { get => _city; set => _city = value; }
        /// <summary>
        /// 发票是第几联
        /// </summary>
        public string form_type { get => _form_type; set => _form_type = value; }
        /// <summary>
        /// 发票联次
        /// </summary>
        public string form_name { get => _form_name; set => _form_name = value; }
        /// <summary>
        /// 发票明细
        /// </summary>
        public List<ItemInfo> items { get; set; }

        #endregion
    }

    /// <summary>
    /// 发票明细
    /// </summary>
    public class ItemInfo
    {
        /// <summary>
        /// 品名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string tax { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string tax_rate { get; set; }
        /// <summary>
        /// 金额(不含税)
        /// </summary>
        public string total { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string specification { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string unit { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public string quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public string price { get; set; }

    }

    /// <summary>
    /// OFD文件数字签名结果对象
    /// </summary>
    public class OfdSignatureResult
    {
        public OfdSignatureResult()
        {
            HasSignatureValid = false;
            SignatureMsg = "";
            SignatureType = 1;
        }

        /// <summary>
        /// 数字签名是否有效
        /// </summary>
        public bool HasSignatureValid { get; set; }
        /// <summary>
        /// 签名消息
        /// </summary>
        public string SignatureMsg { get; set; }
        /// <summary>
        /// 签名类型: 0 印章, 1 签章(默认值)
        /// </summary>
        public int SignatureType { get; set; }
    }
}
