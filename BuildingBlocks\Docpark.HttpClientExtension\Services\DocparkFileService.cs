﻿using Docpark.HttpClientExtension.Const;
using Docpark.HttpClientExtension.Grpc;
using Docpark.HttpClientExtension.IServices;
using Docpark.HttpClientExtension.IServices.Dto;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Docpark.HttpClientExtension.Services
{
    public class DocparkFileService : IDocparkFileService
    {
        private readonly IConfiguration configuration;
        private readonly IDocparkHttpClient httpClient;
        private readonly IDGrpc grpc;
        private readonly ILogger<DocparkFileService> logger;
        private string BaseAddress
        {
            get
            {
                return grpc.GetStringAsync("App.Api.FTP.Host").Result;
            }
        }
        private string OFDBaseAddress
        {
            get
            {
                return grpc.GetStringAsync("App.Api.OfdSvc.Host").Result;
            }
        }
        public DocparkFileService(IConfiguration configuration, ILogger<DocparkFileService> logger, IDocparkHttpClient httpClient, IDGrpc grpc)
        {
            this.httpClient = httpClient;
            this.grpc = grpc;
            this.configuration = configuration;
            this.logger = logger;

        }
        public async Task<ResultInfo<string>> UploadFile(KeyValuePair<string, byte[]> file)
        {
            var api = ConstApi.Document_Upload_File;

            var formData = new KeyValuePair<string, KeyValuePair<string, byte[]>>(file.Key, new KeyValuePair<string, byte[]>("newfile", file.Value));

            var httpResponseMessage = await httpClient.UploadFile(api.ToUrl(BaseAddress), formData);
            if (String.IsNullOrWhiteSpace(httpResponseMessage))
            {
                return ResultInfo<string>.Fail("服务连接异常！");
            }
            else
            {
                var result = JsonConvert.DeserializeObject<ResultFTPDto>(httpResponseMessage);
                if (result.State)
                {
                    return ResultInfo<string>.Success(result.Data.DocumentId);
                }
                else
                {
                    return ResultInfo<string>.Fail(result.Message);
                }
            }
        }

        public async Task<List<DocumentFileDto>> GetDocuments(List<string> documentIds)
        {
            try
            {
                var api = $"api/FTP/Viwer/documents/url";

                string strResult = await httpClient.PostAsync(api.ToUrl(BaseAddress),documentIds);

                var dicResult = JsonConvert.DeserializeObject<Dictionary<string, object>>(strResult);

                if (dicResult != null && dicResult.ContainsKey("state") && (bool)dicResult["state"])
                {
                    var result = JsonConvert.DeserializeObject<List<DocumentFileDto>>(dicResult["data"].ToString());
                    foreach (var item in result)
                    {
                        var host = string.IsNullOrEmpty(configuration["Domain"])? configuration["IdentityUrl"]: configuration["Domain"];
                        if (item.Url.IndexOf("http") < 0)
                        {
                            item.Url = $"{host.TrimEnd('/')}/{item.Url.TrimStart('/')}";
                        }
                    }
                    return result;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"GetDocuments，documentIds:{string.Join(",",documentIds)}");
            }
            return null;
        }
        public async Task<DocumentFileDto> GetDocument(string docId)
        {
            try
            {
                var api = $"api/ftp/viwer/getdocumenturl/{docId}/1";

                string strResult = await httpClient.GetAsync(api.ToUrl(BaseAddress));

                var dicResult = JsonConvert.DeserializeObject<Dictionary<string, object>>(strResult);

                if (dicResult != null && dicResult.ContainsKey("state") && (bool)dicResult["state"])
                {
                    var result = JsonConvert.DeserializeObject<DocumentFileDto>(dicResult["data"].ToString());
                    var host = await grpc.GetStringAsync("App.Api.Default.Host");
                    if (result.Url.IndexOf("http") < 0)
                    {
                        result.Url = $"{host.TrimEnd('/')}/{result.Url.TrimStart('/')}";
                    }
                    return result;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"GetDocumentURL错误，docId:{docId}");
            }
            return null;
        }
        public async Task<(byte[], MediaTypeHeaderValue, string)> DowLoadFile(string docId)
        {
            var api = $"api/ftp/download/file?documentId={docId}";

            return await httpClient.DowLoadFile(api.ToUrl(BaseAddress));
        }

        public async Task<(byte[], MediaTypeHeaderValue, string)> DowLoadWatermarkFile(string docId)
        {
            var api = $"api/FTP/PdfWatermark/GetPdfWatermarkFile?documentId={docId}";

            return await httpClient.DowLoadFile(api.ToUrl(BaseAddress));
        }

        public async Task<byte[]> DownloadOfdFirst(string docId)
        {
            var api = "/api/OfdService/get_ofd_invoice_img";
            var dic = new Dictionary<string, object>()
            {
                ["documentId"] = docId
            };
            var result = await httpClient.PostAsync(api.ToUrl(OFDBaseAddress), dic);

            var json = JsonConvert.DeserializeObject<Dictionary<string, object>>(result);

            if (json != null && json.ContainsKey("state") && (bool)json["state"])
            {
                var base64s = JsonConvert.DeserializeObject<string[]>(json["data"].ToString());
                var first = base64s.FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(first))
                {
                    return Convert.FromBase64String(first[(first.IndexOf(",") + 1)..]);
                }
            }
            return null;
        }

        public async Task<byte[]> DownloadZipFirst(string docId)
        {
            var api = $"api/ftp/viwer/zip/{docId}/1";

            var result = await httpClient.GetAsync(api.ToUrl(BaseAddress));

            var json = JsonConvert.DeserializeObject<Dictionary<string, object>>(result);

            if (json != null && json.ContainsKey("state") && (bool)json["state"])
            {
                var base64 = json["data"].ToString();
                if (!string.IsNullOrWhiteSpace(base64))
                {
                    return Convert.FromBase64String(base64[(base64.IndexOf(",") + 1)..]);
                }
            }
            return null;
        }



    }

    public class ResultFTPDto
    {
        public bool State { get; set; }

        public string Message { get; set; }

        public int TotalCount { get; set; }

        public DataModel Data { get; set; }

        public class DataModel
        {
            public string DocumentId { get; set; }
        }
    }
}
