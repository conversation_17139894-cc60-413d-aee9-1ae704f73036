﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Docpark.HttpClientExtension.IServices.Dto
{
    public class ExtendedOrganizationUnitDto
    {
        public long? ParentId { get; set; }

        public string ParentShortName { get; set; }

        public string Code { get; set; }

        public string DisplayName { get; set; }

        public string Type { get; set; }

        public string Label { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string No { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        public string ShortName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 是否启用(true:启用;false:不启用;)
        /// </summary>
        public bool IsActive { get; set; }

        public int MemberCount { get; set; }
    }
}
